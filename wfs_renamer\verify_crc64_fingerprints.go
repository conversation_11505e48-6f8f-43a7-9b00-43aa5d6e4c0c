// 验证CRC64指纹索引工具
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type CRC64VerifierConfig struct {
	DatabasePath string
	Verbose      bool
}

type CRC64Verifier struct {
	config   *CRC64VerifierConfig
	db       *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
}

func NewCRC64Verifier(config *CRC64VerifierConfig) (*CRC64Verifier, error) {
	logger := log.New(os.Stdout, "[CRC64Verifier] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	// 创建CRC64表（使用ISO多项式）
	crcTable := crc64.MakeTable(crc64.ISO)

	return &CRC64Verifier{
		config:   config,
		db:       db,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (cv *CRC64Verifier) Verify() error {
	cv.logger.Println("=== Verifying CRC64 Fingerprint Indexes ===")

	// 1. 收集所有文件
	files := cv.collectFiles()

	// 2. 验证每个文件的指纹
	return cv.verifyFingerprints(files)
}

func (cv *CRC64Verifier) collectFiles() map[string]int64 {
	cv.logger.Println("Collecting files from PATH_PRE...")

	files := make(map[string]int64)
	iter := cv.db.NewIterator(nil, nil)
	defer iter.Release()

	PATH_PRE := []byte{0x00, 0x00}
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := cv.bytesToInt64(seqIDBytes)
				files[path] = seqID

				if cv.config.Verbose {
					cv.logger.Printf("File: %s, seqID=%d", path, seqID)
				}
			}
		}
	}

	cv.logger.Printf("Found %d files", len(files))
	return files
}

func (cv *CRC64Verifier) verifyFingerprints(files map[string]int64) error {
	cv.logger.Println("Verifying CRC64 fingerprints...")

	allCorrect := true
	for path, seqID := range files {
		// 计算CRC64指纹
		crc64Value := cv.calculateCRC64(path)
		crc64Bytes := cv.int64ToBytes(int64(crc64Value))

		cv.logger.Printf("File: %s", path)
		cv.logger.Printf("  SeqID: %d", seqID)
		cv.logger.Printf("  Expected CRC64: %d", crc64Value)

		// 检查指纹索引是否存在
		if value, err := cv.db.Get(crc64Bytes, nil); err == nil {
			pointedSeqID := cv.bytesToInt64(value)
			if pointedSeqID == seqID {
				cv.logger.Printf("  ✅ Fingerprint correct: CRC64=%d → seqID=%d", crc64Value, pointedSeqID)
			} else {
				cv.logger.Printf("  ❌ Fingerprint points to wrong seqID: CRC64=%d → seqID=%d (expected %d)", 
					crc64Value, pointedSeqID, seqID)
				allCorrect = false
			}
		} else {
			cv.logger.Printf("  ❌ Fingerprint missing: CRC64=%d", crc64Value)
			allCorrect = false
		}

		// 模拟WFS的getData调用
		cv.simulateGetData(path, seqID)
		cv.logger.Println()
	}

	if allCorrect {
		cv.logger.Println("✅ All CRC64 fingerprints are correct!")
	} else {
		cv.logger.Println("❌ Some CRC64 fingerprints are incorrect!")
	}

	return nil
}

func (cv *CRC64Verifier) simulateGetData(path string, expectedSeqID int64) {
	cv.logger.Printf("  Simulating WFS getData('%s'):", path)

	// 步骤1：计算指纹
	crc64Value := cv.calculateCRC64(path)
	crc64Bytes := cv.int64ToBytes(int64(crc64Value))
	cv.logger.Printf("    Step 1: fingerprint(path) = CRC64=%d", crc64Value)

	// 步骤2：通过指纹获取文件内容ID
	if contentID, err := cv.db.Get(crc64Bytes, nil); err == nil {
		pointedSeqID := cv.bytesToInt64(contentID)
		cv.logger.Printf("    Step 2: cacheGet(fingerprint) = seqID=%d", pointedSeqID)

		// 步骤3：通过文件内容ID获取WfsFileBean
		if wfsFileBean, err := cv.db.Get(contentID, nil); err == nil {
			cv.logger.Printf("    Step 3: cacheGet(contentID) = WfsFileBean (%d bytes)", len(wfsFileBean))
			
			// 这里应该解析WfsFileBean并获取实际文件数据
			// 但由于我们主要关注指纹索引，这里简化处理
			cv.logger.Printf("    ✅ getData would succeed - file content available")
		} else {
			cv.logger.Printf("    ❌ Step 3 failed: WfsFileBean not found")
		}
	} else {
		cv.logger.Printf("    ❌ Step 2 failed: fingerprint not found")
	}
}

func (cv *CRC64Verifier) calculateCRC64(path string) uint64 {
	return crc64.Checksum([]byte(path), cv.crcTable)
}

func (cv *CRC64Verifier) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (cv *CRC64Verifier) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (cv *CRC64Verifier) Close() {
	if cv.db != nil {
		cv.db.Close()
	}
}

func main() {
	config := &CRC64VerifierConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "CRC64 Fingerprint Verifier\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	verifier, err := NewCRC64Verifier(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer verifier.Close()

	if err := verifier.Verify(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
