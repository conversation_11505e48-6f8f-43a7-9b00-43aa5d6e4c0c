// 创建缺失的WfsFileBean工具
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type WfsFileBeanCreatorConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type WfsFileBeanCreator struct {
	config   *WfsFileBeanCreatorConfig
	db       *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
}

func NewWfsFileBeanCreator(config *WfsFileBeanCreatorConfig) (*WfsFileBeanCreator, error) {
	logger := log.New(os.Stdout, "[WfsFileBeanCreator] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	// 创建CRC64表
	crcTable := crc64.MakeTable(crc64.ISO)

	return &WfsFileBeanCreator{
		config:   config,
		db:       db,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (wfc *WfsFileBeanCreator) Create() error {
	wfc.logger.Println("=== Creating Missing WfsFileBean Entries ===")

	// 1. 收集所有文件
	files := wfc.collectFiles()

	// 2. 检查每个文件的WfsFileBean状态
	missingFiles := wfc.checkMissingWfsFileBeans(files)

	// 3. 创建缺失的WfsFileBean
	return wfc.createWfsFileBeans(missingFiles)
}

func (wfc *WfsFileBeanCreator) collectFiles() map[string]int64 {
	wfc.logger.Println("Collecting files from PATH_PRE...")

	files := make(map[string]int64)
	iter := wfc.db.NewIterator(nil, nil)
	defer iter.Release()

	PATH_PRE := []byte{0x00, 0x00}
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := wfc.bytesToInt64(seqIDBytes)
				files[path] = seqID

				if wfc.config.Verbose {
					wfc.logger.Printf("File: %s, seqID=%d", path, seqID)
				}
			}
		}
	}

	wfc.logger.Printf("Found %d files", len(files))
	return files
}

func (wfc *WfsFileBeanCreator) checkMissingWfsFileBeans(files map[string]int64) map[string]int64 {
	wfc.logger.Println("Checking for missing WfsFileBean entries...")

	missingFiles := make(map[string]int64)

	for path, seqID := range files {
		// 计算指纹
		crc64Value := wfc.calculateCRC64(path)
		crc64Bytes := wfc.int64ToBytes(int64(crc64Value))

		// 检查指纹索引是否存在
		if contentID, err := wfc.db.Get(crc64Bytes, nil); err == nil {
			// 检查WfsFileBean是否存在
			if _, err := wfc.db.Get(contentID, nil); err != nil {
				// WfsFileBean缺失
				missingFiles[path] = seqID
				if wfc.config.Verbose {
					wfc.logger.Printf("Missing WfsFileBean: %s (seqID=%d, contentID=%x)", 
						path, seqID, contentID)
				}
			} else {
				if wfc.config.Verbose {
					wfc.logger.Printf("WfsFileBean exists: %s", path)
				}
			}
		} else {
			// 指纹索引也缺失
			missingFiles[path] = seqID
			if wfc.config.Verbose {
				wfc.logger.Printf("Missing fingerprint and WfsFileBean: %s (seqID=%d)", path, seqID)
			}
		}
	}

	wfc.logger.Printf("Found %d files with missing WfsFileBean", len(missingFiles))
	return missingFiles
}

func (wfc *WfsFileBeanCreator) createWfsFileBeans(missingFiles map[string]int64) error {
	wfc.logger.Println("Creating missing WfsFileBean entries...")

	batch := new(leveldb.Batch)
	createdCount := 0

	for path, seqID := range missingFiles {
		// 创建模拟的WfsFileBean
		wfsFileBean := wfc.createMockWfsFileBean(path, seqID)
		seqIDBytes := wfc.int64ToBytes(seqID)

		// 添加WfsFileBean
		batch.Put(seqIDBytes, wfsFileBean)

		// 确保指纹索引存在
		crc64Value := wfc.calculateCRC64(path)
		crc64Bytes := wfc.int64ToBytes(int64(crc64Value))
		batch.Put(crc64Bytes, seqIDBytes)

		createdCount++

		if wfc.config.Verbose {
			wfc.logger.Printf("Creating WfsFileBean: %s → seqID=%d → WfsFileBean", path, seqID)
		}
	}

	if createdCount > 0 {
		if wfc.config.DryRun {
			wfc.logger.Printf("[DRY RUN] Would create %d WfsFileBean entries", createdCount)
		} else {
			if err := wfc.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			wfc.logger.Printf("✅ Created %d WfsFileBean entries", createdCount)
		}
	} else {
		wfc.logger.Println("✅ All WfsFileBean entries already exist")
	}

	return nil
}

func (wfc *WfsFileBeanCreator) createMockWfsFileBean(path string, seqID int64) []byte {
	// 创建一个模拟的WfsFileBean
	// 这里我们创建一个最小的protobuf编码的WfsFileBean
	
	// WfsFileBean字段：
	// 1. Storenode (string) - 存储节点ID
	// 2. Offset (int64) - 文件偏移量
	// 3. Size (int64) - 文件大小
	// 4. CompressType (int32) - 压缩类型
	// 5. Refercount (int32) - 引用计数

	var result []byte

	// Field 1: Storenode (使用seqID作为存储节点)
	storenode := fmt.Sprintf("node_%d", seqID)
	result = append(result, 0x0A) // tag: field 1, wire type 2
	result = append(result, byte(len(storenode)))
	result = append(result, []byte(storenode)...)

	// Field 2: Offset (使用seqID * 1000作为偏移量)
	offset := seqID * 1000
	result = append(result, 0x10) // tag: field 2, wire type 0
	result = append(result, wfc.encodeVarint(offset)...)

	// Field 3: Size (使用固定大小1024)
	size := int64(1024)
	result = append(result, 0x18) // tag: field 3, wire type 0
	result = append(result, wfc.encodeVarint(size)...)

	// Field 4: CompressType (0 = 无压缩)
	compressType := int32(0)
	result = append(result, 0x20) // tag: field 4, wire type 0
	result = append(result, wfc.encodeVarint(int64(compressType))...)

	// Field 5: Refercount (1)
	refercount := int32(1)
	result = append(result, 0x28) // tag: field 5, wire type 0
	result = append(result, wfc.encodeVarint(int64(refercount))...)

	return result
}

func (wfc *WfsFileBeanCreator) encodeVarint(value int64) []byte {
	var result []byte
	uvalue := uint64(value)
	
	for uvalue >= 0x80 {
		result = append(result, byte(uvalue)|0x80)
		uvalue >>= 7
	}
	result = append(result, byte(uvalue))
	
	return result
}

func (wfc *WfsFileBeanCreator) calculateCRC64(path string) uint64 {
	return crc64.Checksum([]byte(path), wfc.crcTable)
}

func (wfc *WfsFileBeanCreator) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wfc *WfsFileBeanCreator) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (wfc *WfsFileBeanCreator) Close() {
	if wfc.db != nil {
		wfc.db.Close()
	}
}

func main() {
	config := &WfsFileBeanCreatorConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WfsFileBean Creator\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	creator, err := NewWfsFileBeanCreator(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer creator.Close()

	if err := creator.Create(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
