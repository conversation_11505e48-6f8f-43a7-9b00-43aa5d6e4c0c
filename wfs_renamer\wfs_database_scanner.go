// WFS数据库全扫描工具 - 查找所有可能的数据
package main

import (
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type DatabaseScannerConfig struct {
	DatabasePath string
	Verbose      bool
	ShowContent  bool
}

type WFSDatabaseScanner struct {
	config *DatabaseScannerConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewWFSDatabaseScanner(config *DatabaseScannerConfig) (*WFSDatabaseScanner, error) {
	logger := log.New(os.Stdout, "[WFSDatabaseScanner] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &WFSDatabaseScanner{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (wds *WFSDatabaseScanner) Scan() error {
	wds.logger.Println("=== WFS Database Full Scan ===")

	iter := wds.db.NewIterator(nil, nil)
	defer iter.Release()

	categories := make(map[string]int)
	totalEntries := 0
	largeEntries := 0

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()
		totalEntries++

		// 分类key
		category := wds.categorizeKey(key)
		categories[category]++

		// 检查大数据条目
		if len(value) > 1000 {
			largeEntries++
			if wds.config.Verbose {
				wds.logger.Printf("Large entry: category=%s, keySize=%d, valueSize=%d", 
					category, len(key), len(value))
				if wds.config.ShowContent {
					wds.logger.Printf("  Key: %s", hex.EncodeToString(key))
					if len(value) > 100 {
						wds.logger.Printf("  Value (first 100 bytes): %s", hex.EncodeToString(value[:100]))
					} else {
						wds.logger.Printf("  Value: %s", hex.EncodeToString(value))
					}
				}
			}
		}

		// 详细输出前20个条目
		if wds.config.Verbose && totalEntries <= 20 {
			wds.logger.Printf("Entry %d: category=%s, keySize=%d, valueSize=%d", 
				totalEntries, category, len(key), len(value))
			if wds.config.ShowContent {
				wds.logger.Printf("  Key: %s", hex.EncodeToString(key))
				if len(value) > 50 {
					wds.logger.Printf("  Value (first 50 bytes): %s", hex.EncodeToString(value[:50]))
				} else {
					wds.logger.Printf("  Value: %s", hex.EncodeToString(value))
				}
			}
		}
	}

	// 输出统计信息
	wds.logger.Printf("\n=== Scan Results ===")
	wds.logger.Printf("Total entries: %d", totalEntries)
	wds.logger.Printf("Large entries (>1KB): %d", largeEntries)
	wds.logger.Printf("\nCategories:")
	for category, count := range categories {
		wds.logger.Printf("  %s: %d entries", category, count)
	}

	// 特别关注可能的文件内容
	if largeEntries == 0 {
		wds.logger.Printf("\n❌ CRITICAL: No large entries found - all file content may be lost!")
	} else {
		wds.logger.Printf("\n✅ Found %d large entries - some file content may still exist", largeEntries)
	}

	return nil
}

func (wds *WFSDatabaseScanner) categorizeKey(key []byte) string {
	if len(key) == 0 {
		return "EMPTY"
	}

	if len(key) >= 2 {
		prefix := fmt.Sprintf("%02x%02x", key[0], key[1])
		switch prefix {
		case "0000":
			return "PATH_PRE"
		case "0100":
			return "PATH_SEQ"
		case "0800":
			return "0x0800"
		}
	}

	// 检查是否是MD5哈希（16字节）
	if len(key) == 16 {
		return "FINGERPRINT"
	}

	// 检查是否是数字key（可能是seqID）
	if len(key) <= 8 && wds.isNumericKey(key) {
		return "NUMERIC_KEY"
	}

	// 其他
	return fmt.Sprintf("OTHER_%02x", key[0])
}

func (wds *WFSDatabaseScanner) isNumericKey(key []byte) bool {
	// 简单检查：如果key的所有字节都是合理的数字范围
	for _, b := range key {
		if b > 127 { // 超出ASCII范围
			return false
		}
	}
	return true
}

func (wds *WFSDatabaseScanner) Close() {
	if wds.db != nil {
		wds.db.Close()
	}
}

func main() {
	config := &DatabaseScannerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.BoolVar(&config.ShowContent, "show-content", false, "Show key/value content in hex")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Database Scanner\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose -show-content\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	scanner, err := NewWFSDatabaseScanner(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer scanner.Close()

	if err := scanner.Scan(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
