// WFS简化分析工具 - 快速诊断网页显示问题
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_SIMPLE = []byte{0x00, 0x00}
	PATH_SEQ_SIMPLE = []byte{0x01, 0x00}
)

type SimpleAnalyzerConfig struct {
	DatabasePath string
	Verbose      bool
}

type WFSSimpleAnalyzer struct {
	config *SimpleAnalyzerConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewWFSSimpleAnalyzer(config *SimpleAnalyzerConfig) (*WFSSimpleAnalyzer, error) {
	logger := log.New(os.Stdout, "[WFSSimpleAnalyzer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &WFSSimpleAnalyzer{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (wsa *WFSSimpleAnalyzer) Analyze() error {
	wsa.logger.Println("=== WFS Simple Analysis ===")

	// 1. 分析PATH_PRE索引
	pathPreCount, pathPreFiles := wsa.analyzePATH_PRE()

	// 2. 分析PATH_SEQ索引
	pathSeqCount, pathSeqFiles := wsa.analyzePATH_SEQ()

	// 3. 对比分析
	wsa.compareIndexes(pathPreFiles, pathSeqFiles)

	// 4. 总结
	wsa.logger.Printf("\n=== Summary ===")
	wsa.logger.Printf("PATH_PRE entries: %d", pathPreCount)
	wsa.logger.Printf("PATH_SEQ entries: %d", pathSeqCount)

	if pathSeqCount == 0 {
		wsa.logger.Printf("❌ CRITICAL: No PATH_SEQ entries found - this explains why web shows no files!")
	} else if pathSeqCount < pathPreCount {
		wsa.logger.Printf("⚠️  WARNING: PATH_SEQ has fewer entries than PATH_PRE")
	} else {
		wsa.logger.Printf("✅ PATH_SEQ entries look normal")
	}

	return nil
}

func (wsa *WFSSimpleAnalyzer) analyzePATH_PRE() (int, map[int64]string) {
	wsa.logger.Println("\n--- PATH_PRE Analysis ---")

	files := make(map[int64]string)
	count := 0

	iter := wsa.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_SIMPLE[0] && key[1] == PATH_PRE_SIMPLE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := wsa.bytesToInt64(seqIDBytes)

				files[seqID] = path
				count++

				if wsa.config.Verbose {
					wsa.logger.Printf("PATH_PRE: seqID=%d, path=%s", seqID, path)
				}
			}
		}
	}

	wsa.logger.Printf("Found %d PATH_PRE entries", count)
	return count, files
}

func (wsa *WFSSimpleAnalyzer) analyzePATH_SEQ() (int, map[int64]string) {
	wsa.logger.Println("\n--- PATH_SEQ Analysis ---")

	files := make(map[int64]string)
	count := 0

	iter := wsa.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_SEQ_SIMPLE[0] && key[1] == PATH_SEQ_SIMPLE[1] {
			if len(key) > 2 {
				seqIDBytes := key[2:]
				seqID := wsa.bytesToInt64(seqIDBytes)

				// 解析WfsPathBean
				path, timestamp, err := wsa.parseWfsPathBean(iter.Value())
				if err != nil {
					wsa.logger.Printf("❌ Failed to parse PATH_SEQ for seqID %d: %v", seqID, err)
					continue
				}

				files[seqID] = path
				count++

				if wsa.config.Verbose {
					wsa.logger.Printf("PATH_SEQ: seqID=%d, path=%s, timestamp=%d", seqID, path, timestamp)
				}
			}
		}
	}

	wsa.logger.Printf("Found %d PATH_SEQ entries", count)
	return count, files
}

func (wsa *WFSSimpleAnalyzer) compareIndexes(pathPreFiles, pathSeqFiles map[int64]string) {
	wsa.logger.Println("\n--- Index Comparison ---")

	// 找出只在PATH_PRE中存在的文件
	onlyInPathPre := make(map[int64]string)
	for seqID, path := range pathPreFiles {
		if _, exists := pathSeqFiles[seqID]; !exists {
			onlyInPathPre[seqID] = path
		}
	}

	// 找出只在PATH_SEQ中存在的文件
	onlyInPathSeq := make(map[int64]string)
	for seqID, path := range pathSeqFiles {
		if _, exists := pathPreFiles[seqID]; !exists {
			onlyInPathSeq[seqID] = path
		}
	}

	// 找出路径不一致的文件
	pathMismatch := make(map[int64][2]string)
	for seqID, prePath := range pathPreFiles {
		if seqPath, exists := pathSeqFiles[seqID]; exists && prePath != seqPath {
			pathMismatch[seqID] = [2]string{prePath, seqPath}
		}
	}

	// 报告结果
	if len(onlyInPathPre) > 0 {
		wsa.logger.Printf("❌ Files only in PATH_PRE (missing PATH_SEQ): %d", len(onlyInPathPre))
		for seqID, path := range onlyInPathPre {
			wsa.logger.Printf("  seqID %d: %s", seqID, path)
		}
	}

	if len(onlyInPathSeq) > 0 {
		wsa.logger.Printf("⚠️  Files only in PATH_SEQ (missing PATH_PRE): %d", len(onlyInPathSeq))
		for seqID, path := range onlyInPathSeq {
			wsa.logger.Printf("  seqID %d: %s", seqID, path)
		}
	}

	if len(pathMismatch) > 0 {
		wsa.logger.Printf("⚠️  Path mismatches: %d", len(pathMismatch))
		for seqID, paths := range pathMismatch {
			wsa.logger.Printf("  seqID %d: PATH_PRE=%s, PATH_SEQ=%s", seqID, paths[0], paths[1])
		}
	}

	if len(onlyInPathPre) == 0 && len(onlyInPathSeq) == 0 && len(pathMismatch) == 0 {
		wsa.logger.Printf("✅ All indexes are consistent")
	}

	// 模拟网页显示
	wsa.logger.Println("\n--- Web Display Simulation ---")
	wsa.logger.Printf("Files that would display on web (based on PATH_SEQ):")
	if len(pathSeqFiles) == 0 {
		wsa.logger.Printf("❌ NO FILES - Web would show empty list!")
	} else {
		for seqID, path := range pathSeqFiles {
			wsa.logger.Printf("  seqID %d: %s", seqID, path)
		}
	}
}

func (wsa *WFSSimpleAnalyzer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wsa *WFSSimpleAnalyzer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wsa *WFSSimpleAnalyzer) Close() {
	if wsa.db != nil {
		wsa.db.Close()
	}
}

func main() {
	config := &SimpleAnalyzerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Simple Analyzer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	analyzer, err := NewWFSSimpleAnalyzer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer analyzer.Close()

	if err := analyzer.Analyze(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
