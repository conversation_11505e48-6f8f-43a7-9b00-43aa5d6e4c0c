// LevelDB Key Path Fixer for WFS System
// 修复WFS系统中LevelDB数据库的key路径问题
// 将错误的全路径key修改为只保留文件名

package main

import (
	"bytes"
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义，与WFS系统保持一致
var (
	PATH_PRE = []byte{0, 0} // WFS系统中的PATH_PRE前缀
)

// 统计信息
type Stats struct {
	TotalKeys     int64 // 总key数量
	ProcessedKeys int64 // 已处理key数量
	FixedKeys     int64 // 已修复key数量
	ErrorKeys     int64 // 错误key数量
	StartTime     time.Time
}

// 修复配置
type FixerConfig struct {
	DBPath       string // LevelDB数据库路径
	BackupPath   string // 备份路径
	WorkerCount  int    // 并发工作线程数
	BatchSize    int    // 批处理大小
	DryRun       bool   // 是否为试运行模式
	EnableBackup bool   // 是否启用备份
}

// 修复器
type KeyFixer struct {
	config *FixerConfig
	db     *leveldb.DB
	stats  *Stats
	logger *log.Logger
}

// 创建新的修复器
func NewKeyFixer(config *FixerConfig) *KeyFixer {
	return &KeyFixer{
		config: config,
		stats: &Stats{
			StartTime: time.Now(),
		},
		logger: log.New(os.Stdout, "[KeyFixer] ", log.LstdFlags),
	}
}

// 打开LevelDB数据库
func (kf *KeyFixer) openDB() error {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     64 * 1024 * 1024, // 64MB
		WriteBuffer:            16 * 1024 * 1024, // 16MB
		ReadOnly:               kf.config.DryRun,
	}

	var err error
	kf.db, err = leveldb.OpenFile(kf.config.DBPath, options)
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}

	kf.logger.Printf("Successfully opened database: %s", kf.config.DBPath)
	return nil
}

// 关闭数据库
func (kf *KeyFixer) closeDB() error {
	if kf.db != nil {
		return kf.db.Close()
	}
	return nil
}

// 创建备份
func (kf *KeyFixer) createBackup() error {
	if !kf.config.EnableBackup {
		kf.logger.Println("Backup disabled, skipping...")
		return nil
	}

	kf.logger.Printf("Creating backup to: %s", kf.config.BackupPath)

	// 创建备份目录
	if err := os.MkdirAll(kf.config.BackupPath, 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %v", err)
	}

	// 复制数据库文件
	return kf.copyDir(kf.config.DBPath, kf.config.BackupPath)
}

// 复制目录
func (kf *KeyFixer) copyDir(src, dst string) error {
	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}

		dstPath := filepath.Join(dst, relPath)

		if info.IsDir() {
			return os.MkdirAll(dstPath, info.Mode())
		}

		return kf.copyFile(path, dstPath)
	})
}

// 复制文件
func (kf *KeyFixer) copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = dstFile.ReadFrom(srcFile)
	return err
}

// 检查key是否需要修复
func (kf *KeyFixer) needsFix(key []byte) (bool, string) {
	// 检查是否是PATH_PRE前缀的key
	if len(key) <= len(PATH_PRE) || !bytes.HasPrefix(key, PATH_PRE) {
		return false, ""
	}

	// 提取路径部分
	pathBytes := key[len(PATH_PRE):]
	pathStr := string(pathBytes)

	// 检查是否包含路径分隔符（表示是全路径）
	if strings.Contains(pathStr, "/") || strings.Contains(pathStr, "\\") {
		// 提取文件名（最后一个路径分隔符后的部分）
		fileName := filepath.Base(pathStr)
		return true, fileName
	}

	return false, ""
}

// 修复单个key
func (kf *KeyFixer) fixKey(oldKey []byte, newFileName string) error {
	// 构造新的key
	newKey := append(PATH_PRE, []byte(newFileName)...)

	// 获取原始值
	value, err := kf.db.Get(oldKey, nil)
	if err != nil {
		return fmt.Errorf("failed to get value for key: %v", err)
	}

	if kf.config.DryRun {
		// 检查目标key是否已存在
		targetExists, _ := kf.db.Has(newKey, nil)
		if targetExists {
			kf.logger.Printf("DRY RUN: Would fix key: %s -> %s (target exists, would delete old key only)",
				string(oldKey[len(PATH_PRE):]), newFileName)
		} else {
			kf.logger.Printf("DRY RUN: Would fix key: %s -> %s",
				string(oldKey[len(PATH_PRE):]), newFileName)
		}
		return nil
	}

	// 创建批处理操作
	batch := new(leveldb.Batch)

	// 检查目标key是否已存在
	targetExists, err := kf.db.Has(newKey, nil)
	if err != nil {
		return fmt.Errorf("failed to check target key existence: %v", err)
	}

	if targetExists {
		// 目标key已存在，只删除原始key
		batch.Delete(oldKey)
		kf.logger.Printf("Fixed key (target exists): %s -> %s (deleted old key only)",
			string(oldKey[len(PATH_PRE):]), newFileName)
	} else {
		// 目标key不存在，执行正常的重命名操作
		batch.Put(newKey, value)
		batch.Delete(oldKey)
		kf.logger.Printf("Fixed key: %s -> %s",
			string(oldKey[len(PATH_PRE):]), newFileName)
	}

	// 执行批处理
	if err := kf.db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
		return fmt.Errorf("failed to write batch: %v", err)
	}

	return nil
}

// 工作线程处理函数
func (kf *KeyFixer) worker(ctx context.Context, keysChan <-chan []byte, wg *sync.WaitGroup) {
	defer wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case key, ok := <-keysChan:
			if !ok {
				return
			}

			atomic.AddInt64(&kf.stats.ProcessedKeys, 1)

			if needsFix, newFileName := kf.needsFix(key); needsFix {
				if err := kf.fixKey(key, newFileName); err != nil {
					kf.logger.Printf("Error fixing key %s: %v", string(key), err)
					atomic.AddInt64(&kf.stats.ErrorKeys, 1)
				} else {
					atomic.AddInt64(&kf.stats.FixedKeys, 1)
				}
			}
		}
	}
}

// 打印统计信息
func (kf *KeyFixer) printStats() {
	elapsed := time.Since(kf.stats.StartTime)
	processed := atomic.LoadInt64(&kf.stats.ProcessedKeys)
	fixed := atomic.LoadInt64(&kf.stats.FixedKeys)
	errors := atomic.LoadInt64(&kf.stats.ErrorKeys)
	total := atomic.LoadInt64(&kf.stats.TotalKeys)

	kf.logger.Printf("Progress: %d/%d processed, %d fixed, %d errors, elapsed: %v",
		processed, total, fixed, errors, elapsed.Truncate(time.Second))
}

// 启动修复过程
func (kf *KeyFixer) Fix() error {
	kf.logger.Println("Starting LevelDB key path fix process...")

	// 打开数据库
	if err := kf.openDB(); err != nil {
		return err
	}
	defer kf.closeDB()

	// 创建备份
	if err := kf.createBackup(); err != nil {
		return fmt.Errorf("backup failed: %v", err)
	}

	// 创建上下文和取消函数
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建工作线程
	keysChan := make(chan []byte, kf.config.BatchSize)
	var wg sync.WaitGroup

	for i := 0; i < kf.config.WorkerCount; i++ {
		wg.Add(1)
		go kf.worker(ctx, keysChan, &wg)
	}

	// 启动统计信息打印协程
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				kf.printStats()
			}
		}
	}()

	// 遍历所有key
	iter := kf.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	kf.logger.Println("Scanning database for keys to fix...")

	for iter.Next() {
		key := make([]byte, len(iter.Key()))
		copy(key, iter.Key())

		atomic.AddInt64(&kf.stats.TotalKeys, 1)
		keysChan <- key
	}

	// 关闭通道并等待工作线程完成
	close(keysChan)
	wg.Wait()

	// 检查迭代器错误
	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	// 打印最终统计信息
	kf.printStats()
	kf.logger.Println("Fix process completed successfully!")

	return nil
}

// 主函数
func main() {
	// 解析命令行参数
	if len(os.Args) < 2 {
		fmt.Println("Usage: leveldb_key_fixer <db_path> [options]")
		fmt.Println("Options:")
		fmt.Println("  -backup <path>    : Backup directory path")
		fmt.Println("  -workers <num>    : Number of worker threads (default: CPU count)")
		fmt.Println("  -batch <size>     : Batch size (default: 1000)")
		fmt.Println("  -dry-run          : Dry run mode (no actual changes)")
		fmt.Println("  -no-backup        : Disable backup")
		os.Exit(1)
	}

	config := &FixerConfig{
		DBPath:       os.Args[1],
		BackupPath:   os.Args[1] + "_backup_" + time.Now().Format("20060102_150405"),
		WorkerCount:  runtime.NumCPU(),
		BatchSize:    1000,
		DryRun:       false,
		EnableBackup: true,
	}

	// 解析其他参数
	for i := 2; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-backup":
			if i+1 < len(os.Args) {
				config.BackupPath = os.Args[i+1]
				i++
			}
		case "-workers":
			if i+1 < len(os.Args) {
				fmt.Sscanf(os.Args[i+1], "%d", &config.WorkerCount)
				i++
			}
		case "-batch":
			if i+1 < len(os.Args) {
				fmt.Sscanf(os.Args[i+1], "%d", &config.BatchSize)
				i++
			}
		case "-dry-run":
			config.DryRun = true
		case "-no-backup":
			config.EnableBackup = false
		}
	}

	// 创建并运行修复器
	fixer := NewKeyFixer(config)
	if err := fixer.Fix(); err != nil {
		log.Fatalf("Fix failed: %v", err)
	}
}
