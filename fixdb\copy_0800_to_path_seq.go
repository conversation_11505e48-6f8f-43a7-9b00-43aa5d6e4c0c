// Copy 0x0800 Index to PATH_SEQ
// 将0x0800索引复制到PATH_SEQ索引

package main

import (
	"encoding/binary"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_SEQ   = []byte{0x01, 0x00}
	INDEX_0800 = []byte{0x08, 0x00}
)

// 复制工具
type IndexCopier struct {
	db     *leveldb.DB
	logger *log.Logger
	dryRun bool
}

// 创建复制工具
func NewIndexCopier(dbPath string, dryRun bool) (*IndexCopier, error) {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     64 * 1024 * 1024,
		WriteBuffer:            16 * 1024 * 1024,
		ReadOnly:               dryRun,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}

	return &IndexCopier{
		db:     db,
		logger: log.New(os.Stdout, "[IndexCopier] ", log.LstdFlags),
		dryRun: dryRun,
	}, nil
}

// 关闭工具
func (ic *IndexCopier) Close() error {
	if ic.db != nil {
		return ic.db.Close()
	}
	return nil
}

// 解析protobuf格式的WfsPathBean
func (ic *IndexCopier) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 复制0x0800索引到PATH_SEQ
func (ic *IndexCopier) CopyIndex() error {
	ic.logger.Println("=== Copying 0x0800 Index to PATH_SEQ ===")

	// 遍历0x0800索引
	iter := ic.db.NewIterator(levelutil.BytesPrefix(INDEX_0800), nil)
	defer iter.Release()

	copiedCount := 0
	batch := new(leveldb.Batch)

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 解析0x0800 key中的seqID
		if len(key) < len(INDEX_0800)+8 {
			continue
		}

		// 从key的最后8字节提取seqID
		seqIDBytes := key[len(key)-8:]
		seqID := int64(binary.BigEndian.Uint64(seqIDBytes))

		// 解析WfsPathBean
		path, timestamp, err := ic.parseWfsPathBean(value)
		if err != nil {
			ic.logger.Printf("Failed to parse 0x0800 entry for seqID %d: %v", seqID, err)
			continue
		}

		// 构造PATH_SEQ key
		pathSeqKey := append(PATH_SEQ, seqIDBytes...)

		if ic.dryRun {
			ic.logger.Printf("DRY RUN: Would copy seqID %d: %s (timestamp: %d)", seqID, path, timestamp)
		} else {
			// 检查PATH_SEQ条目是否已存在
			if exists, err := ic.db.Has(pathSeqKey, nil); err == nil && exists {
				ic.logger.Printf("PATH_SEQ already exists for seqID %d (%s), skipping", seqID, path)
				continue
			}

			// 复制到PATH_SEQ
			batch.Put(pathSeqKey, value)
			ic.logger.Printf("Copied seqID %d: %s (timestamp: %d)", seqID, path, timestamp)
		}

		copiedCount++

		// 每100个条目执行一次批处理
		if !ic.dryRun && copiedCount%100 == 0 {
			if err := ic.db.Write(batch, &opt.WriteOptions{Sync: false}); err != nil {
				ic.logger.Printf("Error writing batch: %v", err)
				return err
			}
			batch.Reset()
		}
	}

	// 执行剩余的批处理
	if !ic.dryRun && copiedCount > 0 {
		if err := ic.db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
			ic.logger.Printf("Error writing final batch: %v", err)
			return err
		}
	}

	// 检查迭代器错误
	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	ic.logger.Printf("Copy completed: %d entries processed", copiedCount)

	if ic.dryRun {
		ic.logger.Println("DRY RUN completed - use -copy to perform actual copy")
	} else if copiedCount > 0 {
		ic.logger.Println("✅ Copy successful!")
		ic.logger.Println("💡 PATH_SEQ index has been rebuilt from 0x0800 index")
		ic.logger.Println("💡 Restart WFS service and check web interface")
	} else {
		ic.logger.Println("✅ No copy needed - PATH_SEQ index is already complete")
	}

	return nil
}

// 验证复制结果
func (ic *IndexCopier) Verify() error {
	ic.logger.Println("=== Verifying Copy Results ===")

	// 检查PATH_SEQ索引
	pathSeqIter := ic.db.NewIterator(levelutil.BytesPrefix(PATH_SEQ), nil)
	defer pathSeqIter.Release()

	pathSeqCount := 0
	for pathSeqIter.Next() {
		key := pathSeqIter.Key()
		value := pathSeqIter.Value()

		if len(key) <= len(PATH_SEQ) {
			continue
		}

		seqIDBytes := key[len(PATH_SEQ):]
		seqID := int64(binary.BigEndian.Uint64(seqIDBytes))

		path, timestamp, err := ic.parseWfsPathBean(value)
		if err != nil {
			ic.logger.Printf("❌ PATH_SEQ parse error for seqID %d: %v", seqID, err)
			continue
		}

		pathSeqCount++
		ic.logger.Printf("✅ PATH_SEQ[%d]: seqID %d -> %s (timestamp: %d)", pathSeqCount, seqID, path, timestamp)
	}

	// 检查0x0800索引
	index0800Iter := ic.db.NewIterator(levelutil.BytesPrefix(INDEX_0800), nil)
	defer index0800Iter.Release()

	index0800Count := 0
	for index0800Iter.Next() {
		index0800Count++
	}

	ic.logger.Printf("Verification Summary:")
	ic.logger.Printf("  PATH_SEQ entries: %d", pathSeqCount)
	ic.logger.Printf("  0x0800 entries: %d", index0800Count)

	if pathSeqCount == index0800Count && pathSeqCount > 0 {
		ic.logger.Println("✅ Verification successful - indexes are consistent!")
	} else if pathSeqCount == 0 {
		ic.logger.Println("❌ Verification failed - PATH_SEQ index is empty!")
	} else {
		ic.logger.Printf("⚠️  Verification warning - entry counts don't match")
	}

	return nil
}

// 主函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Copy 0x0800 Index to PATH_SEQ")
		fmt.Println("Usage: copy_0800_to_path_seq <db_path> [options]")
		fmt.Println("")
		fmt.Println("Options:")
		fmt.Println("  -dry-run    : Preview copy without making changes")
		fmt.Println("  -copy       : Perform actual copy")
		fmt.Println("  -verify     : Verify copy results")
		fmt.Println("")
		fmt.Println("Examples:")
		fmt.Println("  copy_0800_to_path_seq db_path -dry-run")
		fmt.Println("  copy_0800_to_path_seq db_path -copy")
		fmt.Println("  copy_0800_to_path_seq db_path -verify")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	dryRun := false
	performCopy := false
	verifyOnly := false

	// 解析参数
	for i := 2; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-dry-run":
			dryRun = true
		case "-copy":
			performCopy = true
		case "-verify":
			verifyOnly = true
		}
	}

	// 默认为dry-run模式
	if !dryRun && !performCopy && !verifyOnly {
		dryRun = true
	}

	// 创建复制工具
	copier, err := NewIndexCopier(dbPath, dryRun || verifyOnly)
	if err != nil {
		log.Fatalf("Failed to create copier: %v", err)
	}
	defer copier.Close()

	// 执行操作
	if verifyOnly {
		if err := copier.Verify(); err != nil {
			log.Fatalf("Verification failed: %v", err)
		}
	} else {
		if err := copier.CopyIndex(); err != nil {
			log.Fatalf("Copy failed: %v", err)
		}

		// 复制后验证
		if !dryRun {
			if err := copier.Verify(); err != nil {
				log.Fatalf("Post-copy verification failed: %v", err)
			}
		}
	}
}
