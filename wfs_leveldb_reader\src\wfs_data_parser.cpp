// WFS数据解析器实现

#include "wfs_data_parser.hpp"
#include <algorithm>

namespace wfs {

WfsPathBeanResult WfsDataParser::parse_wfs_path_bean(const std::vector<uint8_t>& data) {
    WfsPathBean bean;
    size_t pos = 0;
    
    while (pos < data.size()) {
        // 读取tag
        auto [tag, tag_len] = read_varint(data.data(), data.size(), pos);
        if (tag_len == 0) {
            break;
        }
        
        uint32_t field_num = tag >> 3;
        uint32_t wire_type = tag & 0x7;
        
        switch (field_num) {
        case 1: // Path字段
            if (wire_type == 2) { // length-delimited
                auto [path_str, path_len] = read_length_delimited(data.data(), data.size(), pos);
                if (path_len == 0) {
                    return WfsPathBeanResult(ErrorCode::PARSE_ERROR, "Invalid path field");
                }
                bean.path = path_str;
            }
            break;
            
        case 2: // Timestamp字段
            if (wire_type == 0) { // varint
                auto [timestamp, ts_len] = read_signed_varint(data.data(), data.size(), pos);
                if (ts_len == 0) {
                    return WfsPathBeanResult(ErrorCode::PARSE_ERROR, "Invalid timestamp field");
                }
                bean.timestamp = timestamp;
            }
            break;
            
        default:
            // 跳过未知字段
            switch (wire_type) {
            case 0: { // varint
                auto [value, len] = read_varint(data.data(), data.size(), pos);
                if (len == 0) {
                    return WfsPathBeanResult(ErrorCode::PARSE_ERROR, "Invalid varint field");
                }
                break;
            }
            case 2: { // length-delimited
                auto [str, len] = read_length_delimited(data.data(), data.size(), pos);
                if (len == 0) {
                    return WfsPathBeanResult(ErrorCode::PARSE_ERROR, "Invalid length-delimited field");
                }
                break;
            }
            default:
                return WfsPathBeanResult(ErrorCode::PARSE_ERROR, "Unsupported wire type");
            }
            break;
        }
    }
    
    return WfsPathBeanResult(bean);
}

std::vector<uint8_t> WfsDataParser::encode_wfs_path_bean(const WfsPathBean& bean) {
    std::vector<uint8_t> result;
    
    // 编码Path字段 (field 1, wire type 2)
    if (!bean.path.empty()) {
        uint32_t tag1 = (1 << 3) | 2;
        auto tag_bytes = encode_varint(tag1);
        result.insert(result.end(), tag_bytes.begin(), tag_bytes.end());
        
        auto length_bytes = encode_varint(bean.path.length());
        result.insert(result.end(), length_bytes.begin(), length_bytes.end());
        
        result.insert(result.end(), bean.path.begin(), bean.path.end());
    }
    
    // 编码Timestamp字段 (field 2, wire type 0)
    if (bean.timestamp != 0) {
        uint32_t tag2 = (2 << 3) | 0;
        auto tag_bytes = encode_varint(tag2);
        result.insert(result.end(), tag_bytes.begin(), tag_bytes.end());
        
        // 编码有符号整数（使用ZigZag编码）
        uint64_t zigzag = (bean.timestamp << 1) ^ (bean.timestamp >> 63);
        auto timestamp_bytes = encode_varint(zigzag);
        result.insert(result.end(), timestamp_bytes.begin(), timestamp_bytes.end());
    }
    
    return result;
}

std::pair<uint64_t, size_t> WfsDataParser::parse_varint(const uint8_t* data, size_t len) {
    size_t pos = 0;
    return read_varint(data, len, pos);
}

std::vector<uint8_t> WfsDataParser::encode_varint(uint64_t value) {
    std::vector<uint8_t> result;
    
    while (value >= 0x80) {
        result.push_back(static_cast<uint8_t>(value | 0x80));
        value >>= 7;
    }
    result.push_back(static_cast<uint8_t>(value));
    
    return result;
}

std::pair<uint64_t, size_t> WfsDataParser::read_varint(const uint8_t* data, size_t len, size_t& pos) {
    uint64_t result = 0;
    size_t shift = 0;
    size_t start_pos = pos;
    
    while (pos < len && shift < 64) {
        uint8_t byte = data[pos++];
        result |= static_cast<uint64_t>(byte & 0x7F) << shift;
        
        if ((byte & 0x80) == 0) {
            return {result, pos - start_pos};
        }
        
        shift += 7;
    }
    
    // 解析失败，恢复位置
    pos = start_pos;
    return {0, 0};
}

std::pair<int64_t, size_t> WfsDataParser::read_signed_varint(const uint8_t* data, size_t len, size_t& pos) {
    auto [zigzag, varint_len] = read_varint(data, len, pos);
    if (varint_len == 0) {
        return {0, 0};
    }
    
    // ZigZag解码
    int64_t result = (zigzag >> 1) ^ (-(zigzag & 1));
    return {result, varint_len};
}

std::pair<std::string, size_t> WfsDataParser::read_length_delimited(const uint8_t* data, size_t len, size_t& pos) {
    auto [length, length_len] = read_varint(data, len, pos);
    if (length_len == 0 || pos + length > len) {
        return {"", 0};
    }
    
    std::string result(reinterpret_cast<const char*>(data + pos), length);
    pos += length;
    
    return {result, length_len + length};
}

std::unique_ptr<WfsDataParser_i> create_wfs_data_parser() {
    return std::make_unique<WfsDataParser>();
}

} // namespace wfs
