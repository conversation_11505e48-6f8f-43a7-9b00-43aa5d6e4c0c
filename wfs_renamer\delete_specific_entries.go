// 删除特定错误条目工具
package main

import (
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type SpecificEntryDeleterConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type SpecificEntryDeleter struct {
	config *SpecificEntryDeleterConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewSpecificEntryDeleter(config *SpecificEntryDeleterConfig) (*SpecificEntryDeleter, error) {
	logger := log.New(os.Stdout, "[SpecificEntryDeleter] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	return &SpecificEntryDeleter{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (sed *SpecificEntryDeleter) Delete() error {
	sed.logger.Println("=== Deleting Specific Wrong Entries ===")

	// 定义要删除的特定条目
	wrongEntryKeys := []string{
		"0000000000000001",
		"0000000000000002", 
		"0000000000000003",
		"0000000000000004",
		"0000000000000005",
	}

	return sed.deleteSpecificEntries(wrongEntryKeys)
}

func (sed *SpecificEntryDeleter) deleteSpecificEntries(keyHexList []string) error {
	sed.logger.Printf("Attempting to delete %d specific entries...", len(keyHexList))

	batch := new(leveldb.Batch)
	deletedCount := 0

	for _, keyHex := range keyHexList {
		key, err := hex.DecodeString(keyHex)
		if err != nil {
			sed.logger.Printf("Warning: Failed to decode key %s: %v", keyHex, err)
			continue
		}

		// 检查条目是否存在
		if value, err := sed.db.Get(key, nil); err == nil {
			batch.Delete(key)
			deletedCount++

			if sed.config.Verbose {
				sed.logger.Printf("Will delete: %s → %s", keyHex, hex.EncodeToString(value))
			}
		} else {
			if sed.config.Verbose {
				sed.logger.Printf("Entry not found: %s", keyHex)
			}
		}
	}

	if deletedCount > 0 {
		if sed.config.DryRun {
			sed.logger.Printf("[DRY RUN] Would delete %d entries", deletedCount)
		} else {
			if err := sed.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			sed.logger.Printf("✅ Deleted %d entries", deletedCount)
		}
	} else {
		sed.logger.Println("✅ No entries found to delete")
	}

	return nil
}

func (sed *SpecificEntryDeleter) Close() {
	if sed.db != nil {
		sed.db.Close()
	}
}

func main() {
	config := &SpecificEntryDeleterConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Specific Entry Deleter\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	deleter, err := NewSpecificEntryDeleter(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer deleter.Close()

	if err := deleter.Delete(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
