// WFS Thrift Interface Definition
// Copyright (c) 2023, donnie <<EMAIL>>

namespace go stub

// Error structure
struct WfsError {
    1: optional i32 code,
    2: optional string info
}

// Generic acknowledgment response
struct WfsAck {
    1: required bool ok,
    2: optional WfsError error
}

// Request structure for path-based operations
struct WfsReq {
    1: optional string path
}

// Authentication structure
struct WfsAuth {
    1: optional string name,
    2: optional string pwd
}

// Data response structure
struct WfsData {
    1: optional binary data
}

// File structure for upload operations
struct WfsFile {
    1: required binary data,
    2: required string name,
    3: optional i8 compress
}

// File existence response structure
struct WfsExist {
    1: required bool exists,
    2: optional i64 size
}

// Main WFS service interface
service WfsIface {
    // Append file data
    WfsAck Append(1: WfsFile file),
    
    // Delete file by path
    WfsAck Delete(1: string path),
    
    // Rename file
    WfsAck Rename(1: string path, 2: string newpath),
    
    // Authenticate user
    WfsAck Auth(1: WfsAuth wa),
    
    // Get file data by path
    WfsData Get(1: string path),
    
    // Check if file exists and get its size
    WfsExist Exist(1: string path),
    
    // Ping service
    i8 Ping()
}
