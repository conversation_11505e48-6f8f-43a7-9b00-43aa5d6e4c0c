// 使用CRC64算法修复指纹索引工具
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type CRC64FingerprintFixerConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type CRC64FingerprintFixer struct {
	config *CRC64FingerprintFixerConfig
	db     *leveldb.DB
	logger *log.Logger
	crcTable *crc64.Table
}

func NewCRC64FingerprintFixer(config *CRC64FingerprintFixerConfig) (*CRC64FingerprintFixer, error) {
	logger := log.New(os.Stdout, "[CRC64FingerprintFixer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	// 创建CRC64表（使用ISO多项式，与WFS一致）
	crcTable := crc64.MakeTable(crc64.ISO)

	return &CRC64FingerprintFixer{
		config:   config,
		db:       db,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (cff *CRC64FingerprintFixer) Fix() error {
	cff.logger.Println("=== Fixing Fingerprint Indexes with CRC64 ===")

	// 1. 收集当前数据库的文件
	currentFiles := cff.collectCurrentFiles()

	// 2. 收集当前的指纹索引
	currentFingerprints := cff.collectCurrentFingerprints()

	// 3. 修复指纹索引
	return cff.fixFingerprints(currentFiles, currentFingerprints)
}

func (cff *CRC64FingerprintFixer) collectCurrentFiles() map[string][]byte {
	cff.logger.Println("Collecting current files...")

	files := make(map[string][]byte)
	iter := cff.db.NewIterator(nil, nil)
	defer iter.Release()

	PATH_PRE := []byte{0x00, 0x00}
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				contentID := make([]byte, len(iter.Value()))
				copy(contentID, iter.Value())
				files[path] = contentID

				if cff.config.Verbose {
					cff.logger.Printf("Current file: %s → seqID %d", path, cff.bytesToInt64(contentID))
				}
			}
		}
	}

	cff.logger.Printf("Found %d current files", len(files))
	return files
}

func (cff *CRC64FingerprintFixer) collectCurrentFingerprints() map[string][]byte {
	cff.logger.Println("Collecting current fingerprints...")

	fingerprints := make(map[string][]byte)
	iter := cff.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if cff.isKnownIndex(key) {
			continue
		}

		// CRC64指纹索引：key长度8字节，value长度8字节
		if len(key) == 8 && len(value) == 8 {
			keyStr := string(key)
			contentID := make([]byte, len(value))
			copy(contentID, value)
			fingerprints[keyStr] = contentID
			count++

			if cff.config.Verbose {
				cff.logger.Printf("Current fingerprint: CRC64=%d → seqID=%d", 
					cff.bytesToInt64(key), cff.bytesToInt64(contentID))
			}
		}
	}

	cff.logger.Printf("Found %d current fingerprints", count)
	return fingerprints
}

func (cff *CRC64FingerprintFixer) fixFingerprints(currentFiles map[string][]byte, currentFingerprints map[string][]byte) error {
	cff.logger.Println("Fixing fingerprint indexes with CRC64...")

	batch := new(leveldb.Batch)
	fixedCount := 0

	for path, seqID := range currentFiles {
		// 计算CRC64指纹
		crc64Value := cff.calculateCRC64(path)
		crc64Bytes := cff.int64ToBytes(int64(crc64Value))
		crc64Key := string(crc64Bytes)

		// 检查是否已经存在正确的指纹
		if existingSeqID, exists := currentFingerprints[crc64Key]; exists {
			if cff.bytesToInt64(existingSeqID) == cff.bytesToInt64(seqID) {
				if cff.config.Verbose {
					cff.logger.Printf("Fingerprint already correct: %s → CRC64=%d", path, crc64Value)
				}
				continue
			}
		}

		// 删除旧的错误指纹
		for fingerprintKey, fpSeqID := range currentFingerprints {
			if cff.bytesToInt64(fpSeqID) == cff.bytesToInt64(seqID) {
				batch.Delete([]byte(fingerprintKey))
				if cff.config.Verbose {
					oldCRC64 := cff.bytesToInt64([]byte(fingerprintKey))
					cff.logger.Printf("Deleting old fingerprint: %s → CRC64=%d", path, oldCRC64)
				}
				break
			}
		}

		// 添加新的正确指纹
		batch.Put(crc64Bytes, seqID)
		fixedCount++

		if cff.config.Verbose {
			cff.logger.Printf("Adding correct fingerprint: %s → CRC64=%d → seqID=%d", 
				path, crc64Value, cff.bytesToInt64(seqID))
		}
	}

	if fixedCount > 0 {
		if cff.config.DryRun {
			cff.logger.Printf("[DRY RUN] Would fix %d fingerprint indexes", fixedCount)
		} else {
			if err := cff.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			cff.logger.Printf("✅ Fixed %d fingerprint indexes", fixedCount)
		}
	} else {
		cff.logger.Println("✅ All fingerprint indexes are already correct")
	}

	return nil
}

func (cff *CRC64FingerprintFixer) calculateCRC64(path string) uint64 {
	// 使用CRC64-ISO算法计算指纹，与WFS一致
	return crc64.Checksum([]byte(path), cff.crcTable)
}

func (cff *CRC64FingerprintFixer) isKnownIndex(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
		{0x06, 0x00}, // 其他已知前缀
		{0x07, 0x00},
		{0x09, 0x00},
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (cff *CRC64FingerprintFixer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (cff *CRC64FingerprintFixer) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (cff *CRC64FingerprintFixer) Close() {
	if cff.db != nil {
		cff.db.Close()
	}
}

func main() {
	config := &CRC64FingerprintFixerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "CRC64 Fingerprint Fixer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	fixer, err := NewCRC64FingerprintFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
