# WFS LevelDB Reader
# C++程序读取WFS LevelDB数据并提取文件内容

cmake_minimum_required(VERSION 3.20)
project(wfs_leveldb_reader)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器选项
if(MSVC)
    add_compile_options(/utf-8)
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
endif()

# 设置vcpkg路径
set(CMAKE_TOOLCHAIN_FILE "C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake")

# 查找依赖包
find_package(leveldb CONFIG REQUIRED)
find_package(fmt CONFIG REQUIRED)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)

# 源文件
set(SOURCES
    src/main.cpp
    src/leveldb_reader.cpp
    src/wfs_data_parser.cpp
    src/file_extractor.cpp
)

# 头文件
set(HEADERS
    include/leveldb_reader.hpp
    include/wfs_data_parser.hpp
    include/file_extractor.hpp
    include/datatype.hpp
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
    PRIVATE
    leveldb::leveldb
    fmt::fmt
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_SOURCE_DIR}/redist_desk
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_SOURCE_DIR}/redist_desk
)
