// 清理错误条目工具
package main

import (
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type WrongEntryCleanerConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type WrongEntryCleaner struct {
	config *WrongEntryCleanerConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewWrongEntryCleaner(config *WrongEntryCleanerConfig) (*WrongEntryCleaner, error) {
	logger := log.New(os.Stdout, "[WrongEntryCleaner] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	return &WrongEntryCleaner{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (wec *WrongEntryCleaner) Clean() error {
	wec.logger.Println("=== Cleaning Wrong Entries ===")

	// 收集所有错误的条目
	wrongEntries := wec.collectWrongEntries()

	// 删除这些条目
	return wec.deleteWrongEntries(wrongEntries)
}

func (wec *WrongEntryCleaner) collectWrongEntries() [][]byte {
	wec.logger.Println("Collecting wrong entries...")

	var wrongEntries [][]byte
	iter := wec.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 检查是否是错误的条目
		if wec.isWrongEntry(key, value) {
			keyBytes := make([]byte, len(key))
			copy(keyBytes, key)
			wrongEntries = append(wrongEntries, keyBytes)

			if wec.config.Verbose {
				wec.logger.Printf("Found wrong entry: key=%s, value=%s",
					hex.EncodeToString(key), hex.EncodeToString(value))
			}
		}
	}

	wec.logger.Printf("Found %d wrong entries", len(wrongEntries))
	return wrongEntries
}

func (wec *WrongEntryCleaner) isWrongEntry(key, value []byte) bool {
	keyHex := hex.EncodeToString(key)
	valueHex := hex.EncodeToString(value)

	// 检查是否是错误的seqID条目
	// 这些条目的特征：key是16字节的seqID，value也是相同的seqID
	if len(key) == 8 && len(value) == 8 {
		if keyHex == valueHex {
			// key和value相同，这是错误的条目
			return true
		}
	}

	// 检查是否是错误的WfsFileBean条目（被错误地存储为PATH_PRE）
	// 特征：key是 0000 + 8字节seqID，value是WfsFileBean
	if len(key) == 10 && key[0] == 0x00 && key[1] == 0x00 {
		// 检查后8字节是否是seqID（非零且有序）
		seqIDPart := key[2:]
		if wec.looksLikeSeqID(seqIDPart) && len(value) > 10 && len(value) < 100 {
			// 这看起来像是错误存储的WfsFileBean
			return true
		}
	}

	// 检查是否是错误的PATH_PRE条目
	// 正确的PATH_PRE应该是 0000 + 文件路径，不应该是纯数字
	if len(key) >= 2 && key[0] == 0x00 && key[1] == 0x00 {
		if len(key) > 2 {
			// 检查路径部分是否全是数字（这是错误的）
			pathPart := key[2:]
			if wec.isAllZeros(pathPart) {
				return true
			}
		}
	}

	return false
}

func (wec *WrongEntryCleaner) looksLikeSeqID(data []byte) bool {
	// 检查是否看起来像seqID（8字节，大端序，值在1-10之间）
	if len(data) != 8 {
		return false
	}

	// 检查前7字节是否都是0
	for i := 0; i < 7; i++ {
		if data[i] != 0 {
			return false
		}
	}

	// 检查最后一字节是否在1-10之间
	lastByte := data[7]
	return lastByte >= 1 && lastByte <= 10
}

func (wec *WrongEntryCleaner) isAllZeros(data []byte) bool {
	for _, b := range data {
		if b != 0 {
			return false
		}
	}
	return true
}

func (wec *WrongEntryCleaner) deleteWrongEntries(wrongEntries [][]byte) error {
	if len(wrongEntries) == 0 {
		wec.logger.Println("✅ No wrong entries to clean")
		return nil
	}

	wec.logger.Printf("Deleting %d wrong entries...", len(wrongEntries))

	if wec.config.DryRun {
		wec.logger.Printf("[DRY RUN] Would delete %d wrong entries", len(wrongEntries))
		for i, key := range wrongEntries {
			wec.logger.Printf("[DRY RUN] Would delete entry %d: key=%s", i+1, hex.EncodeToString(key))
		}
		return nil
	}

	batch := new(leveldb.Batch)
	for _, key := range wrongEntries {
		batch.Delete(key)
		if wec.config.Verbose {
			wec.logger.Printf("Deleting wrong entry: key=%s", hex.EncodeToString(key))
		}
	}

	if err := wec.db.Write(batch, nil); err != nil {
		return fmt.Errorf("failed to write batch: %v", err)
	}

	wec.logger.Printf("✅ Deleted %d wrong entries", len(wrongEntries))
	return nil
}

func (wec *WrongEntryCleaner) Close() {
	if wec.db != nil {
		wec.db.Close()
	}
}

func main() {
	config := &WrongEntryCleanerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Wrong Entry Cleaner\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	cleaner, err := NewWrongEntryCleaner(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer cleaner.Close()

	if err := cleaner.Clean(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
