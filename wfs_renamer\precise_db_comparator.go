// 精确数据库比较工具 - 找出真正的差异
package main

import (
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"
	"sort"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type PreciseComparatorConfig struct {
	Database1Path string
	Database2Path string
	Verbose       bool
}

type DatabaseEntry struct {
	Key   string
	Value string
	Size  int
}

type PreciseDBComparator struct {
	config *PreciseComparatorConfig
	db1    *leveldb.DB
	db2    *leveldb.DB
	logger *log.Logger
}

func NewPreciseDBComparator(config *PreciseComparatorConfig) (*PreciseDBComparator, error) {
	logger := log.New(os.Stdout, "[PreciseDBComparator] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db1, err := leveldb.OpenFile(config.Database1Path, options)
	if err != nil {
		return nil, fmt.Errorf("failed to open database1: %v", err)
	}

	db2, err := leveldb.OpenFile(config.Database2Path, options)
	if err != nil {
		db1.Close()
		return nil, fmt.Errorf("failed to open database2: %v", err)
	}

	return &PreciseDBComparator{
		config: config,
		db1:    db1,
		db2:    db2,
		logger: logger,
	}, nil
}

func (pdc *PreciseDBComparator) Compare() error {
	pdc.logger.Println("=== Precise Database Comparison ===")
	pdc.logger.Printf("Database 1 (Current): %s", pdc.config.Database1Path)
	pdc.logger.Printf("Database 2 (Reference): %s", pdc.config.Database2Path)

	// 收集两个数据库的所有条目
	entries1 := pdc.collectAllEntries(pdc.db1, "Current")
	entries2 := pdc.collectAllEntries(pdc.db2, "Reference")

	// 分析差异
	pdc.analyzeDifferences(entries1, entries2)

	return nil
}

func (pdc *PreciseDBComparator) collectAllEntries(db *leveldb.DB, dbName string) map[string]*DatabaseEntry {
	pdc.logger.Printf("Collecting all entries from %s database...", dbName)

	entries := make(map[string]*DatabaseEntry)
	iter := db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		keyHex := hex.EncodeToString(key)
		valueHex := hex.EncodeToString(value)

		entries[keyHex] = &DatabaseEntry{
			Key:   keyHex,
			Value: valueHex,
			Size:  len(value),
		}

		count++
	}

	pdc.logger.Printf("%s database has %d entries", dbName, count)
	return entries
}

func (pdc *PreciseDBComparator) analyzeDifferences(entries1, entries2 map[string]*DatabaseEntry) {
	pdc.logger.Printf("\n=== Analyzing Differences ===")

	// 找出只在DB1中存在的条目
	onlyInDB1 := make(map[string]*DatabaseEntry)
	for key, entry := range entries1 {
		if _, exists := entries2[key]; !exists {
			onlyInDB1[key] = entry
		}
	}

	// 找出只在DB2中存在的条目
	onlyInDB2 := make(map[string]*DatabaseEntry)
	for key, entry := range entries2 {
		if _, exists := entries1[key]; !exists {
			onlyInDB2[key] = entry
		}
	}

	// 找出值不同的条目
	differentValues := make(map[string][2]*DatabaseEntry)
	for key, entry1 := range entries1 {
		if entry2, exists := entries2[key]; exists {
			if entry1.Value != entry2.Value {
				differentValues[key] = [2]*DatabaseEntry{entry1, entry2}
			}
		}
	}

	pdc.logger.Printf("Entries only in Current DB: %d", len(onlyInDB1))
	pdc.logger.Printf("Entries only in Reference DB: %d", len(onlyInDB2))
	pdc.logger.Printf("Entries with different values: %d", len(differentValues))

	// 详细分析只在参考数据库中存在的条目（这些是我们缺失的）
	pdc.logger.Printf("\n=== Missing Entries (Only in Reference DB) ===")
	pdc.analyzeSpecificEntries("Missing", onlyInDB2)

	// 详细分析只在当前数据库中存在的条目（这些是多余的）
	pdc.logger.Printf("\n=== Extra Entries (Only in Current DB) ===")
	pdc.analyzeSpecificEntries("Extra", onlyInDB1)

	// 详细分析值不同的条目
	if len(differentValues) > 0 {
		pdc.logger.Printf("\n=== Entries with Different Values ===")
		for key, entries := range differentValues {
			pdc.logger.Printf("Key: %s", key)
			pdc.logger.Printf("  Current:   %s", entries[0].Value)
			pdc.logger.Printf("  Reference: %s", entries[1].Value)
		}
	}
}

func (pdc *PreciseDBComparator) analyzeSpecificEntries(category string, entries map[string]*DatabaseEntry) {
	if len(entries) == 0 {
		pdc.logger.Printf("No %s entries", category)
		return
	}

	// 按key排序
	keys := make([]string, 0, len(entries))
	for key := range entries {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// 分类分析
	pathPreEntries := make([]string, 0)
	pathSeqEntries := make([]string, 0)
	index0800Entries := make([]string, 0)
	fingerprintEntries := make([]string, 0)
	wfsFileBeanEntries := make([]string, 0)
	otherEntries := make([]string, 0)

	for _, key := range keys {
		entry := entries[key]
		
		if len(key) >= 4 {
			prefix := key[:4]
			switch prefix {
			case "0000":
				pathPreEntries = append(pathPreEntries, key)
			case "0100":
				pathSeqEntries = append(pathSeqEntries, key)
			case "0800":
				index0800Entries = append(index0800Entries, key)
			default:
				// 检查是否可能是指纹索引（8字节key，8字节value）
				if len(key) == 16 && entry.Size == 8 {
					fingerprintEntries = append(fingerprintEntries, key)
				} else if entry.Size > 15 && entry.Size < 100 {
					// 可能是WfsFileBean
					wfsFileBeanEntries = append(wfsFileBeanEntries, key)
				} else {
					otherEntries = append(otherEntries, key)
				}
			}
		} else {
			otherEntries = append(otherEntries, key)
		}
	}

	pdc.logger.Printf("%s PATH_PRE entries: %d", category, len(pathPreEntries))
	pdc.printEntryDetails(pathPreEntries, entries, 5)

	pdc.logger.Printf("%s PATH_SEQ entries: %d", category, len(pathSeqEntries))
	pdc.printEntryDetails(pathSeqEntries, entries, 5)

	pdc.logger.Printf("%s 0x0800 entries: %d", category, len(index0800Entries))
	pdc.printEntryDetails(index0800Entries, entries, 5)

	pdc.logger.Printf("%s Fingerprint entries: %d", category, len(fingerprintEntries))
	pdc.printEntryDetails(fingerprintEntries, entries, 10)

	pdc.logger.Printf("%s WfsFileBean entries: %d", category, len(wfsFileBeanEntries))
	pdc.printEntryDetails(wfsFileBeanEntries, entries, 10)

	pdc.logger.Printf("%s Other entries: %d", category, len(otherEntries))
	pdc.printEntryDetails(otherEntries, entries, 5)
}

func (pdc *PreciseDBComparator) printEntryDetails(keys []string, entries map[string]*DatabaseEntry, limit int) {
	if len(keys) == 0 {
		return
	}

	count := 0
	for _, key := range keys {
		if count >= limit {
			pdc.logger.Printf("  ... and %d more", len(keys)-limit)
			break
		}
		entry := entries[key]
		pdc.logger.Printf("  %s → %s (size: %d)", key, entry.Value, entry.Size)
		count++
	}
}

func (pdc *PreciseDBComparator) Close() {
	if pdc.db1 != nil {
		pdc.db1.Close()
	}
	if pdc.db2 != nil {
		pdc.db2.Close()
	}
}

func main() {
	config := &PreciseComparatorConfig{}

	flag.StringVar(&config.Database1Path, "db1", "", "Current database path (required)")
	flag.StringVar(&config.Database2Path, "db2", "", "Reference database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Precise Database Comparator\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db1 C:\\wfsdata\\wfsdb -db2 C:\\wfsdata_new\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.Database1Path == "" || config.Database2Path == "" {
		fmt.Fprintf(os.Stderr, "Error: Both -db1 and -db2 parameters are required\n")
		flag.Usage()
		os.Exit(1)
	}

	comparator, err := NewPreciseDBComparator(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer comparator.Close()

	if err := comparator.Compare(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
