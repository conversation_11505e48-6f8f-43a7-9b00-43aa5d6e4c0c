// WFS数据库导出到简化CSV工具 - 只导出文件名，支持2亿级别数据
package main

import (
	"bufio"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_SIMPLE = []byte{0x00, 0x00}
)

type SimpleCSVConfig struct {
	WFSPath       string
	CSVPath       string
	Verbose       bool
	BufferSize    int    // 缓冲区大小
	ProgressStep  int64  // 进度报告间隔
}

type WFSToSimpleCSV struct {
	config *SimpleCSVConfig
	wfsDB  *leveldb.DB
	logger *log.Logger
}

func NewWFSToSimpleCSV(config *SimpleCSVConfig) (*WFSToSimpleCSV, error) {
	logger := log.New(os.Stdout, "[WFSToSimpleCSV] ", log.LstdFlags)

	// 打开WFS数据库（只读模式）
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	wfsDB, err := leveldb.OpenFile(config.WFSPath, options)
	if err != nil {
		logger.Printf("Failed to open WFS DB normally, attempting recovery...")
		wfsDB, err = leveldb.RecoverFile(config.WFSPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover WFS database: %v", err)
		}
	}

	return &WFSToSimpleCSV{
		config: config,
		wfsDB:  wfsDB,
		logger: logger,
	}, nil
}

func (wts *WFSToSimpleCSV) Export() error {
	wts.logger.Println("=== Starting WFS to Simple CSV Export ===")
	wts.logger.Printf("Target: %s", wts.config.CSVPath)

	// 创建CSV文件
	file, err := os.Create(wts.config.CSVPath)
	if err != nil {
		return fmt.Errorf("failed to create CSV file: %v", err)
	}
	defer file.Close()

	// 使用缓冲写入器提高性能
	writer := bufio.NewWriterSize(file, wts.config.BufferSize)
	defer writer.Flush()

	// 写入CSV标题
	if _, err := writer.WriteString("filename\n"); err != nil {
		return fmt.Errorf("failed to write header: %v", err)
	}

	// 流式处理PATH_PRE索引
	return wts.streamProcessPATH_PRE(writer)
}

func (wts *WFSToSimpleCSV) streamProcessPATH_PRE(writer *bufio.Writer) error {
	wts.logger.Println("Streaming PATH_PRE index...")

	iter := wts.wfsDB.NewIterator(nil, nil)
	defer iter.Release()

	var count int64
	var errorCount int64

	// 流式处理每个PATH_PRE条目
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		
		// 检查是否是PATH_PRE条目
		if len(key) >= 2 && key[0] == PATH_PRE_SIMPLE[0] && key[1] == PATH_PRE_SIMPLE[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])
				seqIDBytes := iter.Value()
				
				// 清理文件名（移除路径前缀）
				cleanFileName := wts.cleanFileName(originalPath)
				
				// 写入CSV行
				csvLine := fmt.Sprintf("%s\n", wts.escapeCSV(cleanFileName))
				if _, err := writer.WriteString(csvLine); err != nil {
					errorCount++
					if wts.config.Verbose {
						wts.logger.Printf("Error writing line %d: %v", count+1, err)
					}
				} else {
					count++
				}

				// 进度报告
				if count%wts.config.ProgressStep == 0 {
					wts.logger.Printf("Processed %d records...", count)
					
					// 定期刷新缓冲区
					if err := writer.Flush(); err != nil {
						return fmt.Errorf("failed to flush buffer at record %d: %v", count, err)
					}
				}

				// 详细日志
				if wts.config.Verbose && count <= 10 {
					seqID := wts.bytesToInt64(seqIDBytes)
					wts.logger.Printf("Record %d: %s -> %s (seqID: %d)", count, originalPath, cleanFileName, seqID)
				}
			}
		}
	}

	// 检查迭代器错误
	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	// 最终刷新
	if err := writer.Flush(); err != nil {
		return fmt.Errorf("failed to flush final buffer: %v", err)
	}

	wts.logger.Printf("✅ Export completed: %d records written, %d errors", count, errorCount)
	
	// 文件大小信息
	if fileInfo, err := os.Stat(wts.config.CSVPath); err == nil {
		sizeMB := float64(fileInfo.Size()) / 1024 / 1024
		wts.logger.Printf("CSV file size: %.2f MB", sizeMB)
		
		if sizeMB > 1024 {
			wts.logger.Printf("⚠️  Large file warning: %.2f GB - consider splitting for better handling", sizeMB/1024)
		}
	}

	return nil
}

func (wts *WFSToSimpleCSV) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	parts := strings.Split(path, "/")
	return parts[len(parts)-1]
}

func (wts *WFSToSimpleCSV) escapeCSV(value string) string {
	// 简单的CSV转义：如果包含逗号、引号或换行符，则用引号包围
	if strings.Contains(value, ",") || strings.Contains(value, "\"") || strings.Contains(value, "\n") {
		// 转义内部的引号
		escaped := strings.ReplaceAll(value, "\"", "\"\"")
		return fmt.Sprintf("\"%s\"", escaped)
	}
	return value
}

func (wts *WFSToSimpleCSV) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wts *WFSToSimpleCSV) Close() {
	if wts.wfsDB != nil {
		wts.wfsDB.Close()
	}
}

func main() {
	config := &SimpleCSVConfig{
		BufferSize:   64 * 1024,  // 64KB缓冲区
		ProgressStep: 100000,     // 每10万条记录报告一次进度
	}

	flag.StringVar(&config.WFSPath, "wfs", "", "WFS database path (required)")
	flag.StringVar(&config.CSVPath, "csv", "", "CSV output path (optional, default: wfs_filenames.csv)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.IntVar(&config.BufferSize, "buffer", 64*1024, "Buffer size in bytes")
	flag.Int64Var(&config.ProgressStep, "progress", 100000, "Progress report interval")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS to Simple CSV Exporter - Optimized for large datasets\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -wfs C:\\wfsdata\\wfsdb\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -wfs C:\\wfsdata\\wfsdb -csv filenames.csv -progress 1000000\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "\nFor 200M+ records:\n")
		fmt.Fprintf(os.Stderr, "  %s -wfs C:\\wfsdata\\wfsdb -buffer 1048576 -progress 10000000\n", os.Args[0])
	}

	flag.Parse()

	if config.WFSPath == "" {
		fmt.Fprintf(os.Stderr, "Error: -wfs parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	if config.CSVPath == "" {
		config.CSVPath = filepath.Join(filepath.Dir(config.WFSPath), "wfs_filenames.csv")
	}

	// 对于大数据集的优化建议
	if config.ProgressStep < 10000 {
		fmt.Fprintf(os.Stderr, "Warning: Small progress step may impact performance for large datasets\n")
	}

	if config.BufferSize < 32*1024 {
		fmt.Fprintf(os.Stderr, "Warning: Small buffer size may impact performance for large datasets\n")
	}

	exporter, err := NewWFSToSimpleCSV(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer exporter.Close()

	if err := exporter.Export(); err != nil {
		fmt.Fprintf(os.Stderr, "Error during export: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Export completed successfully: %s\n", config.CSVPath)
}
