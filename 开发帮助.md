# WFS 文件存储系统开发帮助文档

## 库功能概述

WFS (Web File System) 是一个高性能的分布式文件存储系统，专门设计用于解决海量小文件存储的挑战。该系统提供了完整的文件管理功能，包括文件上传、下载、删除、重命名以及新增的文件存在性检查功能。

## 主要特点

### 1. 高性能存储
- **微秒级响应**: 在理想条件下可达到微秒级响应时间
- **高并发支持**: 读取操作可达150万ops/s，写入操作可达4万ops/s
- **智能缓存**: 内置LRU缓存机制，提高热点数据访问速度

### 2. 海量小文件优化
- **文件聚合**: 将多个小文件合并存储，减少元数据开销
- **高效索引**: 基于LevelDB的快速索引系统
- **压缩存储**: 支持多种压缩算法，节省存储空间

### 3. 多协议支持
- **Thrift RPC**: 高性能的二进制协议，适合程序间通信
- **HTTP/HTTPS**: RESTful API，便于Web应用集成
- **多语言客户端**: 支持Go、Java、Python、Rust等多种语言

### 4. 完整的文件管理功能
- **文件上传**: 支持单文件和批量上传
- **文件下载**: 高速文件检索和下载
- **文件删除**: 安全的文件删除操作
- **文件重命名**: 支持文件路径修改
- **存在性检查**: 新增的Exist接口，快速检查文件是否存在

## 调用程序样例

### 1. Go语言客户端示例

#### 1.1 基本连接设置
```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"

    "github.com/donnie4w/gothrift/thrift"
    "github.com/donnie4w/wfs/stub"
)

func createClient() (*stub.WfsIfaceClient, error) {
    // 创建传输层
    transport, err := thrift.NewTSocketTimeout("localhost:6802", time.Second*10)
    if err != nil {
        return nil, err
    }
    
    if err := transport.Open(); err != nil {
        return nil, err
    }
    
    // 创建协议层
    protocol := thrift.NewTCompactProtocol(transport)
    
    // 创建客户端
    client := stub.NewWfsIfaceClient(thrift.NewTStandardClient(protocol, protocol))
    
    return client, nil
}
```

#### 1.2 用户认证
```go
func authenticate(client *stub.WfsIfaceClient) error {
    ctx := context.Background()
    
    // 创建认证信息
    auth := &stub.WfsAuth{}
    username := "admin"
    password := "123"
    auth.Name = &username
    auth.Pwd = &password
    
    // 执行认证
    result, err := client.Auth(ctx, auth)
    if err != nil {
        return fmt.Errorf("认证请求失败: %v", err)
    }
    
    if !result.GetOk() {
        return fmt.Errorf("认证失败")
    }
    
    fmt.Println("认证成功")
    return nil
}
```

#### 1.3 文件上传
```go
func uploadFile(client *stub.WfsIfaceClient, filePath string, fileData []byte) error {
    ctx := context.Background()
    
    // 创建文件对象
    wfsFile := &stub.WfsFile{
        Name: filePath,
        Data: fileData,
    }
    
    // 可选：设置压缩类型
    compress := int8(1) // 1表示启用压缩
    wfsFile.Compress = &compress
    
    // 上传文件
    result, err := client.Append(ctx, wfsFile)
    if err != nil {
        return fmt.Errorf("上传失败: %v", err)
    }
    
    if !result.GetOk() {
        return fmt.Errorf("上传失败: %v", result.GetError())
    }
    
    fmt.Printf("文件 %s 上传成功\n", filePath)
    return nil
}
```

#### 1.4 文件下载
```go
func downloadFile(client *stub.WfsIfaceClient, filePath string) ([]byte, error) {
    ctx := context.Background()
    
    // 下载文件
    result, err := client.Get(ctx, filePath)
    if err != nil {
        return nil, fmt.Errorf("下载失败: %v", err)
    }
    
    if result.GetData() == nil {
        return nil, fmt.Errorf("文件不存在: %s", filePath)
    }
    
    fmt.Printf("文件 %s 下载成功，大小: %d 字节\n", filePath, len(result.GetData()))
    return result.GetData(), nil
}
```

#### 1.5 检查文件是否存在（新功能）
```go
func checkFileExists(client *stub.WfsIfaceClient, filePath string) error {
    ctx := context.Background()
    
    // 检查文件是否存在
    result, err := client.Exist(ctx, filePath)
    if err != nil {
        return fmt.Errorf("检查失败: %v", err)
    }
    
    if result.GetExists() {
        fmt.Printf("文件 %s 存在", filePath)
        if result.IsSetSize() {
            fmt.Printf("，大小: %d 字节", result.GetSize())
        }
        fmt.Println()
    } else {
        fmt.Printf("文件 %s 不存在\n", filePath)
    }
    
    return nil
}
```

#### 1.6 文件删除
```go
func deleteFile(client *stub.WfsIfaceClient, filePath string) error {
    ctx := context.Background()
    
    // 删除文件
    result, err := client.Delete(ctx, filePath)
    if err != nil {
        return fmt.Errorf("删除失败: %v", err)
    }
    
    if !result.GetOk() {
        return fmt.Errorf("删除失败: %v", result.GetError())
    }
    
    fmt.Printf("文件 %s 删除成功\n", filePath)
    return nil
}
```

#### 1.7 文件重命名
```go
func renameFile(client *stub.WfsIfaceClient, oldPath, newPath string) error {
    ctx := context.Background()
    
    // 重命名文件
    result, err := client.Rename(ctx, oldPath, newPath)
    if err != nil {
        return fmt.Errorf("重命名失败: %v", err)
    }
    
    if !result.GetOk() {
        return fmt.Errorf("重命名失败: %v", result.GetError())
    }
    
    fmt.Printf("文件从 %s 重命名为 %s 成功\n", oldPath, newPath)
    return nil
}
```

#### 1.8 完整示例程序
```go
func main() {
    // 创建客户端
    client, err := createClient()
    if err != nil {
        log.Fatal("创建客户端失败:", err)
    }
    
    // 认证
    if err := authenticate(client); err != nil {
        log.Fatal("认证失败:", err)
    }
    
    // 测试数据
    testFile := "test/example.txt"
    testData := []byte("Hello, WFS! This is a test file.")
    
    // 上传文件
    if err := uploadFile(client, testFile, testData); err != nil {
        log.Printf("上传失败: %v", err)
    }
    
    // 检查文件是否存在
    if err := checkFileExists(client, testFile); err != nil {
        log.Printf("检查失败: %v", err)
    }
    
    // 下载文件
    if data, err := downloadFile(client, testFile); err != nil {
        log.Printf("下载失败: %v", err)
    } else {
        fmt.Printf("下载的内容: %s\n", string(data))
    }
    
    // 重命名文件
    newFile := "test/renamed_example.txt"
    if err := renameFile(client, testFile, newFile); err != nil {
        log.Printf("重命名失败: %v", err)
    }
    
    // 再次检查原文件（应该不存在）
    if err := checkFileExists(client, testFile); err != nil {
        log.Printf("检查失败: %v", err)
    }
    
    // 检查新文件（应该存在）
    if err := checkFileExists(client, newFile); err != nil {
        log.Printf("检查失败: %v", err)
    }
    
    // 删除文件
    if err := deleteFile(client, newFile); err != nil {
        log.Printf("删除失败: %v", err)
    }
    
    // 最后检查（应该不存在）
    if err := checkFileExists(client, newFile); err != nil {
        log.Printf("检查失败: %v", err)
    }
}
```

### 2. HTTP API调用示例

#### 2.1 文件上传
```bash
# 上传文件
curl -F "file=@example.txt" \
     "http://localhost:6801/append/test/example.txt" \
     -H "username:admin" \
     -H "password:123"
```

#### 2.2 文件下载
```bash
# 下载文件
curl "http://localhost:6801/test/example.txt" \
     -H "username:admin" \
     -H "password:123"
```

#### 2.3 文件删除
```bash
# 删除文件
curl -X DELETE "http://localhost:6801/delete/test/example.txt" \
     -H "username:admin" \
     -H "password:123"
```

### 3. 错误处理最佳实践

```go
func handleWfsError(err error, operation string) {
    if err != nil {
        log.Printf("%s 操作失败: %v", operation, err)
        
        // 可以根据错误类型进行不同处理
        if strings.Contains(err.Error(), "auth") {
            log.Println("认证错误，请检查用户名和密码")
        } else if strings.Contains(err.Error(), "not exist") {
            log.Println("文件不存在")
        } else if strings.Contains(err.Error(), "oversize") {
            log.Println("文件大小超过限制")
        }
    }
}
```

### 4. 性能优化建议

#### 4.1 连接复用
```go
// 使用连接池复用连接
type WfsClientPool struct {
    clients chan *stub.WfsIfaceClient
    maxSize int
}

func (p *WfsClientPool) Get() *stub.WfsIfaceClient {
    select {
    case client := <-p.clients:
        return client
    default:
        // 创建新连接
        client, _ := createClient()
        return client
    }
}

func (p *WfsClientPool) Put(client *stub.WfsIfaceClient) {
    select {
    case p.clients <- client:
    default:
        // 池已满，关闭连接
        // client.Close()
    }
}
```

#### 4.2 批量操作
```go
// 批量检查文件存在性
func batchCheckExists(client *stub.WfsIfaceClient, paths []string) map[string]bool {
    results := make(map[string]bool)
    
    for _, path := range paths {
        if result, err := client.Exist(context.Background(), path); err == nil {
            results[path] = result.GetExists()
        }
    }
    
    return results
}
```

## 配置说明

### 服务器配置文件 (wfs.json)
```json
{
    "listen": 4660,        // HTTP服务端口
    "opaddr": ":6802",     // Thrift服务地址
    "webaddr": ":6801",    // Web管理界面地址
    "memLimit": 128,       // 内存限制(MB)
    "data.maxsize": 10000, // 单文件大小限制(KB)
    "filesize": 100        // 存储文件大小限制(MB)
}
```

## 部署指南

### 1. 编译
```bash
# 核心版本（无Web界面）
go build -o wfs_core.exe wfs_core.go

# 完整版本（需要CGO支持）
go build -o wfs.exe .
```

### 2. 启动服务
```bash
# 使用默认配置
./wfs_core.exe

# 使用自定义配置
./wfs_core.exe -c custom_config.json
```

### 3. 负载均衡部署
使用Nginx进行负载均衡：
```nginx
upstream wfs_backend {
    server 127.0.0.1:6801;
    server 127.0.0.1:6811;
    server 127.0.0.1:6821;
}

server {
    listen 80;
    location / {
        proxy_pass http://wfs_backend;
    }
}
```

## 注意事项

1. **认证**: 所有操作都需要先进行用户认证
2. **文件大小**: 注意单文件大小限制，默认10MB
3. **路径格式**: 使用Unix风格的路径分隔符 "/"
4. **错误处理**: 始终检查返回的错误信息
5. **连接管理**: 长期使用建议实现连接池
6. **性能监控**: 在生产环境中监控系统性能指标

通过以上示例和说明，开发者可以快速集成WFS文件存储系统，实现高效的文件管理功能。新增的Exist接口为应用提供了便捷的文件存在性检查能力，特别适用于需要快速验证文件状态的场景。
