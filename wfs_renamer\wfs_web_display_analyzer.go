// WFS网页显示逻辑分析工具 - 深入分析为什么某些文件不显示
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"sort"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_WEB = []byte{0x00, 0x00}
	PATH_SEQ_WEB = []byte{0x01, 0x00}
)

type WebDisplayConfig struct {
	DatabasePath string
	Verbose      bool
}

type WebFileRecord struct {
	SeqID          int64
	SeqIDBytes     []byte
	PathPrePath    string
	PathSeqPath    string
	PathSeqTime    int64
	HasPathPre     bool
	HasPathSeq     bool
	HasFingerprint bool
	Has0x0800      bool
	WebDisplayable bool
	Issues         []string
}

type WFSWebDisplayAnalyzer struct {
	config *WebDisplayConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewWFSWebDisplayAnalyzer(config *WebDisplayConfig) (*WFSWebDisplayAnalyzer, error) {
	logger := log.New(os.Stdout, "[WFSWebDisplayAnalyzer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &WFSWebDisplayAnalyzer{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (wda *WFSWebDisplayAnalyzer) Analyze() error {
	wda.logger.Println("=== WFS Web Display Logic Analysis ===")

	// 1. 收集所有文件记录
	records, err := wda.collectAllRecords()
	if err != nil {
		return fmt.Errorf("failed to collect records: %v", err)
	}

	// 2. 分析每个记录的完整性
	wda.analyzeRecords(records)

	// 3. 模拟WFS网页显示逻辑
	wda.simulateWebDisplay(records)

	return nil
}

func (wda *WFSWebDisplayAnalyzer) collectAllRecords() (map[int64]*WebFileRecord, error) {
	wda.logger.Println("Collecting all file records...")

	records := make(map[int64]*WebFileRecord)

	// 从PATH_PRE收集
	if err := wda.collectFromPathPre(records); err != nil {
		return nil, err
	}

	// 从PATH_SEQ收集
	if err := wda.collectFromPathSeq(records); err != nil {
		return nil, err
	}

	// 检查指纹索引
	wda.checkFingerprints(records)

	// 检查0x0800索引
	wda.check0x0800(records)

	return records, nil
}

func (wda *WFSWebDisplayAnalyzer) collectFromPathPre(records map[int64]*FileRecord) error {
	wda.logger.Println("Scanning PATH_PRE index...")

	iter := wda.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_WEB[0] && key[1] == PATH_PRE_WEB[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := make([]byte, len(iter.Value()))
				copy(seqIDBytes, iter.Value())
				seqID := wda.bytesToInt64(seqIDBytes)

				if records[seqID] == nil {
					records[seqID] = &FileRecord{
						SeqID:      seqID,
						SeqIDBytes: seqIDBytes,
					}
				}

				records[seqID].PathPrePath = path
				records[seqID].HasPathPre = true

				if wda.config.Verbose {
					wda.logger.Printf("PATH_PRE: seqID=%d, path=%s", seqID, path)
				}
			}
		}
	}

	return nil
}

func (wda *WFSWebDisplayAnalyzer) collectFromPathSeq(records map[int64]*FileRecord) error {
	wda.logger.Println("Scanning PATH_SEQ index...")

	iter := wda.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_SEQ_WEB[0] && key[1] == PATH_SEQ_WEB[1] {
			if len(key) > 2 {
				seqIDBytes := key[2:]
				seqID := wda.bytesToInt64(seqIDBytes)

				if records[seqID] == nil {
					records[seqID] = &FileRecord{
						SeqID:      seqID,
						SeqIDBytes: make([]byte, len(seqIDBytes)),
					}
					copy(records[seqID].SeqIDBytes, seqIDBytes)
				}

				// 解析WfsPathBean
				path, timestamp, err := wda.parseWfsPathBean(iter.Value())
				if err != nil {
					wda.logger.Printf("Failed to parse PATH_SEQ for seqID %d: %v", seqID, err)
					records[seqID].Issues = append(records[seqID].Issues, fmt.Sprintf("PATH_SEQ parse error: %v", err))
				} else {
					records[seqID].PathSeqPath = path
					records[seqID].PathSeqTime = timestamp
					records[seqID].HasPathSeq = true

					if wda.config.Verbose {
						wda.logger.Printf("PATH_SEQ: seqID=%d, path=%s, timestamp=%d", seqID, path, timestamp)
					}
				}
			}
		}
	}

	return nil
}

func (wda *WFSWebDisplayAnalyzer) checkFingerprints(records map[int64]*FileRecord) {
	wda.logger.Println("Checking fingerprint indexes...")

	for seqID, record := range records {
		if record.HasPathPre {
			fingerprint := wda.calculateFingerprint(record.PathPrePath)
			if _, err := wda.db.Get(fingerprint, nil); err == nil {
				record.HasFingerprint = true
				if wda.config.Verbose {
					wda.logger.Printf("Fingerprint exists for seqID %d (%s)", seqID, record.PathPrePath)
				}
			} else {
				record.Issues = append(record.Issues, "Missing fingerprint index")
				if wda.config.Verbose {
					wda.logger.Printf("Missing fingerprint for seqID %d (%s)", seqID, record.PathPrePath)
				}
			}
		}
	}
}

func (wda *WFSWebDisplayAnalyzer) check0x0800(records map[int64]*FileRecord) {
	wda.logger.Println("Checking 0x0800 indexes...")

	prefix0x0800 := []byte{0x08, 0x00}
	iter := wda.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0x0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		// 尝试从key中提取seqID
		seqID := wda.extractSeqIDFromKey(key)
		if seqID > 0 && records[seqID] != nil {
			records[seqID].Has0x0800 = true

			if wda.config.Verbose {
				path, _, _ := wda.parseWfsPathBean(iter.Value())
				wda.logger.Printf("0x0800 exists for seqID %d: %s", seqID, path)
			}
		}
	}
}

func (wda *WFSWebDisplayAnalyzer) analyzeRecords(records map[int64]*FileRecord) {
	wda.logger.Println("\n=== Record Analysis ===")

	// 按seqID排序
	var seqIDs []int64
	for seqID := range records {
		seqIDs = append(seqIDs, seqID)
	}
	sort.Slice(seqIDs, func(i, j int) bool { return seqIDs[i] < seqIDs[j] })

	for _, seqID := range seqIDs {
		record := records[seqID]

		// 分析网页显示能力
		record.WebDisplayable = wda.isWebDisplayable(record)

		// 检查一致性问题
		wda.checkConsistency(record)

		// 输出分析结果
		status := "❌"
		if record.WebDisplayable {
			status = "✅"
		}

		wda.logger.Printf("%s seqID %d:", status, seqID)
		wda.logger.Printf("  PATH_PRE: %v (%s)", record.HasPathPre, record.PathPrePath)
		wda.logger.Printf("  PATH_SEQ: %v (%s, timestamp=%d)", record.HasPathSeq, record.PathSeqPath, record.PathSeqTime)
		wda.logger.Printf("  Fingerprint: %v", record.HasFingerprint)
		wda.logger.Printf("  0x0800: %v", record.Has0x0800)

		if len(record.Issues) > 0 {
			wda.logger.Printf("  Issues:")
			for _, issue := range record.Issues {
				wda.logger.Printf("    - %s", issue)
			}
		}
		wda.logger.Println()
	}
}

func (wda *WFSWebDisplayAnalyzer) isWebDisplayable(record *FileRecord) bool {
	// WFS网页显示的最低要求：PATH_SEQ必须存在且可解析
	return record.HasPathSeq && record.PathSeqPath != ""
}

func (wda *WFSWebDisplayAnalyzer) checkConsistency(record *FileRecord) {
	// 检查PATH_PRE和PATH_SEQ的路径一致性
	if record.HasPathPre && record.HasPathSeq {
		if record.PathPrePath != record.PathSeqPath {
			record.Issues = append(record.Issues,
				fmt.Sprintf("Path mismatch: PATH_PRE=%s, PATH_SEQ=%s",
					record.PathPrePath, record.PathSeqPath))
		}
	}

	// 检查必要索引
	if record.HasPathPre && !record.HasPathSeq {
		record.Issues = append(record.Issues, "Has PATH_PRE but missing PATH_SEQ")
	}

	if record.HasPathSeq && !record.HasPathPre {
		record.Issues = append(record.Issues, "Has PATH_SEQ but missing PATH_PRE")
	}
}

func (wda *WFSWebDisplayAnalyzer) simulateWebDisplay(records map[int64]*FileRecord) {
	wda.logger.Println("\n=== Web Display Simulation ===")

	displayableCount := 0
	var displayableFiles []string

	// 按seqID排序
	var seqIDs []int64
	for seqID := range records {
		seqIDs = append(seqIDs, seqID)
	}
	sort.Slice(seqIDs, func(i, j int) bool { return seqIDs[i] < seqIDs[j] })

	for _, seqID := range seqIDs {
		record := records[seqID]
		if record.WebDisplayable {
			displayableCount++
			displayableFiles = append(displayableFiles, record.PathSeqPath)
			wda.logger.Printf("Web would display: %s (seqID=%d, timestamp=%d)",
				record.PathSeqPath, seqID, record.PathSeqTime)
		} else {
			wda.logger.Printf("Web would NOT display: seqID=%d (missing PATH_SEQ or parse error)", seqID)
		}
	}

	wda.logger.Printf("\n=== Summary ===")
	wda.logger.Printf("Total records: %d", len(records))
	wda.logger.Printf("Web displayable: %d", displayableCount)
	wda.logger.Printf("Web non-displayable: %d", len(records)-displayableCount)

	if displayableCount > 0 {
		wda.logger.Printf("Files that would display on web:")
		for _, file := range displayableFiles {
			wda.logger.Printf("  - %s", file)
		}
	}
}

// 辅助函数
func (wda *WFSWebDisplayAnalyzer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wda *WFSWebDisplayAnalyzer) calculateFingerprint(path string) []byte {
	// 简化的指纹计算（实际WFS可能使用不同算法）
	hash := make([]byte, 16)
	pathBytes := []byte(path)
	for i, b := range pathBytes {
		hash[i%16] ^= b
	}
	return hash
}

func (wda *WFSWebDisplayAnalyzer) extractSeqIDFromKey(key []byte) int64 {
	// 尝试不同的seqID提取方法
	if len(key) >= 10 {
		seqIDBytes := key[len(key)-8:]
		seqID := wda.bytesToInt64(seqIDBytes)
		if seqID > 0 && seqID < 1000000 {
			return seqID
		}
	}
	return 0
}

func (wda *WFSWebDisplayAnalyzer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wda *WFSWebDisplayAnalyzer) Close() {
	if wda.db != nil {
		wda.db.Close()
	}
}

func main() {
	config := &WebDisplayConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Web Display Logic Analyzer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	analyzer, err := NewWFSWebDisplayAnalyzer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer analyzer.Close()

	if err := analyzer.Analyze(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
