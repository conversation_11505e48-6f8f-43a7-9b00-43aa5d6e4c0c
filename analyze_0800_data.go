// Analyze 0x0800 Index Data Structure
// 分析0x0800索引数据结构，找出文件内容存储方式

package main

import (
	"encoding/binary"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

var (
	INDEX_0800 = []byte{0x08, 0x00}
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: analyze_0800_data <db_path>")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			log.Fatalf("Failed to open/recover database: %v", err)
		}
	}
	defer db.Close()

	fmt.Println("=== Analyzing 0x0800 Index Data ===")

	// 遍历0x0800索引
	iter := db.NewIterator(levelutil.BytesPrefix(INDEX_0800), nil)
	defer iter.Release()

	count := 0
	for iter.Next() {
		key := iter.Key()
		value := iter.Value()
		count++

		fmt.Printf("\n--- Entry %d ---\n", count)
		fmt.Printf("Key (hex): %x\n", key)
		fmt.Printf("Key (len): %d\n", len(key))
		
		if len(key) >= len(INDEX_0800)+8 {
			seqIDBytes := key[len(key)-8:]
			seqID := int64(binary.BigEndian.Uint64(seqIDBytes))
			fmt.Printf("SeqID: %d\n", seqID)
		}

		fmt.Printf("Value (hex): %x\n", value)
		fmt.Printf("Value (len): %d\n", len(value))

		// 尝试解析protobuf数据
		if path, timestamp, err := parseWfsPathBean(value); err == nil {
			fmt.Printf("Parsed Path: %s\n", path)
			fmt.Printf("Parsed Timestamp: %d\n", timestamp)
		} else {
			fmt.Printf("Parse error: %v\n", err)
		}
	}

	fmt.Printf("\nTotal 0x0800 entries: %d\n", count)

	// 现在扫描所有其他数据，寻找文件内容
	fmt.Println("\n=== Scanning for File Content Data ===")
	
	allIter := db.NewIterator(nil, nil)
	defer allIter.Release()

	contentCount := 0
	for allIter.Next() {
		key := allIter.Key()
		value := allIter.Value()

		// 跳过已知的索引前缀
		if len(key) >= 2 {
			prefix := key[:2]
			if binary.BigEndian.Uint16(prefix) == 0x0000 || // PATH_PRE
			   binary.BigEndian.Uint16(prefix) == 0x0100 || // PATH_SEQ
			   binary.BigEndian.Uint16(prefix) == 0x0800 {  // INDEX_0800
				continue
			}
		}

		// 检查是否可能是文件内容
		if len(value) > 100 { // 假设文件内容至少100字节
			contentCount++
			fmt.Printf("\nPossible content entry %d:\n", contentCount)
			fmt.Printf("Key (hex): %x\n", key)
			fmt.Printf("Key (len): %d\n", len(key))
			fmt.Printf("Value (len): %d bytes\n", len(value))
			
			// 显示前50字节的内容
			displayLen := 50
			if len(value) < displayLen {
				displayLen = len(value)
			}
			fmt.Printf("Value preview: %x\n", value[:displayLen])
			
			// 尝试显示为文本
			fmt.Printf("Value as text: %q\n", string(value[:displayLen]))
		}
	}

	fmt.Printf("\nTotal possible content entries: %d\n", contentCount)
}

// 解析protobuf格式的WfsPathBean
func parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}
