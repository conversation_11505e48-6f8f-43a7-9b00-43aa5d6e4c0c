# WFS LevelDB Reader 开发帮助

## 📋 功能概述

WFS LevelDB Reader是一个C++开发库，用于读取WFS（Web File System）的LevelDB数据库，解析文件信息并提取文件内容到磁盘。

### 主要特点

- ✅ **直接读取LevelDB**：无需WFS服务，直接访问数据库文件
- ✅ **多索引支持**：支持PATH_PRE、PATH_SEQ、0x0800等多种索引格式
- ✅ **智能内容检测**：自动检测和提取文件内容
- ✅ **文件类型识别**：支持JPEG、PNG、TXT等常见文件格式
- ✅ **路径修正**：自动提取纯文件名，去除路径信息
- ✅ **高性能处理**：使用现代C++20特性，优化性能
- ✅ **详细日志**：提供彩色输出和进度显示

## 🛠️ 技术栈

- **C++标准**：C++20
- **构建系统**：CMake 3.20+
- **包管理**：vcpkg
- **主要依赖**：
  - LevelDB：数据库访问
  - fmt：格式化输出和日志
  - OpenSSL：MD5计算
  - protobuf：数据解析（可选）

## 📁 项目结构

```
wfs_leveldb_reader/
├── CMakeLists.txt          # CMake构建配置
├── build.bat              # Windows构建脚本
├── 开发帮助.md             # 本文档
├── include/               # 头文件目录
│   ├── datatype.hpp       # 数据类型定义
│   ├── leveldb_reader.hpp # LevelDB读取器接口
│   ├── wfs_data_parser.hpp# WFS数据解析器接口
│   └── file_extractor.hpp # 文件提取器接口
├── src/                   # 源文件目录
│   ├── main.cpp           # 主程序
│   ├── leveldb_reader.cpp # LevelDB读取器实现
│   ├── wfs_data_parser.cpp# WFS数据解析器实现
│   └── file_extractor.cpp # 文件提取器实现
└── redist_desk/           # 发布目录
```

## 🔧 编译和安装

### 前置要求

1. **Visual Studio 2022** 或更高版本
2. **CMake 3.20+**
3. **vcpkg** 安装在 `C:\dev\vcpkg\`
4. **必要的vcpkg包**：
   ```bash
   vcpkg install leveldb:x64-windows
   vcpkg install fmt:x64-windows
   vcpkg install openssl:x64-windows
   ```

### 构建步骤

```bash
# 1. 克隆或复制项目到本地
# 2. 运行构建脚本
build.bat

# 或者手动构建
mkdir build
cd build
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake
cmake --build . --config Release
```

## 🚀 使用方法

### 基本用法

```bash
# 读取WFS数据库并提取文件
wfs_leveldb_reader.exe C:\wfsdata

# 指定输出目录
wfs_leveldb_reader.exe C:\wfsdata -o extracted_files

# 详细输出模式
wfs_leveldb_reader.exe C:\wfsdata -v

# 创建测试内容（当文件内容缺失时）
wfs_leveldb_reader.exe C:\wfsdata -t
```

### 参数说明

- `wfsdata_path`: WFS数据目录路径（必须包含wfsdb子目录）
- `-o, --output <dir>`: 输出目录（默认：extracted_files）
- `-v, --verbose`: 详细输出模式
- `-t, --test-content`: 创建测试内容
- `-h, --help`: 显示帮助信息

## 📊 调用程序样例

### C++调用示例

```cpp
#include "leveldb_reader.hpp"
#include "file_extractor.hpp"
#include <iostream>

int main() {
    using namespace wfs;
    
    // 创建LevelDB读取器
    auto reader = create_leveldb_reader();
    
    // 打开数据库
    std::string db_path = "C:\\wfsdata\\wfsdb";
    auto result = reader->open_database(db_path);
    if (result != ErrorCode::SUCCESS) {
        std::cerr << "Failed to open database" << std::endl;
        return 1;
    }
    
    // 扫描数据库
    result = reader->scan_all_entries();
    if (result != ErrorCode::SUCCESS) {
        std::cerr << "Failed to scan database" << std::endl;
        return 1;
    }
    
    // 获取文件记录
    const auto& records = reader->get_file_records();
    std::cout << "Found " << records.size() << " files" << std::endl;
    
    // 提取文件
    auto extractor = create_file_extractor();
    extractor->set_output_directory("extracted_files");
    extractor->extract_files(records);
    
    std::cout << "Extracted " << extractor->get_extracted_count() 
              << " files" << std::endl;
    
    return 0;
}
```

### 进度回调示例

```cpp
// 设置进度回调
reader->set_progress_callback([](size_t current, size_t total) {
    if (total > 0) {
        double percentage = (double)current / total * 100.0;
        std::cout << "\rProgress: " << current << "/" << total 
                  << " (" << std::fixed << std::setprecision(1) 
                  << percentage << "%)" << std::flush;
    }
});
```

### 自定义文件处理

```cpp
// 获取文件记录后自定义处理
const auto& records = reader->get_file_records();
for (const auto& [seq_id, record] : records) {
    std::cout << "SeqID: " << seq_id << std::endl;
    std::cout << "Original Path: " << record.original_path << std::endl;
    std::cout << "File Name: " << record.file_name << std::endl;
    std::cout << "Content Size: " << record.content_size << " bytes" << std::endl;
    
    // 自定义处理文件内容
    if (!record.content_data.empty()) {
        // 处理文件数据...
    }
}
```

## 🔍 核心接口说明

### LevelDBReader_i 接口

```cpp
class LevelDBReader_i {
public:
    // 打开数据库
    virtual ErrorCode open_database(const std::string& db_path) = 0;
    
    // 扫描所有条目
    virtual ErrorCode scan_all_entries() = 0;
    
    // 获取统计信息
    virtual const DatabaseStats& get_stats() const = 0;
    
    // 获取文件记录
    virtual const std::map<int64_t, FileRecord>& get_file_records() const = 0;
    
    // 设置进度回调
    virtual void set_progress_callback(std::function<void(size_t, size_t)> callback) = 0;
};
```

### FileExtractor_i 接口

```cpp
class FileExtractor_i {
public:
    // 设置输出目录
    virtual ErrorCode set_output_directory(const std::string& output_dir) = 0;
    
    // 提取单个文件
    virtual ErrorCode extract_file(const FileRecord& record) = 0;
    
    // 批量提取文件
    virtual ErrorCode extract_files(const std::map<int64_t, FileRecord>& records) = 0;
    
    // 获取统计信息
    virtual size_t get_extracted_count() const = 0;
    virtual size_t get_total_extracted_size() const = 0;
};
```

### 数据结构

```cpp
// 文件记录
struct FileRecord {
    int64_t seq_id;                    // 序列号
    std::string original_path;         // 原始路径
    std::string file_name;             // 文件名
    std::vector<uint8_t> content_data; // 文件内容
    int64_t timestamp;                 // 时间戳
    size_t content_size;               // 内容大小
};

// 数据库统计
struct DatabaseStats {
    size_t total_entries;      // 总条目数
    size_t path_pre_entries;   // PATH_PRE条目数
    size_t path_seq_entries;   // PATH_SEQ条目数
    size_t index_0800_entries; // 0x0800条目数
    size_t content_entries;    // 内容条目数
    size_t extracted_files;    // 提取文件数
    size_t total_content_size; // 总内容大小
};
```

## ⚠️ 注意事项

1. **数据库权限**：确保对WFS数据库目录有读取权限
2. **磁盘空间**：确保输出目录有足够的磁盘空间
3. **文件名冲突**：工具会自动处理文件名冲突，生成唯一文件名
4. **大文件处理**：默认限制单文件大小为100MB，可通过配置调整
5. **字符编码**：所有文件名和路径使用UTF-8编码

## 🐛 故障排除

### 常见问题

1. **数据库打开失败**
   - 检查路径是否正确
   - 确认wfsdb目录存在
   - 检查文件权限

2. **编译错误**
   - 确认vcpkg包已正确安装
   - 检查CMake版本
   - 确认Visual Studio版本

3. **文件提取失败**
   - 检查输出目录权限
   - 确认磁盘空间充足
   - 检查文件名是否合法

### 调试模式

```bash
# 使用详细输出模式获取更多信息
wfs_leveldb_reader.exe C:\wfsdata -v

# 检查数据库统计信息
# 程序会输出详细的数据库分析结果
```

## 📞 技术支持

如果遇到问题，请检查：
1. 输出的错误信息和统计数据
2. WFS数据库的完整性
3. 系统环境和依赖库版本

---

**开发库版本**: v1.0  
**开发时间**: 2025年7月28日  
**适用范围**: WFS系统数据库读取和文件提取
