// 修复0x0800索引工具 - 更新WfsPathBean中的路径
package main

import (
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_0800 = []byte{0x00, 0x00}
	PATH_SEQ_0800 = []byte{0x01, 0x00}
)

type Fix0x0800Config struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type Fix0x0800Tool struct {
	config *Fix0x0800Config
	db     *leveldb.DB
	logger *log.Logger
}

func NewFix0x0800Tool(config *Fix0x0800Config) (*Fix0x0800Tool, error) {
	logger := log.New(os.Stdout, "[Fix0x0800] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &Fix0x0800Tool{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (ft *Fix0x0800Tool) Fix() error {
	ft.logger.Println("=== Fixing 0x0800 Index ===")

	// 1. 收集PATH_PRE信息（正确的文件名）
	correctPaths, err := ft.collectCorrectPaths()
	if err != nil {
		return fmt.Errorf("failed to collect correct paths: %v", err)
	}

	ft.logger.Printf("Found %d correct paths", len(correctPaths))

	// 2. 扫描并修复0x0800索引
	return ft.fix0x0800Index(correctPaths)
}

func (ft *Fix0x0800Tool) collectCorrectPaths() (map[int64]string, error) {
	ft.logger.Println("Collecting correct paths from PATH_PRE...")

	correctPaths := make(map[int64]string)
	iter := ft.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_0800[0] && key[1] == PATH_PRE_0800[1] {
			if len(key) > 2 {
				correctPath := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := ft.bytesToInt64(seqIDBytes)

				correctPaths[seqID] = correctPath

				if ft.config.Verbose {
					ft.logger.Printf("Correct path: seqID %d -> %s", seqID, correctPath)
				}
			}
		}
	}

	return correctPaths, nil
}

func (ft *Fix0x0800Tool) fix0x0800Index(correctPaths map[int64]string) error {
	ft.logger.Println("Scanning and fixing 0x0800 index...")

	prefix0x0800 := []byte{0x08, 0x00}
	iter := ft.db.NewIterator(nil, nil)
	defer iter.Release()

	batch := new(leveldb.Batch)
	fixedCount := 0

	for iter.Seek(prefix0x0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		value := iter.Value()

		// 解析当前的WfsPathBean
		currentPath, timestamp, err := ft.parseWfsPathBean(value)
		if err != nil {
			ft.logger.Printf("Failed to parse 0x0800 entry %s: %v", hex.EncodeToString(key), err)
			continue
		}

		// 从key中提取seqID（假设key格式为 0x0800 + 其他数据 + seqID）
		seqID := ft.extractSeqIDFromKey(key)
		if seqID == 0 {
			ft.logger.Printf("Failed to extract seqID from key %s", hex.EncodeToString(key))
			continue
		}

		// 检查是否需要修复
		correctPath, exists := correctPaths[seqID]
		if !exists {
			if ft.config.Verbose {
				ft.logger.Printf("No correct path found for seqID %d", seqID)
			}
			continue
		}

		if currentPath == correctPath {
			if ft.config.Verbose {
				ft.logger.Printf("0x0800 entry already correct: seqID %d -> %s", seqID, currentPath)
			}
			continue
		}

		// 需要修复
		if ft.config.Verbose {
			ft.logger.Printf("Fixing 0x0800: seqID %d, %s -> %s", seqID, currentPath, correctPath)
		}

		if ft.config.DryRun {
			ft.logger.Printf("[DRY RUN] Would fix 0x0800: seqID %d, %s -> %s", seqID, currentPath, correctPath)
		} else {
			// 创建新的WfsPathBean
			newPathBeanData := ft.encodeWfsPathBean(correctPath, timestamp)
			batch.Put(key, newPathBeanData)
		}

		fixedCount++
	}

	if fixedCount == 0 {
		ft.logger.Println("✅ All 0x0800 entries are already correct")
		return nil
	}

	ft.logger.Printf("Will fix %d 0x0800 entries", fixedCount)

	if ft.config.DryRun {
		ft.logger.Println("[DRY RUN] Would execute batch operation")
	} else {
		if err := ft.db.Write(batch, nil); err != nil {
			return fmt.Errorf("failed to write batch: %v", err)
		}
		ft.logger.Printf("✅ Successfully fixed %d 0x0800 entries", fixedCount)
	}

	return nil
}

func (ft *Fix0x0800Tool) extractSeqIDFromKey(key []byte) int64 {
	// 尝试不同的seqID提取方法

	// 方法1：假设seqID在key的最后8字节
	if len(key) >= 10 { // 0x0800 (2字节) + 其他数据 + seqID (8字节)
		seqIDBytes := key[len(key)-8:]
		seqID := ft.bytesToInt64(seqIDBytes)
		if seqID > 0 && seqID < 1000000 { // 合理的seqID范围
			return seqID
		}
	}

	// 方法2：假设seqID在key的第2-10字节
	if len(key) >= 10 {
		seqIDBytes := key[2:10]
		seqID := ft.bytesToInt64(seqIDBytes)
		if seqID > 0 && seqID < 1000000 {
			return seqID
		}
	}

	// 方法3：扫描key中的所有8字节组合
	for i := 2; i <= len(key)-8; i++ {
		seqIDBytes := key[i : i+8]
		seqID := ft.bytesToInt64(seqIDBytes)
		if seqID > 0 && seqID < 1000000 {
			return seqID
		}
	}

	return 0
}

// 辅助函数
func (ft *Fix0x0800Tool) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (ft *Fix0x0800Tool) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (ft *Fix0x0800Tool) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// Path字段 (field 1, wire type 2)
	if path != "" {
		result = append(result, 0x0A)
		pathBytes := []byte(path)
		result = append(result, byte(len(pathBytes)))
		result = append(result, pathBytes...)
	}

	// Timestamp字段 (field 2, wire type 0)
	if timestamp != 0 {
		result = append(result, 0x10)
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

func (ft *Fix0x0800Tool) Close() {
	if ft.db != nil {
		ft.db.Close()
	}
}

func main() {
	config := &Fix0x0800Config{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Fix 0x0800 Index Tool - Update WfsPathBean paths in 0x0800 index\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	tool, err := NewFix0x0800Tool(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer tool.Close()

	if err := tool.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
