// 文件提取器接口
// 负责将文件内容提取到磁盘

#pragma once

#include "datatype.hpp"
#include <filesystem>
#include <map>

namespace wfs
{

    // 文件提取器接口
    class FileExtractor_i
    {
    public:
        virtual ~FileExtractor_i() = default;

        // 设置输出目录
        virtual ErrorCode set_output_directory(const std::string &output_dir) = 0;

        // 提取单个文件
        virtual ErrorCode extract_file(const FileRecord &record) = 0;

        // 批量提取文件
        virtual ErrorCode extract_files(const std::map<int64_t, FileRecord> &records) = 0;

        // 获取提取统计信息
        virtual size_t get_extracted_count() const = 0;
        virtual size_t get_total_extracted_size() const = 0;
    };

    // 文件提取器实现
    class FileExtractor : public FileExtractor_i
    {
    private:
        std::filesystem::path output_directory_;
        size_t extracted_count_ = 0;
        size_t total_extracted_size_ = 0;

        // 内部方法
        ErrorCode ensure_directory_exists(const std::filesystem::path &dir);
        std::string sanitize_filename(const std::string &filename);
        std::filesystem::path generate_unique_path(const std::filesystem::path &base_path);
        ErrorCode write_file_content(const std::filesystem::path &file_path, const std::vector<uint8_t> &content);

    public:
        FileExtractor() = default;
        ~FileExtractor() override = default;

        // 实现接口方法
        ErrorCode set_output_directory(const std::string &output_dir) override;
        ErrorCode extract_file(const FileRecord &record) override;
        ErrorCode extract_files(const std::map<int64_t, FileRecord> &records) override;
        size_t get_extracted_count() const override;
        size_t get_total_extracted_size() const override;
    };

    // 工厂函数
    std::unique_ptr<FileExtractor_i> create_file_extractor();

} // namespace wfs
