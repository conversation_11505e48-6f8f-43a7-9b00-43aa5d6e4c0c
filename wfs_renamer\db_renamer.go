// WFS数据库直接重命名工具
// 基于fixdb项目，直接操作LevelDB进行文件重命名

package main

import (
	"bufio"
	"bytes"
	"crypto/md5"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00} // PATH_PRE前缀
	PATH_SEQ = []byte{0x01, 0x00} // PATH_SEQ前缀
)

// 数据库重命名器配置
type DBRenamerConfig struct {
	DatabasePath string // 数据库路径
	RulesFile    string // 规则文件（可选，如果不提供则自动生成规则）
	DryRun       bool   // 预览模式
	Verbose      bool   // 详细输出
	BackupDB     bool   // 备份数据库
	Workers      int    // 并发工作线程数
	AutoRules    bool   // 自动生成规则模式
}

// 重命名规则
type DBRenameRule struct {
	OldPath string
	NewPath string
}

// 数据库重命名器
type DBRenamer struct {
	config     *DBRenamerConfig
	db         *leveldb.DB
	logger     *log.Logger
	taskChan   chan DBRenameRule
	resultChan chan RenameTaskResult
	stats      struct {
		TotalRules   int64
		ProcessedPRE int64
		ProcessedSEQ int64
		Errors       int64
		Skipped      int64
		Deleted      int64
		Conflicts    int64
	}
}

// 重命名任务结果
type RenameTaskResult struct {
	Rule    DBRenameRule
	Success bool
	Action  string // "renamed", "deleted", "skipped", "error"
	Error   error
}

// 创建数据库重命名器
func NewDBRenamer(config *DBRenamerConfig) (*DBRenamer, error) {
	logger := log.New(os.Stdout, "[DBRenamer] ", log.LstdFlags)

	// 打开数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun, // 预览模式使用只读
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &DBRenamer{
		config:     config,
		db:         db,
		logger:     logger,
		taskChan:   make(chan DBRenameRule, config.Workers*2),
		resultChan: make(chan RenameTaskResult, config.Workers*2),
	}, nil
}

// 加载或生成重命名规则
func (dr *DBRenamer) loadRules() ([]DBRenameRule, error) {
	if dr.config.AutoRules {
		return dr.generateAutoRules()
	}
	return dr.loadRulesFromFile()
}

// 从文件加载重命名规则
func (dr *DBRenamer) loadRulesFromFile() ([]DBRenameRule, error) {
	file, err := os.Open(dr.config.RulesFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open rules file: %v", err)
	}
	defer file.Close()

	var rules []DBRenameRule
	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析规则
		var oldPath, newPath string
		if strings.Contains(line, "->") {
			parts := strings.Split(line, "->")
			if len(parts) != 2 {
				dr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
				continue
			}
			oldPath = strings.TrimSpace(parts[0])
			newPath = strings.TrimSpace(parts[1])
		} else if strings.Contains(line, ",") {
			parts := strings.Split(line, ",")
			if len(parts) != 2 {
				dr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
				continue
			}
			oldPath = strings.TrimSpace(parts[0])
			newPath = strings.TrimSpace(parts[1])
		} else {
			dr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
			continue
		}

		if oldPath == "" || newPath == "" {
			dr.logger.Printf("Warning: Empty path at line %d: %s", lineNum, line)
			continue
		}

		rules = append(rules, DBRenameRule{
			OldPath: oldPath,
			NewPath: newPath,
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading rules file: %v", err)
	}

	dr.logger.Printf("Loaded %d rename rules from file", len(rules))
	return rules, nil
}

// 自动生成重命名规则
func (dr *DBRenamer) generateAutoRules() ([]DBRenameRule, error) {
	dr.logger.Println("Generating automatic rename rules...")

	var rules []DBRenameRule
	existingFiles := make(map[string]bool) // 跟踪已存在的文件名

	// 扫描PATH_PRE索引获取所有文件路径
	iter := dr.db.NewIterator(nil, nil)
	defer iter.Release()

	// 首先收集所有现有的纯文件名（不包含路径分隔符的文件）
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])
				// 只有不包含路径分隔符的文件才算作已存在的纯文件名
				if !dr.containsPathSeparator(originalPath) {
					existingFiles[originalPath] = true
				}
			}
		}
	}

	// 重新扫描，生成重命名规则
	iter = dr.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])

				// 检查是否包含路径分隔符
				if dr.containsPathSeparator(originalPath) {
					fileName := dr.extractFileName(originalPath)

					// 检查目标文件名是否已存在
					if existingFiles[fileName] && fileName != originalPath {
						// 目标文件已存在，标记为删除
						dr.logger.Printf("Conflict detected: %s -> %s (target exists, will delete source)",
							originalPath, fileName)
						rules = append(rules, DBRenameRule{
							OldPath: originalPath,
							NewPath: "", // 空字符串表示删除
						})
					} else if fileName != originalPath {
						// 正常重命名
						rules = append(rules, DBRenameRule{
							OldPath: originalPath,
							NewPath: fileName,
						})
						// 标记新文件名为已存在
						existingFiles[fileName] = true
					}
				}
			}
		}
	}

	dr.logger.Printf("Generated %d automatic rename rules", len(rules))
	return rules, nil
}

// 检查路径是否包含分隔符
func (dr *DBRenamer) containsPathSeparator(path string) bool {
	return strings.Contains(path, "\\") || strings.Contains(path, "/")
}

// 提取文件名（去除路径）
func (dr *DBRenamer) extractFileName(path string) string {
	// 处理Windows和Unix路径分隔符
	path = strings.ReplaceAll(path, "\\", "/")
	parts := strings.Split(path, "/")
	return parts[len(parts)-1]
}

// 执行重命名
func (dr *DBRenamer) Execute() error {
	dr.logger.Println("=== Starting High-Concurrency Database Rename ===")

	// 加载规则
	rules, err := dr.loadRules()
	if err != nil {
		return fmt.Errorf("failed to load rules: %v", err)
	}

	if len(rules) == 0 {
		dr.logger.Println("No rename rules found")
		return nil
	}

	dr.stats.TotalRules = int64(len(rules))
	dr.logger.Printf("Total rename tasks: %d", len(rules))

	// 备份数据库（如果需要）
	if dr.config.BackupDB && !dr.config.DryRun {
		if err := dr.backupDatabase(); err != nil {
			return fmt.Errorf("failed to backup database: %v", err)
		}
	}

	// 启动高并发处理
	return dr.executeWithConcurrency(rules)
}

// 高并发执行重命名
func (dr *DBRenamer) executeWithConcurrency(rules []DBRenameRule) error {
	var wg sync.WaitGroup

	// 启动工作线程
	for i := 0; i < dr.config.Workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			dr.worker(workerID)
		}(i)
	}

	// 启动结果处理器
	go dr.resultProcessor()

	// 发送任务
	go func() {
		defer close(dr.taskChan)
		for _, rule := range rules {
			dr.taskChan <- rule
		}
	}()

	// 等待所有工作线程完成
	wg.Wait()
	close(dr.resultChan)

	// 等待结果处理完成
	time.Sleep(100 * time.Millisecond)

	// 打印统计信息
	dr.printStats()

	return nil
}

// 工作线程
func (dr *DBRenamer) worker(workerID int) {
	dr.logger.Printf("Worker %d started", workerID)
	defer dr.logger.Printf("Worker %d completed", workerID)

	for rule := range dr.taskChan {
		result := dr.processRuleWithResult(workerID, rule)
		dr.resultChan <- result
	}
}

// 处理规则并返回结果
func (dr *DBRenamer) processRuleWithResult(workerID int, rule DBRenameRule) RenameTaskResult {
	if dr.config.Verbose {
		dr.logger.Printf("Worker %d: Processing %s -> %s", workerID, rule.OldPath, rule.NewPath)
	}

	// 检查是否为删除操作
	if rule.NewPath == "" {
		return dr.processDeleteRule(workerID, rule)
	}

	// 正常重命名操作
	return dr.processRenameRule(workerID, rule)
}

// 处理删除规则
func (dr *DBRenamer) processDeleteRule(workerID int, rule DBRenameRule) RenameTaskResult {
	if dr.config.DryRun {
		dr.logger.Printf("Worker %d: [DRY RUN] Would delete %s (conflict resolution)",
			workerID, rule.OldPath)
		return RenameTaskResult{
			Rule:    rule,
			Success: true,
			Action:  "deleted",
			Error:   nil,
		}
	}

	// 执行删除操作
	if err := dr.deletePATH_PRE(rule.OldPath); err != nil {
		return RenameTaskResult{
			Rule:    rule,
			Success: false,
			Action:  "error",
			Error:   fmt.Errorf("failed to delete: %v", err),
		}
	}

	dr.logger.Printf("Worker %d: Deleted %s (conflict resolution)", workerID, rule.OldPath)
	return RenameTaskResult{
		Rule:    rule,
		Success: true,
		Action:  "deleted",
		Error:   nil,
	}
}

// 处理重命名规则
func (dr *DBRenamer) processRenameRule(workerID int, rule DBRenameRule) RenameTaskResult {
	if dr.config.DryRun {
		dr.logger.Printf("Worker %d: [DRY RUN] Would rename %s -> %s",
			workerID, rule.OldPath, rule.NewPath)
		return RenameTaskResult{
			Rule:    rule,
			Success: true,
			Action:  "renamed",
			Error:   nil,
		}
	}

	// 执行重命名操作
	if err := dr.processRule(rule); err != nil {
		return RenameTaskResult{
			Rule:    rule,
			Success: false,
			Action:  "error",
			Error:   err,
		}
	}

	dr.logger.Printf("Worker %d: Successfully renamed %s -> %s",
		workerID, rule.OldPath, rule.NewPath)
	return RenameTaskResult{
		Rule:    rule,
		Success: true,
		Action:  "renamed",
		Error:   nil,
	}
}

// 结果处理器
func (dr *DBRenamer) resultProcessor() {
	for result := range dr.resultChan {
		switch result.Action {
		case "renamed":
			if result.Success {
				atomic.AddInt64(&dr.stats.ProcessedPRE, 1)
			} else {
				atomic.AddInt64(&dr.stats.Errors, 1)
				dr.logger.Printf("Error: Failed to rename %s -> %s: %v",
					result.Rule.OldPath, result.Rule.NewPath, result.Error)
			}
		case "deleted":
			if result.Success {
				atomic.AddInt64(&dr.stats.Deleted, 1)
			} else {
				atomic.AddInt64(&dr.stats.Errors, 1)
				dr.logger.Printf("Error: Failed to delete %s: %v",
					result.Rule.OldPath, result.Error)
			}
		case "error":
			atomic.AddInt64(&dr.stats.Errors, 1)
			dr.logger.Printf("Error: Processing %s failed: %v",
				result.Rule.OldPath, result.Error)
		}

		// 显示进度
		processed := atomic.LoadInt64(&dr.stats.ProcessedPRE) +
			atomic.LoadInt64(&dr.stats.Deleted) +
			atomic.LoadInt64(&dr.stats.Errors)
		if processed%100 == 0 || dr.config.Verbose {
			dr.printProgress(processed)
		}
	}
}

// 打印进度
func (dr *DBRenamer) printProgress(processed int64) {
	total := atomic.LoadInt64(&dr.stats.TotalRules)
	renamed := atomic.LoadInt64(&dr.stats.ProcessedPRE)
	deleted := atomic.LoadInt64(&dr.stats.Deleted)
	errors := atomic.LoadInt64(&dr.stats.Errors)

	if total > 0 {
		percentage := float64(processed) / float64(total) * 100
		dr.logger.Printf("Progress: %d/%d (%.1f%%), %d renamed, %d deleted, %d errors",
			processed, total, percentage, renamed, deleted, errors)
	}
}

// 处理单个重命名规则
func (dr *DBRenamer) processRule(rule DBRenameRule) error {
	// 1. 处理PATH_PRE索引
	if err := dr.renamePATH_PRE(rule); err != nil {
		return fmt.Errorf("failed to rename PATH_PRE: %v", err)
	}

	// 2. 处理PATH_SEQ索引
	if err := dr.renamePATH_SEQ(rule); err != nil {
		return fmt.Errorf("failed to rename PATH_SEQ: %v", err)
	}

	// 3. 关键：处理指纹索引（WFS网页显示的关键）
	if err := dr.updateFingerprintIndex(rule); err != nil {
		return fmt.Errorf("failed to update fingerprint index: %v", err)
	}

	return nil
}

// 重命名PATH_PRE索引
func (dr *DBRenamer) renamePATH_PRE(rule DBRenameRule) error {
	oldKey := append(PATH_PRE, []byte(rule.OldPath)...)
	newKey := append(PATH_PRE, []byte(rule.NewPath)...)

	// 检查旧key是否存在
	seqIDBytes, err := dr.db.Get(oldKey, nil)
	if err != nil {
		if err == leveldb.ErrNotFound {
			dr.logger.Printf("PATH_PRE key not found: %s", rule.OldPath)
			atomic.AddInt64(&dr.stats.Skipped, 1)
			return nil
		}
		return fmt.Errorf("failed to get PATH_PRE key: %v", err)
	}

	// 检查新key是否已存在
	_, err = dr.db.Get(newKey, nil)
	if err == nil {
		dr.logger.Printf("Warning: Target PATH_PRE key already exists: %s", rule.NewPath)
		// 继续处理，删除旧key
	} else if err != leveldb.ErrNotFound {
		return fmt.Errorf("failed to check target PATH_PRE key: %v", err)
	}

	if dr.config.DryRun {
		dr.logger.Printf("[DRY RUN] Would rename PATH_PRE: %s -> %s", rule.OldPath, rule.NewPath)
	} else {
		// 创建批处理
		batch := new(leveldb.Batch)
		batch.Put(newKey, seqIDBytes)
		batch.Delete(oldKey)

		// 执行批处理
		if err := dr.db.Write(batch, nil); err != nil {
			return fmt.Errorf("failed to write PATH_PRE batch: %v", err)
		}

		dr.logger.Printf("Renamed PATH_PRE: %s -> %s", rule.OldPath, rule.NewPath)
	}

	atomic.AddInt64(&dr.stats.ProcessedPRE, 1)
	return nil
}

// 删除PATH_PRE索引
func (dr *DBRenamer) deletePATH_PRE(path string) error {
	oldKey := append(PATH_PRE, []byte(path)...)

	// 检查key是否存在
	_, err := dr.db.Get(oldKey, nil)
	if err != nil {
		if err == leveldb.ErrNotFound {
			dr.logger.Printf("PATH_PRE key not found for deletion: %s", path)
			atomic.AddInt64(&dr.stats.Skipped, 1)
			return nil
		}
		return fmt.Errorf("failed to check PATH_PRE key: %v", err)
	}

	if dr.config.DryRun {
		dr.logger.Printf("[DRY RUN] Would delete PATH_PRE: %s", path)
	} else {
		// 删除PATH_PRE条目
		if err := dr.db.Delete(oldKey, nil); err != nil {
			return fmt.Errorf("failed to delete PATH_PRE: %v", err)
		}
		dr.logger.Printf("Deleted PATH_PRE: %s", path)
	}

	return nil
}

// 重命名PATH_SEQ索引
func (dr *DBRenamer) renamePATH_SEQ(rule DBRenameRule) error {
	// 获取重命名后的seqID
	newPREKey := append(PATH_PRE, []byte(rule.NewPath)...)
	seqIDBytes, err := dr.db.Get(newPREKey, nil)
	if err != nil {
		if err == leveldb.ErrNotFound {
			dr.logger.Printf("PATH_SEQ: Cannot find seqID for renamed path: %s", rule.NewPath)
			return nil
		}
		return fmt.Errorf("failed to get seqID for renamed path: %v", err)
	}

	if dr.config.Verbose {
		dr.logger.Printf("PATH_SEQ: Found seqID %x for path %s", seqIDBytes, rule.NewPath)
	}

	// 尝试不同的PATH_SEQ key格式
	pathSeqKey := append(PATH_SEQ, seqIDBytes...)

	// 获取WfsPathBean数据
	pathBeanData, err := dr.db.Get(pathSeqKey, nil)
	if err != nil {
		if err == leveldb.ErrNotFound {
			// 尝试0x0800索引格式
			return dr.rename0800Index(seqIDBytes, rule)
		}
		return fmt.Errorf("failed to get PATH_SEQ data: %v", err)
	}

	// 解析并更新WfsPathBean
	oldPath, timestamp, err := dr.parseWfsPathBean(pathBeanData)
	if err != nil {
		return fmt.Errorf("failed to parse WfsPathBean: %v", err)
	}

	if dr.config.Verbose {
		dr.logger.Printf("PATH_SEQ: Found path=%s, timestamp=%d", oldPath, timestamp)
	}

	// 创建新的WfsPathBean
	newPathBeanData := dr.encodeWfsPathBean(rule.NewPath, timestamp)

	if dr.config.DryRun {
		dr.logger.Printf("[DRY RUN] Would update PATH_SEQ: %s -> %s", oldPath, rule.NewPath)
	} else {
		// 更新PATH_SEQ
		if err := dr.db.Put(pathSeqKey, newPathBeanData, nil); err != nil {
			return fmt.Errorf("failed to update PATH_SEQ: %v", err)
		}

		dr.logger.Printf("Updated PATH_SEQ: %s -> %s", oldPath, rule.NewPath)
	}

	atomic.AddInt64(&dr.stats.ProcessedSEQ, 1)
	return nil
}

// 重命名0x0800索引（WFS可能使用这种格式）
func (dr *DBRenamer) rename0800Index(seqIDBytes []byte, rule DBRenameRule) error {
	// 0x0800索引格式：0x0800 + 其他数据 + seqID
	prefix0800 := []byte{0x08, 0x00}

	// 扫描所有0x0800开头的key，查找包含此seqID的条目
	iter := dr.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if !bytes.HasPrefix(key, prefix0800) {
			break
		}

		// 检查key是否以seqID结尾
		if len(key) >= len(seqIDBytes) && bytes.HasSuffix(key, seqIDBytes) {
			value := iter.Value()

			// 解析WfsPathBean
			oldPath, timestamp, err := dr.parseWfsPathBean(value)
			if err != nil {
				continue
			}

			if dr.config.Verbose {
				dr.logger.Printf("0x0800: Found path=%s, timestamp=%d for seqID %x",
					oldPath, timestamp, seqIDBytes)
			}

			// 创建新的WfsPathBean
			newPathBeanData := dr.encodeWfsPathBean(rule.NewPath, timestamp)

			if dr.config.DryRun {
				dr.logger.Printf("[DRY RUN] Would update 0x0800: %s -> %s", oldPath, rule.NewPath)
			} else {
				// 更新0x0800索引
				if err := dr.db.Put(key, newPathBeanData, nil); err != nil {
					return fmt.Errorf("failed to update 0x0800 index: %v", err)
				}

				dr.logger.Printf("Updated 0x0800: %s -> %s", oldPath, rule.NewPath)
			}

			atomic.AddInt64(&dr.stats.ProcessedSEQ, 1)
			return nil
		}
	}

	dr.logger.Printf("No 0x0800 index found for seqID: %x", seqIDBytes)
	return nil
}

// 解析protobuf格式的WfsPathBean（简化版本）
func (dr *DBRenamer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 编码WfsPathBean为protobuf格式
func (dr *DBRenamer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// 编码Path字段 (field 1, wire type 2)
	if path != "" {
		// Tag: (1 << 3) | 2 = 10
		result = append(result, 0x0A)
		// Length
		pathBytes := []byte(path)
		result = append(result, byte(len(pathBytes)))
		// Data
		result = append(result, pathBytes...)
	}

	// 编码Timestamp字段 (field 2, wire type 0)
	if timestamp != 0 {
		// Tag: (2 << 3) | 0 = 16
		result = append(result, 0x10)
		// ZigZag编码
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		// Varint编码
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

// 更新指纹索引（关键：WFS网页显示的数据源）
func (dr *DBRenamer) updateFingerprintIndex(rule DBRenameRule) error {
	// 计算旧路径和新路径的指纹
	oldFingerprint := dr.calculateFingerprint(rule.OldPath)
	newFingerprint := dr.calculateFingerprint(rule.NewPath)

	if dr.config.Verbose {
		dr.logger.Printf("Fingerprint: %s -> %x, %s -> %x",
			rule.OldPath, oldFingerprint, rule.NewPath, newFingerprint)
	}

	// 获取旧指纹对应的文件内容ID
	fileContentID, err := dr.db.Get(oldFingerprint, nil)
	if err != nil {
		if err == leveldb.ErrNotFound {
			dr.logger.Printf("Warning: Fingerprint not found for %s", rule.OldPath)
			return nil
		}
		return fmt.Errorf("failed to get file content ID: %v", err)
	}

	if dr.config.DryRun {
		dr.logger.Printf("[DRY RUN] Would update fingerprint: %x -> %x", oldFingerprint, newFingerprint)
		return nil
	}

	// 创建批处理：删除旧指纹，添加新指纹
	batch := new(leveldb.Batch)
	batch.Put(newFingerprint, fileContentID)
	batch.Delete(oldFingerprint)

	if err := dr.db.Write(batch, nil); err != nil {
		return fmt.Errorf("failed to update fingerprint index: %v", err)
	}

	dr.logger.Printf("Updated fingerprint index: %s -> %s", rule.OldPath, rule.NewPath)
	return nil
}

// 计算文件路径的指纹（MD5哈希）
func (dr *DBRenamer) calculateFingerprint(path string) []byte {
	// WFS使用MD5作为文件路径的指纹
	hash := md5.Sum([]byte(path))
	return hash[:]
}

// 备份数据库
func (dr *DBRenamer) backupDatabase() error {
	backupPath := dr.config.DatabasePath + "_backup_" + time.Now().Format("20060102_150405")
	dr.logger.Printf("Creating database backup: %s", backupPath)

	// 这里应该实现数据库备份逻辑
	// 简化版本：提示用户手动备份
	dr.logger.Printf("Please manually backup your database before proceeding")
	dr.logger.Printf("Backup path suggestion: %s", backupPath)

	return nil
}

// 打印统计信息
func (dr *DBRenamer) printStats() {
	dr.logger.Println("\n=== High-Concurrency Rename Statistics ===")
	dr.logger.Printf("Total rules: %d", dr.stats.TotalRules)
	dr.logger.Printf("Files renamed: %d", dr.stats.ProcessedPRE)
	dr.logger.Printf("Files deleted (conflicts): %d", dr.stats.Deleted)
	dr.logger.Printf("PATH_SEQ processed: %d", dr.stats.ProcessedSEQ)
	dr.logger.Printf("Errors: %d", dr.stats.Errors)
	dr.logger.Printf("Skipped: %d", dr.stats.Skipped)
	dr.logger.Printf("Workers used: %d", dr.config.Workers)

	total_processed := dr.stats.ProcessedPRE + dr.stats.Deleted
	if dr.stats.TotalRules > 0 {
		success_rate := float64(total_processed) / float64(dr.stats.TotalRules) * 100
		dr.logger.Printf("Success rate: %.1f%%", success_rate)
	}

	if dr.stats.Errors > 0 {
		dr.logger.Printf("⚠️  %d errors occurred during processing", dr.stats.Errors)
	} else {
		dr.logger.Println("✅ All operations completed successfully!")
	}
}

// 关闭数据库
func (dr *DBRenamer) Close() {
	if dr.db != nil {
		dr.db.Close()
	}
}

// 主函数
func main() {
	config := &DBRenamerConfig{}

	// 命令行参数
	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.StringVar(&config.RulesFile, "rules", "", "Rename rules file (optional if using -auto)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode, don't actually rename")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.BoolVar(&config.BackupDB, "backup", false, "Backup database before rename")
	flag.IntVar(&config.Workers, "workers", 8, "Number of concurrent workers")
	flag.BoolVar(&config.AutoRules, "auto", false, "Auto-generate rules for path cleanup")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Database Renamer - High-Concurrency Path Cleanup Tool\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -auto -dry-run\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -auto -workers 16\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -rules rules.txt -backup\n", os.Args[0])
	}

	flag.Parse()

	// 验证参数
	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n\n")
		flag.Usage()
		os.Exit(1)
	}

	if !config.AutoRules && config.RulesFile == "" {
		fmt.Fprintf(os.Stderr, "Error: Either -rules or -auto parameter is required\n\n")
		flag.Usage()
		os.Exit(1)
	}

	// 创建重命名器
	renamer, err := NewDBRenamer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: Failed to create renamer: %v\n", err)
		os.Exit(1)
	}
	defer renamer.Close()

	// 执行重命名
	if err := renamer.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: Rename failed: %v\n", err)
		os.Exit(1)
	}
}
