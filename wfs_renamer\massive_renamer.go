// WFS大规模重命名工具 - 针对2亿级别数据的高性能实现
// 低内存开销，高并发处理，流式处理

package main

import (
	"crypto/md5"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00}
	PATH_SEQ = []byte{0x01, 0x00}
)

// 大规模重命名配置
type MassiveRenamerConfig struct {
	DatabasePath   string
	Workers        int
	BatchSize      int   // 批处理大小
	MemoryLimit    int64 // 内存限制（MB）
	DryRun         bool
	Verbose        bool
	ProgressReport int64 // 进度报告间隔
}

// 重命名任务
type MassiveRenameTask struct {
	OldPath string
	NewPath string
	SeqID   []byte
}

// 批处理任务
type BatchTask struct {
	Tasks []MassiveRenameTask
	ID    int64
}

// 大规模重命名器
type MassiveRenamer struct {
	config     *MassiveRenamerConfig
	db         *leveldb.DB
	logger     *log.Logger
	batchChan  chan BatchTask
	resultChan chan BatchResult
	stats      MassiveStats
	memMonitor *MemoryMonitor
}

// 批处理结果
type BatchResult struct {
	BatchID   int64
	Processed int64
	Errors    int64
	Duration  time.Duration
}

// 统计信息
type MassiveStats struct {
	StartTime        time.Time
	TotalTasks       int64
	ProcessedTasks   int64
	TotalBatches     int64
	ProcessedBatches int64
	Errors           int64
	BytesProcessed   int64
}

// 内存监控器
type MemoryMonitor struct {
	limit     int64
	current   int64
	threshold int64
}

// 创建大规模重命名器
func NewMassiveRenamer(config *MassiveRenamerConfig) (*MassiveRenamer, error) {
	logger := log.New(os.Stdout, "[MassiveRenamer] ", log.LstdFlags)

	// 优化数据库选项
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 512,
		BlockCacheCapacity:     64 * 1024 * 1024, // 64MB
		WriteBuffer:            16 * 1024 * 1024, // 16MB
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
		CompactionTableSize:    8 * 1024 * 1024,  // 8MB
		CompactionTotalSize:    64 * 1024 * 1024, // 64MB
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	// 创建内存监控器
	memMonitor := &MemoryMonitor{
		limit:     config.MemoryLimit * 1024 * 1024,            // 转换为字节
		threshold: config.MemoryLimit * 1024 * 1024 * 80 / 100, // 80%阈值
	}

	return &MassiveRenamer{
		config:     config,
		db:         db,
		logger:     logger,
		batchChan:  make(chan BatchTask, config.Workers),
		resultChan: make(chan BatchResult, config.Workers),
		memMonitor: memMonitor,
		stats: MassiveStats{
			StartTime: time.Now(),
		},
	}, nil
}

// 检查内存使用
func (mm *MemoryMonitor) checkMemory() bool {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	atomic.StoreInt64(&mm.current, int64(m.Alloc))
	return int64(m.Alloc) < mm.threshold
}

// 强制垃圾回收
func (mm *MemoryMonitor) forceGC() {
	runtime.GC()
	runtime.GC() // 两次GC确保清理
}

// 流式扫描数据库
func (mr *MassiveRenamer) streamScan() error {
	mr.logger.Println("Starting massive database scan...")

	iter := mr.db.NewIterator(nil, nil)
	defer iter.Release()

	var currentBatch []MassiveRenameTask
	batchID := int64(0)
	existingFiles := make(map[string]bool, mr.config.BatchSize*2)

	// 第一遍：收集纯文件名
	mr.logger.Println("Phase 1: Collecting existing pure filenames...")
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				if !mr.hasPathSeparator(path) {
					existingFiles[path] = true
				}
			}
		}

		// 内存检查
		if !mr.memMonitor.checkMemory() {
			mr.logger.Printf("Memory threshold reached, forcing GC...")
			mr.memMonitor.forceGC()
		}
	}

	mr.logger.Printf("Phase 1 complete: Found %d existing pure filenames", len(existingFiles))

	// 第二遍：生成重命名任务
	mr.logger.Println("Phase 2: Generating rename tasks...")
	iter = mr.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])

				if mr.hasPathSeparator(originalPath) {
					fileName := mr.extractFileName(originalPath)
					seqID := iter.Value()

					var task MassiveRenameTask
					task.OldPath = originalPath
					task.SeqID = seqID

					// 检查冲突
					if existingFiles[fileName] {
						// 跳过冲突文件（或标记删除）
						continue
					} else {
						task.NewPath = fileName
						existingFiles[fileName] = true
					}

					currentBatch = append(currentBatch, task)
					atomic.AddInt64(&mr.stats.TotalTasks, 1)

					// 批处理满了，发送
					if len(currentBatch) >= mr.config.BatchSize {
						batchTask := BatchTask{
							Tasks: currentBatch,
							ID:    batchID,
						}

						mr.batchChan <- batchTask
						atomic.AddInt64(&mr.stats.TotalBatches, 1)

						// 重置批处理
						currentBatch = make([]MassiveRenameTask, 0, mr.config.BatchSize)
						batchID++

						// 内存检查
						if !mr.memMonitor.checkMemory() {
							mr.logger.Printf("Memory threshold reached, forcing GC...")
							mr.memMonitor.forceGC()
						}
					}
				}
			}
		}
	}

	// 发送最后一个批处理
	if len(currentBatch) > 0 {
		batchTask := BatchTask{
			Tasks: currentBatch,
			ID:    batchID,
		}
		mr.batchChan <- batchTask
		atomic.AddInt64(&mr.stats.TotalBatches, 1)
	}

	close(mr.batchChan)
	mr.logger.Printf("Phase 2 complete: Generated %d tasks in %d batches",
		mr.stats.TotalTasks, mr.stats.TotalBatches)

	return nil
}

// 检查路径分隔符
func (mr *MassiveRenamer) hasPathSeparator(path string) bool {
	return strings.Contains(path, "\\") || strings.Contains(path, "/")
}

// 提取文件名
func (mr *MassiveRenamer) extractFileName(path string) string {
	path = strings.ReplaceAll(path, "\\", "/")
	parts := strings.Split(path, "/")
	return parts[len(parts)-1]
}

// 批处理工作线程
func (mr *MassiveRenamer) batchWorker(workerID int) {
	mr.logger.Printf("Batch worker %d started", workerID)
	defer mr.logger.Printf("Batch worker %d completed", workerID)

	for batchTask := range mr.batchChan {
		result := mr.processBatch(workerID, batchTask)
		mr.resultChan <- result
	}
}

// 处理批处理任务
func (mr *MassiveRenamer) processBatch(workerID int, batchTask BatchTask) BatchResult {
	startTime := time.Now()
	result := BatchResult{
		BatchID: batchTask.ID,
	}

	if mr.config.Verbose {
		mr.logger.Printf("Worker %d: Processing batch %d with %d tasks",
			workerID, batchTask.ID, len(batchTask.Tasks))
	}

	if mr.config.DryRun {
		// 预览模式
		for _, task := range batchTask.Tasks {
			mr.logger.Printf("Worker %d: [DRY RUN] Would rename %s -> %s",
				workerID, task.OldPath, task.NewPath)
		}
		result.Processed = int64(len(batchTask.Tasks))
	} else {
		// 执行批量重命名
		processed, errors := mr.executeBatchRename(workerID, batchTask.Tasks)
		result.Processed = processed
		result.Errors = errors
	}

	result.Duration = time.Since(startTime)
	return result
}

// 执行批量重命名
func (mr *MassiveRenamer) executeBatchRename(workerID int, tasks []MassiveRenameTask) (int64, int64) {
	var processed, errors int64

	// 创建大批量操作
	batch := new(leveldb.Batch)

	for _, task := range tasks {
		if err := mr.addRenameOperationToBatch(batch, task); err != nil {
			mr.logger.Printf("Worker %d: Error preparing task %s: %v",
				workerID, task.OldPath, err)
			errors++
		} else {
			processed++
		}
	}

	// 执行批量操作
	if err := mr.db.Write(batch, nil); err != nil {
		mr.logger.Printf("Worker %d: Batch write failed: %v", workerID, err)
		return 0, int64(len(tasks))
	}

	if mr.config.Verbose {
		mr.logger.Printf("Worker %d: Batch completed: %d processed, %d errors",
			workerID, processed, errors)
	}

	return processed, errors
}

// 添加重命名操作到批处理
func (mr *MassiveRenamer) addRenameOperationToBatch(batch *leveldb.Batch, task MassiveRenameTask) error {
	// 计算指纹
	oldFingerprint := mr.calculateFingerprint(task.OldPath)
	newFingerprint := mr.calculateFingerprint(task.NewPath)

	// 删除旧指纹和PATH_PRE
	batch.Delete(oldFingerprint)
	batch.Delete(append(PATH_PRE, []byte(task.OldPath)...))

	// 添加新PATH_PRE
	batch.Put(append(PATH_PRE, []byte(task.NewPath)...), task.SeqID)

	// 更新指纹索引
	if fileContentID, err := mr.db.Get(oldFingerprint, nil); err == nil {
		batch.Put(newFingerprint, fileContentID)
	}

	// 更新PATH_SEQ和0x0800索引
	mr.updatePathSeqInBatch(batch, task)

	return nil
}

// 在批处理中更新PATH_SEQ
func (mr *MassiveRenamer) updatePathSeqInBatch(batch *leveldb.Batch, task MassiveRenameTask) {
	seqID := mr.bytesToInt64(task.SeqID)
	pathseqkey := append(PATH_SEQ, mr.int64ToBytes(seqID)...)

	if pathBeanData, err := mr.db.Get(pathseqkey, nil); err == nil {
		if oldPath, timestamp, err := mr.parseWfsPathBean(pathBeanData); err == nil && oldPath == task.OldPath {
			newPathBeanData := mr.encodeWfsPathBean(task.NewPath, timestamp)
			batch.Put(pathseqkey, newPathBeanData)
		}
	}

	// 更新0x0800索引
	mr.update0800InBatch(batch, task)
}

// 在批处理中更新0x0800索引
func (mr *MassiveRenamer) update0800InBatch(batch *leveldb.Batch, task MassiveRenameTask) {
	// 简化版本：只处理能快速找到的0x0800条目
	prefix0800 := []byte{0x08, 0x00}

	// 构造可能的0x0800 key
	key0800 := append(prefix0800, task.SeqID...)
	if value, err := mr.db.Get(key0800, nil); err == nil {
		if oldPath, timestamp, err := mr.parseWfsPathBean(value); err == nil && oldPath == task.OldPath {
			newData := mr.encodeWfsPathBean(task.NewPath, timestamp)
			batch.Put(key0800, newData)
		}
	}
}

// 辅助函数
func (mr *MassiveRenamer) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func (mr *MassiveRenamer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		return 0
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (mr *MassiveRenamer) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

// 简化的protobuf解析
func (mr *MassiveRenamer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 编码WfsPathBean
func (mr *MassiveRenamer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// Path字段
	if path != "" {
		result = append(result, 0x0A)
		pathBytes := []byte(path)
		result = append(result, byte(len(pathBytes)))
		result = append(result, pathBytes...)
	}

	// Timestamp字段
	if timestamp != 0 {
		result = append(result, 0x10)
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

// 结果处理器
func (mr *MassiveRenamer) resultProcessor() {
	for result := range mr.resultChan {
		atomic.AddInt64(&mr.stats.ProcessedBatches, 1)
		atomic.AddInt64(&mr.stats.ProcessedTasks, result.Processed)
		atomic.AddInt64(&mr.stats.Errors, result.Errors)

		// 进度报告
		if mr.stats.ProcessedTasks%mr.config.ProgressReport == 0 {
			mr.printProgress()
		}
	}
}

// 打印进度
func (mr *MassiveRenamer) printProgress() {
	elapsed := time.Since(mr.stats.StartTime)
	processed := atomic.LoadInt64(&mr.stats.ProcessedTasks)
	total := atomic.LoadInt64(&mr.stats.TotalTasks)
	errors := atomic.LoadInt64(&mr.stats.Errors)

	if total > 0 {
		percentage := float64(processed) / float64(total) * 100
		rate := float64(processed) / elapsed.Seconds()

		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		memMB := m.Alloc / 1024 / 1024

		mr.logger.Printf("Progress: %d/%d (%.1f%%), %d errors, %.0f tasks/sec, %dMB memory, elapsed: %v",
			processed, total, percentage, errors, rate, memMB, elapsed.Truncate(time.Second))
	}
}

// 执行大规模重命名
func (mr *MassiveRenamer) Execute() error {
	mr.logger.Println("=== Starting Massive WFS Rename ===")
	mr.logger.Printf("Configuration: %d workers, %d batch size, %dMB memory limit",
		mr.config.Workers, mr.config.BatchSize, mr.config.MemoryLimit)

	// 启动工作线程
	var wg sync.WaitGroup
	for i := 0; i < mr.config.Workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			mr.batchWorker(workerID)
		}(i)
	}

	// 启动结果处理器
	go mr.resultProcessor()

	// 启动流式扫描
	go func() {
		if err := mr.streamScan(); err != nil {
			mr.logger.Printf("Stream scan error: %v", err)
		}
	}()

	// 等待所有工作线程完成
	wg.Wait()
	close(mr.resultChan)

	// 等待结果处理完成
	time.Sleep(100 * time.Millisecond)

	// 打印最终统计
	mr.printFinalStats()

	return nil
}

// 打印最终统计
func (mr *MassiveRenamer) printFinalStats() {
	elapsed := time.Since(mr.stats.StartTime)

	mr.logger.Println("\n=== Massive Rename Complete ===")
	mr.logger.Printf("Total tasks: %d", mr.stats.TotalTasks)
	mr.logger.Printf("Processed: %d", mr.stats.ProcessedTasks)
	mr.logger.Printf("Errors: %d", mr.stats.Errors)
	mr.logger.Printf("Total batches: %d", mr.stats.TotalBatches)
	mr.logger.Printf("Success rate: %.1f%%", float64(mr.stats.ProcessedTasks)/float64(mr.stats.TotalTasks)*100)
	mr.logger.Printf("Total time: %v", elapsed.Truncate(time.Second))
	mr.logger.Printf("Average rate: %.0f tasks/sec", float64(mr.stats.TotalTasks)/elapsed.Seconds())

	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	mr.logger.Printf("Peak memory: %dMB", m.TotalAlloc/1024/1024)
}

// 关闭
func (mr *MassiveRenamer) Close() {
	if mr.db != nil {
		mr.db.Close()
	}
}

// 主函数
func main() {
	config := &MassiveRenamerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.IntVar(&config.Workers, "workers", runtime.NumCPU(), "Number of workers")
	flag.IntVar(&config.BatchSize, "batch", 1000, "Batch size for processing")
	flag.Int64Var(&config.MemoryLimit, "memory", 1024, "Memory limit in MB")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.Int64Var(&config.ProgressReport, "progress", 10000, "Progress report interval")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Massive Renamer - For 200M+ records\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db /path/to/wfsdb -workers 16 -batch 5000 -memory 2048\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	renamer, err := NewMassiveRenamer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer renamer.Close()

	if err := renamer.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
