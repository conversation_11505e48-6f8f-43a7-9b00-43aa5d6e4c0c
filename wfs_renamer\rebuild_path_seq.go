// 重建PATH_SEQ索引工具 - 根据0x0800索引重建PATH_SEQ
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_SEQ_REBUILD = []byte{0x01, 0x00}
)

type PathSeqRebuilderConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type PathSeqRebuilder struct {
	config *PathSeqRebuilderConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewPathSeqRebuilder(config *PathSeqRebuilderConfig) (*PathSeqRebuilder, error) {
	logger := log.New(os.Stdout, "[PathSeqRebuilder] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &PathSeqRebuilder{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (psr *PathSeqRebuilder) Rebuild() error {
	psr.logger.Println("=== Rebuilding PATH_SEQ Index ===")

	// 1. 收集0x0800索引中的所有条目
	entries0x0800 := psr.collect0x0800Entries()

	// 2. 检查现有的PATH_SEQ索引
	existingPathSeq := psr.checkExistingPathSeq()

	// 3. 重建PATH_SEQ索引
	return psr.rebuildPathSeq(entries0x0800, existingPathSeq)
}

func (psr *PathSeqRebuilder) collect0x0800Entries() map[int64][]byte {
	psr.logger.Println("Collecting 0x0800 entries...")

	entries := make(map[int64][]byte)
	iter := psr.db.NewIterator(nil, nil)
	defer iter.Release()

	prefix0x0800 := []byte{0x08, 0x00}
	for iter.Seek(prefix0x0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		value := iter.Value()

		// 从key中提取seqID
		seqID := psr.extractSeqIDFromKey(key)
		if seqID > 0 {
			// 复制value数据
			pathBeanData := make([]byte, len(value))
			copy(pathBeanData, value)
			entries[seqID] = pathBeanData

			if psr.config.Verbose {
				path, timestamp, err := psr.parseWfsPathBean(pathBeanData)
				if err == nil {
					psr.logger.Printf("0x0800 entry: seqID=%d, path=%s, timestamp=%d", 
						seqID, path, timestamp)
				} else {
					psr.logger.Printf("0x0800 entry: seqID=%d, parse error: %v", seqID, err)
				}
			}
		}
	}

	psr.logger.Printf("Found %d 0x0800 entries", len(entries))
	return entries
}

func (psr *PathSeqRebuilder) checkExistingPathSeq() map[int64]bool {
	psr.logger.Println("Checking existing PATH_SEQ entries...")

	existing := make(map[int64]bool)
	iter := psr.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(PATH_SEQ_REBUILD); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != PATH_SEQ_REBUILD[0] || key[1] != PATH_SEQ_REBUILD[1] {
			break
		}

		if len(key) >= 10 { // PATH_SEQ (2字节) + seqID (8字节)
			seqIDBytes := key[2:]
			seqID := psr.bytesToInt64(seqIDBytes)
			existing[seqID] = true

			if psr.config.Verbose {
				psr.logger.Printf("Existing PATH_SEQ: seqID=%d", seqID)
			}
		}
	}

	psr.logger.Printf("Found %d existing PATH_SEQ entries", len(existing))
	return existing
}

func (psr *PathSeqRebuilder) rebuildPathSeq(entries0x0800 map[int64][]byte, existingPathSeq map[int64]bool) error {
	psr.logger.Println("Rebuilding PATH_SEQ index...")

	batch := new(leveldb.Batch)
	addedCount := 0

	for seqID, pathBeanData := range entries0x0800 {
		// 检查是否已经存在PATH_SEQ条目
		if existingPathSeq[seqID] {
			if psr.config.Verbose {
				psr.logger.Printf("PATH_SEQ already exists for seqID=%d", seqID)
			}
			continue
		}

		// 创建PATH_SEQ key
		pathSeqKey := append(PATH_SEQ_REBUILD, psr.int64ToBytes(seqID)...)

		// 添加到批处理
		batch.Put(pathSeqKey, pathBeanData)
		addedCount++

		if psr.config.Verbose {
			path, timestamp, err := psr.parseWfsPathBean(pathBeanData)
			if err == nil {
				psr.logger.Printf("Adding PATH_SEQ: seqID=%d, path=%s, timestamp=%d", 
					seqID, path, timestamp)
			} else {
				psr.logger.Printf("Adding PATH_SEQ: seqID=%d, parse error: %v", seqID, err)
			}
		}
	}

	if addedCount > 0 {
		if psr.config.DryRun {
			psr.logger.Printf("[DRY RUN] Would add %d PATH_SEQ entries", addedCount)
		} else {
			if err := psr.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			psr.logger.Printf("✅ Added %d PATH_SEQ entries", addedCount)
		}
	} else {
		psr.logger.Println("✅ All PATH_SEQ entries already exist")
	}

	return nil
}

func (psr *PathSeqRebuilder) extractSeqIDFromKey(key []byte) int64 {
	// 0x0800 key格式: 0x0800 (2字节) + 其他数据 + seqID (8字节)
	if len(key) >= 10 {
		seqIDBytes := key[len(key)-8:]
		seqID := psr.bytesToInt64(seqIDBytes)
		if seqID > 0 && seqID < 1000000 { // 合理的seqID范围
			return seqID
		}
	}
	return 0
}

func (psr *PathSeqRebuilder) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (psr *PathSeqRebuilder) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (psr *PathSeqRebuilder) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (psr *PathSeqRebuilder) Close() {
	if psr.db != nil {
		psr.db.Close()
	}
}

func main() {
	config := &PathSeqRebuilderConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "PATH_SEQ Index Rebuilder\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	rebuilder, err := NewPathSeqRebuilder(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer rebuilder.Close()

	if err := rebuilder.Rebuild(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
