// SQLite查看工具 - 查看文件名数据库内容
package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"os"

	_ "github.com/mattn/go-sqlite3"
)

type SQLiteViewerConfig struct {
	DatabasePath string
	ShowCount    bool
}

type SQLiteViewer struct {
	config *SQLiteViewerConfig
	db     *sql.DB
	logger *log.Logger
}

func NewSQLiteViewer(config *SQLiteViewerConfig) (*SQLiteViewer, error) {
	logger := log.New(os.Stdout, "[SQLiteViewer] ", log.LstdFlags)

	db, err := sql.Open("sqlite3", config.DatabasePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open SQLite database: %v", err)
	}

	return &SQLiteViewer{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (sv *SQLiteViewer) View() error {
	sv.logger.Printf("=== SQLite Database: %s ===", sv.config.DatabasePath)

	// 显示总数
	if err := sv.showCount(); err != nil {
		return err
	}

	// 显示所有文件名
	if err := sv.showFilenames(); err != nil {
		return err
	}

	return nil
}

func (sv *SQLiteViewer) showCount() error {
	query := `SELECT COUNT(*) FROM filenames`
	
	var count int
	err := sv.db.QueryRow(query).Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to count filenames: %v", err)
	}

	sv.logger.Printf("Total filenames: %d", count)
	return nil
}

func (sv *SQLiteViewer) showFilenames() error {
	sv.logger.Println("\n--- All Filenames ---")

	query := `SELECT filename FROM filenames ORDER BY filename`
	
	rows, err := sv.db.Query(query)
	if err != nil {
		return fmt.Errorf("failed to query filenames: %v", err)
	}
	defer rows.Close()

	index := 1
	for rows.Next() {
		var filename string
		if err := rows.Scan(&filename); err != nil {
			return fmt.Errorf("failed to scan filename: %v", err)
		}
		
		fmt.Printf("%d. %s\n", index, filename)
		index++
	}

	return nil
}

func (sv *SQLiteViewer) Close() {
	if sv.db != nil {
		sv.db.Close()
	}
}

func main() {
	config := &SQLiteViewerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "wfs_filenames.db3", "SQLite database path")
	flag.BoolVar(&config.ShowCount, "count", false, "Only show count")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "SQLite Viewer\n\n")
		fmt.Fprintf(os.Stderr, "查看WFS文件名SQLite数据库内容\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db wfs_filenames.db3\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db wfs_filenames.db3 -count\n", os.Args[0])
	}

	flag.Parse()

	// 检查数据库文件是否存在
	if _, err := os.Stat(config.DatabasePath); os.IsNotExist(err) {
		fmt.Fprintf(os.Stderr, "Error: Database file does not exist: %s\n", config.DatabasePath)
		os.Exit(1)
	}

	viewer, err := NewSQLiteViewer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer viewer.Close()

	if err := viewer.View(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
