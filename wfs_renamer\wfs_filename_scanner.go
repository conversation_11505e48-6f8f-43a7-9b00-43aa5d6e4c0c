// WFS文件名扫描工具 - 扫描LevelDB并提取文件名保存到SQLite
package main

import (
	"database/sql"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	_ "github.com/mattn/go-sqlite3"
)

type FilenameScannerConfig struct {
	DatabasePath string
	OutputPath   string
	Verbose      bool
}

type FilenameScanner struct {
	config   *FilenameScannerConfig
	db       *leveldb.DB
	sqliteDB *sql.DB
	logger   *log.Logger
}

func NewFilenameScanner(config *FilenameScannerConfig) (*FilenameScanner, error) {
	logger := log.New(os.Stdout, "[FilenameScanner] ", log.LstdFlags)

	// 打开LevelDB
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	var db *leveldb.DB
	var err error
	
	for i := 0; i < 5; i++ {
		db, err = leveldb.OpenFile(config.DatabasePath, options)
		if err == nil {
			break
		}
		logger.Printf("Attempt %d failed: %v, retrying in 1 second...", i+1, err)
		time.Sleep(1 * time.Second)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to open LevelDB after 5 attempts: %v", err)
	}

	// 打开SQLite数据库
	sqliteDB, err := sql.Open("sqlite3", config.OutputPath)
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to open SQLite database: %v", err)
	}

	return &FilenameScanner{
		config:   config,
		db:       db,
		sqliteDB: sqliteDB,
		logger:   logger,
	}, nil
}

func (fs *FilenameScanner) Scan() error {
	fs.logger.Println("=== WFS Filename Scan ===")

	// 创建SQLite表
	if err := fs.createTable(); err != nil {
		return fmt.Errorf("failed to create table: %v", err)
	}

	// 扫描并提取文件名
	return fs.scanFilenames()
}

func (fs *FilenameScanner) createTable() error {
	fs.logger.Println("Creating SQLite table...")

	createTableSQL := `
	CREATE TABLE IF NOT EXISTS filenames (
		filename TEXT PRIMARY KEY
	);
	`

	_, err := fs.sqliteDB.Exec(createTableSQL)
	return err
}

func (fs *FilenameScanner) scanFilenames() error {
	fs.logger.Println("Scanning LevelDB for filenames...")

	// 准备插入语句
	insertSQL := `INSERT OR IGNORE INTO filenames (filename) VALUES (?)`
	stmt, err := fs.sqliteDB.Prepare(insertSQL)
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement: %v", err)
	}
	defer stmt.Close()

	// 开始事务
	tx, err := fs.sqliteDB.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 用于去重的map
	filenameSet := make(map[string]bool)

	// 扫描所有条目
	iter := fs.db.NewIterator(nil, nil)
	defer iter.Release()

	scannedCount := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 从不同类型的索引中提取文件名
		filenames := fs.extractFilenames(key, value)
		
		for _, filename := range filenames {
			if filename != "" && !filenameSet[filename] {
				filenameSet[filename] = true
				
				_, err := tx.Stmt(stmt).Exec(filename)
				if err != nil {
					return fmt.Errorf("failed to insert filename: %v", err)
				}

				if fs.config.Verbose {
					fs.logger.Printf("Found filename: %s", filename)
				}
			}
		}

		scannedCount++
		if fs.config.Verbose && scannedCount%100 == 0 {
			fs.logger.Printf("Scanned %d entries...", scannedCount)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	fs.logger.Printf("✅ Scanned %d entries and found %d unique filenames", scannedCount, len(filenameSet))
	return nil
}

func (fs *FilenameScanner) extractFilenames(key, value []byte) []string {
	var filenames []string

	// 检查PATH_PRE索引 (0x0000)
	if len(key) >= 2 && key[0] == 0x00 && key[1] == 0x00 {
		if len(key) > 2 {
			path := string(key[2:])
			filename := fs.extractFilename(path)
			if filename != "" {
				filenames = append(filenames, filename)
			}
		}
	}

	// 检查PATH_SEQ索引 (0x0100) - 从WfsPathBean中提取
	if len(key) >= 2 && key[0] == 0x01 && key[1] == 0x00 {
		if path, _, err := fs.parseWfsPathBean(value); err == nil && path != "" {
			filename := fs.extractFilename(path)
			if filename != "" {
				filenames = append(filenames, filename)
			}
		}
	}

	// 检查0x0800索引 - 从WfsPathBean中提取
	if len(key) >= 2 && key[0] == 0x08 && key[1] == 0x00 {
		if path, _, err := fs.parseWfsPathBean(value); err == nil && path != "" {
			filename := fs.extractFilename(path)
			if filename != "" {
				filenames = append(filenames, filename)
			}
		}
	}

	return filenames
}

func (fs *FilenameScanner) extractFilename(path string) string {
	if path == "" {
		return ""
	}
	
	// 标准化路径分隔符
	path = strings.ReplaceAll(path, "\\", "/")
	
	// 获取文件名（包含扩展名）
	filename := filepath.Base(path)
	
	// 过滤掉明显不是文件的条目
	if filename == "." || filename == ".." || filename == "/" || filename == "\\" {
		return ""
	}
	
	// 过滤掉没有扩展名的条目（可能是目录）
	if !strings.Contains(filename, ".") {
		return ""
	}
	
	return filename
}

func (fs *FilenameScanner) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (fs *FilenameScanner) Close() {
	if fs.db != nil {
		fs.db.Close()
	}
	if fs.sqliteDB != nil {
		fs.sqliteDB.Close()
	}
}

func main() {
	config := &FilenameScannerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "LevelDB database path (required)")
	flag.StringVar(&config.OutputPath, "output", "wfs_filenames.db3", "SQLite output file path (.db3)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Filename Scanner\n\n")
		fmt.Fprintf(os.Stderr, "扫描WFS LevelDB数据库，提取所有文件名并保存到SQLite数据库\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -output filenames.db3 -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	// 确保输出文件有.db3扩展名
	if !strings.HasSuffix(config.OutputPath, ".db3") {
		config.OutputPath = strings.TrimSuffix(config.OutputPath, filepath.Ext(config.OutputPath)) + ".db3"
	}

	scanner, err := NewFilenameScanner(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer scanner.Close()

	if err := scanner.Scan(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ 文件名扫描完成，结果保存到: %s\n", config.OutputPath)
}
