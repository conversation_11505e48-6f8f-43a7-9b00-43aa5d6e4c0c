// WFS数据类型定义
// 定义WFS系统中使用的数据结构

#pragma once

#include <string>
#include <vector>
#include <cstdint>
#include <memory>

namespace wfs {

// WFS索引前缀常量
struct IndexPrefixes {
    static constexpr uint16_t PATH_PRE = 0x0000;   // PATH_PRE索引前缀
    static constexpr uint16_t PATH_SEQ = 0x0100;   // PATH_SEQ索引前缀
    static constexpr uint16_t INDEX_0800 = 0x0800; // 0x0800索引前缀
};

// 文件记录结构
struct FileRecord {
    int64_t seq_id = 0;
    std::string original_path;
    std::string file_name;
    std::vector<uint8_t> content_data;
    int64_t timestamp = 0;
    size_t content_size = 0;
    
    FileRecord() = default;
    FileRecord(int64_t id, const std::string& orig_path, const std::string& name)
        : seq_id(id), original_path(orig_path), file_name(name) {}
};

// WFS路径Bean结构（对应protobuf WfsPathBean）
struct WfsPathBean {
    std::string path;
    int64_t timestamp = 0;
    
    WfsPathBean() = default;
    WfsPathBean(const std::string& p, int64_t ts) : path(p), timestamp(ts) {}
};

// 数据库统计信息
struct DatabaseStats {
    size_t total_entries = 0;
    size_t path_pre_entries = 0;
    size_t path_seq_entries = 0;
    size_t index_0800_entries = 0;
    size_t content_entries = 0;
    size_t extracted_files = 0;
    size_t total_content_size = 0;
    
    void reset() {
        total_entries = 0;
        path_pre_entries = 0;
        path_seq_entries = 0;
        index_0800_entries = 0;
        content_entries = 0;
        extracted_files = 0;
        total_content_size = 0;
    }
};

// 配置结构
struct ReaderConfig {
    std::string wfsdata_path;           // wfsdata目录路径
    std::string output_directory;       // 输出目录
    bool extract_files = true;          // 是否提取文件到磁盘
    bool verbose_logging = true;        // 详细日志
    bool create_test_content = false;   // 创建测试内容（当文件内容缺失时）
    size_t max_file_size = 100 * 1024 * 1024; // 最大文件大小限制（100MB）
    
    ReaderConfig() = default;
    ReaderConfig(const std::string& wfs_path, const std::string& output_path)
        : wfsdata_path(wfs_path), output_directory(output_path) {}
};

// 错误类型枚举
enum class ErrorCode {
    SUCCESS = 0,
    DATABASE_OPEN_FAILED,
    INVALID_PATH,
    PARSE_ERROR,
    FILE_WRITE_ERROR,
    CONTENT_NOT_FOUND,
    UNKNOWN_ERROR
};

// 结果结构
template<typename T>
struct Result {
    ErrorCode error_code = ErrorCode::SUCCESS;
    std::string error_message;
    T data;
    
    Result() = default;
    Result(const T& d) : data(d) {}
    Result(ErrorCode code, const std::string& msg) : error_code(code), error_message(msg) {}
    
    bool is_success() const { return error_code == ErrorCode::SUCCESS; }
    bool is_error() const { return error_code != ErrorCode::SUCCESS; }
};

using StringResult = Result<std::string>;
using FileRecordResult = Result<FileRecord>;
using WfsPathBeanResult = Result<WfsPathBean>;

} // namespace wfs
