// WFS数据库导出到CSV工具
package main

import (
	"crypto/md5"
	"encoding/binary"
	"encoding/csv"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_PREFIX = []byte{0x00, 0x00}
	PATH_SEQ_PREFIX = []byte{0x01, 0x00}
)

type WFSRecord struct {
	OriginalPath   string
	CleanFileName  string
	SeqID          int64
	SeqIDHex       string
	HasPATH_PRE    bool
	HasPATH_SEQ    bool
	PATH_SEQData   string
	Has0x0800      bool
	HasFingerprint bool
	FingerprintHex string
	ContentIDHex   string
	Timestamp      int64
	IsComplete     bool
}

type WFSToCSV struct {
	wfsDB  *leveldb.DB
	logger *log.Logger
}

func NewWFSToCSV(wfsPath string) (*WFSToCSV, error) {
	logger := log.New(os.Stdout, "[WFSToCSV] ", log.LstdFlags)

	// 打开WFS数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	wfsDB, err := leveldb.OpenFile(wfsPath, options)
	if err != nil {
		logger.Printf("Failed to open WFS DB normally, attempting recovery...")
		wfsDB, err = leveldb.RecoverFile(wfsPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover WFS database: %v", err)
		}
	}

	return &WFSToCSV{
		wfsDB:  wfsDB,
		logger: logger,
	}, nil
}

func (wtc *WFSToCSV) Export(csvPath string) error {
	wtc.logger.Println("=== Starting WFS to CSV Export ===")

	// 收集所有文件信息
	records, err := wtc.collectAllRecords()
	if err != nil {
		return fmt.Errorf("failed to collect records: %v", err)
	}

	wtc.logger.Printf("Collected %d records", len(records))

	// 写入CSV文件
	if err := wtc.writeCSV(csvPath, records); err != nil {
		return fmt.Errorf("failed to write CSV: %v", err)
	}

	wtc.logger.Println("✅ Export completed successfully")
	return nil
}

func (wtc *WFSToCSV) collectAllRecords() ([]WFSRecord, error) {
	wtc.logger.Println("Collecting all WFS records...")

	records := make(map[string]*WFSRecord)
	timestampMap := make(map[string]int64)

	// 1. 从0x0800索引获取时间戳
	wtc.logger.Println("Phase 1: Scanning 0x0800 index for timestamps...")
	prefix0800 := []byte{0x08, 0x00}
	iter := wtc.wfsDB.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		value := iter.Value()
		if path, timestamp, err := wtc.parseWfsPathBean(value); err == nil {
			timestampMap[path] = timestamp
		}
	}

	wtc.logger.Printf("Found %d timestamp entries", len(timestampMap))

	// 2. 扫描PATH_PRE索引
	wtc.logger.Println("Phase 2: Scanning PATH_PRE index...")
	iter = wtc.wfsDB.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_PREFIX[0] && key[1] == PATH_PRE_PREFIX[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := wtc.bytesToInt64(seqIDBytes)

				record := &WFSRecord{
					OriginalPath:  originalPath,
					CleanFileName: wtc.cleanFileName(originalPath),
					SeqID:         seqID,
					SeqIDHex:      hex.EncodeToString(seqIDBytes),
					HasPATH_PRE:   true,
					Timestamp:     timestampMap[originalPath],
				}

				records[originalPath] = record
			}
		}
	}

	wtc.logger.Printf("Found %d PATH_PRE entries", len(records))

	// 3. 检查PATH_SEQ索引
	wtc.logger.Println("Phase 3: Checking PATH_SEQ index...")
	for path, record := range records {
		seqIDBytes := wtc.int64ToBytes(record.SeqID)
		pathSeqKey := append(PATH_SEQ_PREFIX, seqIDBytes...)

		if data, err := wtc.wfsDB.Get(pathSeqKey, nil); err == nil {
			record.HasPATH_SEQ = true
			record.PATH_SEQData = hex.EncodeToString(data)

			if seqPath, timestamp, err := wtc.parseWfsPathBean(data); err == nil {
				if timestamp != 0 && record.Timestamp == 0 {
					record.Timestamp = timestamp
				}
				// 验证路径一致性
				if seqPath != path {
					wtc.logger.Printf("⚠️  PATH_SEQ path mismatch: %s vs %s", path, seqPath)
				}
			}
		}
	}

	// 4. 检查0x0800索引
	wtc.logger.Println("Phase 4: Checking 0x0800 index...")
	for path, record := range records {
		if _, exists := timestampMap[path]; exists {
			record.Has0x0800 = true
		}
	}

	// 5. 检查指纹索引
	wtc.logger.Println("Phase 5: Checking fingerprint index...")
	for path, record := range records {
		fingerprint := wtc.calculateFingerprint(path)
		record.FingerprintHex = hex.EncodeToString(fingerprint)

		if contentID, err := wtc.wfsDB.Get(fingerprint, nil); err == nil {
			record.HasFingerprint = true
			record.ContentIDHex = hex.EncodeToString(contentID)
		}
	}

	// 6. 确定完整性
	for _, record := range records {
		record.IsComplete = record.HasPATH_PRE && record.HasPATH_SEQ && record.HasFingerprint
	}

	// 转换为切片
	var result []WFSRecord
	for _, record := range records {
		result = append(result, *record)
	}

	return result, nil
}

func (wtc *WFSToCSV) writeCSV(csvPath string, records []WFSRecord) error {
	wtc.logger.Printf("Writing CSV file: %s", csvPath)

	file, err := os.Create(csvPath)
	if err != nil {
		return fmt.Errorf("failed to create CSV file: %v", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入标题行
	headers := []string{
		"original_path",
		"clean_filename", 
		"seq_id",
		"seq_id_hex",
		"has_path_pre",
		"has_path_seq",
		"path_seq_data",
		"has_0x0800",
		"has_fingerprint",
		"fingerprint_hex",
		"content_id_hex",
		"timestamp",
		"is_complete",
	}

	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("failed to write headers: %v", err)
	}

	// 写入数据行
	for _, record := range records {
		row := []string{
			record.OriginalPath,
			record.CleanFileName,
			strconv.FormatInt(record.SeqID, 10),
			record.SeqIDHex,
			strconv.FormatBool(record.HasPATH_PRE),
			strconv.FormatBool(record.HasPATH_SEQ),
			record.PATH_SEQData,
			strconv.FormatBool(record.Has0x0800),
			strconv.FormatBool(record.HasFingerprint),
			record.FingerprintHex,
			record.ContentIDHex,
			strconv.FormatInt(record.Timestamp, 10),
			strconv.FormatBool(record.IsComplete),
		}

		if err := writer.Write(row); err != nil {
			return fmt.Errorf("failed to write record: %v", err)
		}
	}

	wtc.logger.Printf("Successfully wrote %d records to CSV", len(records))
	return nil
}

func (wtc *WFSToCSV) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	parts := strings.Split(path, "/")
	return parts[len(parts)-1]
}

// 辅助函数
func (wtc *WFSToCSV) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wtc *WFSToCSV) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (wtc *WFSToCSV) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func (wtc *WFSToCSV) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wtc *WFSToCSV) Close() {
	if wtc.wfsDB != nil {
		wtc.wfsDB.Close()
	}
}

func main() {
	var wfsPath, csvPath string
	var verbose bool

	flag.StringVar(&wfsPath, "wfs", "", "WFS database path (required)")
	flag.StringVar(&csvPath, "csv", "", "CSV output path (optional, default: wfs_export.csv)")
	flag.BoolVar(&verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS to CSV Exporter\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -wfs C:\\wfsdata\\wfsdb\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -wfs C:\\wfsdata\\wfsdb -csv wfs_data.csv\n", os.Args[0])
	}

	flag.Parse()

	if wfsPath == "" {
		fmt.Fprintf(os.Stderr, "Error: -wfs parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	if csvPath == "" {
		csvPath = filepath.Join(filepath.Dir(wfsPath), "wfs_export.csv")
	}

	exporter, err := NewWFSToCSV(wfsPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer exporter.Close()

	if err := exporter.Export(csvPath); err != nil {
		fmt.Fprintf(os.Stderr, "Error during export: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Export completed successfully: %s\n", csvPath)
}
