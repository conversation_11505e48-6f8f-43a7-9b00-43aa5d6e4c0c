// 直接修复0x0800索引工具
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type Fix0x0800DirectConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type Fix0x0800DirectTool struct {
	config *Fix0x0800DirectConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewFix0x0800DirectTool(config *Fix0x0800DirectConfig) (*Fix0x0800DirectTool, error) {
	logger := log.New(os.Stdout, "[Fix0x0800Direct] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &Fix0x0800DirectTool{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (fdt *Fix0x0800DirectTool) Fix() error {
	fdt.logger.Println("=== Direct Fix 0x0800 Index ===")

	prefix0x0800 := []byte{0x08, 0x00}
	iter := fdt.db.NewIterator(nil, nil)
	defer iter.Release()

	batch := new(leveldb.Batch)
	fixedCount := 0

	for iter.Seek(prefix0x0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		value := iter.Value()

		// 解析WfsPathBean
		path, timestamp, err := fdt.parseWfsPathBean(value)
		if err != nil {
			fdt.logger.Printf("Warning: Failed to parse 0x0800 entry: %v", err)
			continue
		}

		// 清理路径
		cleanPath := fdt.cleanFileName(path)
		if path != cleanPath {
			// 创建新的WfsPathBean
			newValue := fdt.encodeWfsPathBean(cleanPath, timestamp)

			if fdt.config.Verbose {
				fdt.logger.Printf("Fixing 0x0800: %s -> %s", path, cleanPath)
			}

			if fdt.config.DryRun {
				fdt.logger.Printf("[DRY RUN] Would fix: %s -> %s", path, cleanPath)
			} else {
				batch.Put(key, newValue)
			}
			fixedCount++
		} else {
			if fdt.config.Verbose {
				fdt.logger.Printf("Already clean: %s", path)
			}
		}
	}

	if fixedCount > 0 {
		if fdt.config.DryRun {
			fdt.logger.Printf("[DRY RUN] Would fix %d 0x0800 entries", fixedCount)
		} else {
			if err := fdt.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			fdt.logger.Printf("✅ Fixed %d 0x0800 entries", fixedCount)
		}
	} else {
		fdt.logger.Println("✅ All 0x0800 entries are already clean")
	}

	return nil
}

func (fdt *Fix0x0800DirectTool) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	return filepath.Base(path)
}

func (fdt *Fix0x0800DirectTool) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (fdt *Fix0x0800DirectTool) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// Path字段 (field 1, wire type 2)
	if path != "" {
		result = append(result, 0x0A) // tag: field 1, wire type 2
		pathBytes := []byte(path)
		
		// 编码长度
		length := len(pathBytes)
		for length >= 0x80 {
			result = append(result, byte(length)|0x80)
			length >>= 7
		}
		result = append(result, byte(length))
		
		// 添加路径数据
		result = append(result, pathBytes...)
	}

	// Timestamp字段 (field 2, wire type 0)
	if timestamp != 0 {
		result = append(result, 0x10) // tag: field 2, wire type 0
		
		// 使用zigzag编码
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

func (fdt *Fix0x0800DirectTool) Close() {
	if fdt.db != nil {
		fdt.db.Close()
	}
}

func main() {
	config := &Fix0x0800DirectConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Fix 0x0800 Direct Tool\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	tool, err := NewFix0x0800DirectTool(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer tool.Close()

	if err := tool.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
