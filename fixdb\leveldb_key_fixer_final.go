// LevelDB Key Fixer for WFS System - Final Integrated Version
// 完整的WFS系统LevelDB数据库修复和验证工具

package main

import (
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE   = []byte{0x00, 0x00} // PATH_PRE前缀
	PATH_SEQ   = []byte{0x01, 0x00} // PATH_SEQ前缀
	INDEX_0800 = []byte{0x08, 0x00} // 第三索引前缀
)

// 文件信息结构
type FileInfo struct {
	SeqID     int64
	FileName  string
	FullPath  string
	Timestamp int64
}

// 修复器
type LevelDBFixer struct {
	db     *leveldb.DB
	logger *log.Logger
	dryRun bool
}

// 创建修复器
func NewLevelDBFixer(dbPath string, dryRun bool) (*LevelDBFixer, error) {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     64 * 1024 * 1024,
		WriteBuffer:            16 * 1024 * 1024,
		ReadOnly:               dryRun,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}

	return &LevelDBFixer{
		db:     db,
		logger: log.New(os.Stdout, "[LevelDBFixer] ", log.LstdFlags),
		dryRun: dryRun,
	}, nil
}

// 关闭修复器
func (f *LevelDBFixer) Close() error {
	if f.db != nil {
		return f.db.Close()
	}
	return nil
}

// 解析protobuf格式的WfsPathBean
func (f *LevelDBFixer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 编码protobuf格式的WfsPathBean
func (f *LevelDBFixer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var buf []byte

	// 编码Path字段 (field 1, wire type 2)
	pathBytes := []byte(path)
	tag1 := (1 << 3) | 2
	buf = append(buf, byte(tag1))

	lengthBuf := make([]byte, binary.MaxVarintLen64)
	n := binary.PutUvarint(lengthBuf, uint64(len(pathBytes)))
	buf = append(buf, lengthBuf[:n]...)
	buf = append(buf, pathBytes...)

	// 编码Timestramp字段 (field 2, wire type 0)
	tag2 := (2 << 3) | 0
	buf = append(buf, byte(tag2))

	timestampBuf := make([]byte, binary.MaxVarintLen64)
	n = binary.PutVarint(timestampBuf, timestamp)
	buf = append(buf, timestampBuf[:n]...)

	return buf
}

// 检查路径是否需要修复
func (f *LevelDBFixer) needsFix(path string) (bool, string) {
	if strings.Contains(path, "/") || strings.Contains(path, "\\") {
		return true, filepath.Base(path)
	}
	return false, ""
}

// 全面分析数据库状态
func (f *LevelDBFixer) Analyze() error {
	f.logger.Println("=== WFS Database Analysis ===")

	// 分析PATH_PRE索引
	pathPreCount, pathPreProblems := f.analyzePATH_PRE()

	// 分析PATH_SEQ索引
	pathSeqCount, pathSeqProblems := f.analyzePATH_SEQ()

	// 分析0x0800索引
	index0800Count, index0800Problems := f.analyzeIndex0800()

	// 生成总结报告
	f.logger.Println("\n=== Analysis Summary ===")
	f.logger.Printf("PATH_PRE Index: %d entries, %d problems", pathPreCount, pathPreProblems)
	f.logger.Printf("PATH_SEQ Index: %d entries, %d problems", pathSeqCount, pathSeqProblems)
	f.logger.Printf("0x0800 Index: %d entries, %d problems", index0800Count, index0800Problems)

	totalProblems := pathPreProblems + pathSeqProblems + index0800Problems
	if totalProblems == 0 {
		f.logger.Println("✅ Database is healthy - no path problems found!")
		f.logger.Println("✅ All file names are correctly formatted")
		f.logger.Println("✅ WFS web interface should display correct file names")
	} else {
		f.logger.Printf("⚠️  Found %d total problems that need fixing", totalProblems)
		f.logger.Println("💡 Use -fix option to repair these issues")
	}

	return nil
}

// 分析PATH_PRE索引
func (f *LevelDBFixer) analyzePATH_PRE() (int, int) {
	f.logger.Println("\n--- PATH_PRE Index Analysis ---")

	iter := f.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	count := 0
	problems := 0

	for iter.Next() {
		key := iter.Key()
		_ = iter.Value() // seqID value not needed for analysis

		if len(key) <= len(PATH_PRE) {
			continue
		}

		path := string(key[len(PATH_PRE):])
		count++

		needsFix, newFileName := f.needsFix(path)
		if needsFix {
			problems++
			f.logger.Printf("❌ PROBLEM: %s -> should be: %s", path, newFileName)
		} else {
			f.logger.Printf("✅ OK: %s", path)
		}
	}

	return count, problems
}

// 分析PATH_SEQ索引
func (f *LevelDBFixer) analyzePATH_SEQ() (int, int) {
	f.logger.Println("\n--- PATH_SEQ Index Analysis ---")

	iter := f.db.NewIterator(levelutil.BytesPrefix(PATH_SEQ), nil)
	defer iter.Release()

	count := 0
	problems := 0

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_SEQ) {
			continue
		}

		count++

		path, timestamp, err := f.parseWfsPathBean(value)
		if err != nil {
			problems++
			f.logger.Printf("❌ PARSE ERROR: %v", err)
			continue
		}

		needsFix, newFileName := f.needsFix(path)
		if needsFix {
			problems++
			f.logger.Printf("❌ PROBLEM: %s -> should be: %s (timestamp: %d)", path, newFileName, timestamp)
		} else {
			f.logger.Printf("✅ OK: %s (timestamp: %d)", path, timestamp)
		}
	}

	return count, problems
}

// 分析0x0800索引
func (f *LevelDBFixer) analyzeIndex0800() (int, int) {
	f.logger.Println("\n--- 0x0800 Index Analysis ---")

	iter := f.db.NewIterator(levelutil.BytesPrefix(INDEX_0800), nil)
	defer iter.Release()

	count := 0
	problems := 0

	for iter.Next() {
		_ = iter.Key() // key not needed for analysis
		value := iter.Value()

		count++

		path, timestamp, err := f.parseWfsPathBean(value)
		if err != nil {
			problems++
			f.logger.Printf("❌ PARSE ERROR: %v", err)
			continue
		}

		needsFix, newFileName := f.needsFix(path)
		if needsFix {
			problems++
			f.logger.Printf("❌ PROBLEM: %s -> should be: %s (timestamp: %d)", path, newFileName, timestamp)
		} else {
			f.logger.Printf("✅ OK: %s (timestamp: %d)", path, timestamp)
		}
	}

	return count, problems
}

// 执行修复
func (f *LevelDBFixer) Fix() error {
	f.logger.Println("=== Starting WFS Database Fix ===")

	if f.dryRun {
		f.logger.Println("🔍 DRY RUN MODE - No actual changes will be made")
	}

	// 修复PATH_PRE索引
	pathPreFixed := f.fixPATH_PRE()

	// 修复PATH_SEQ索引
	pathSeqFixed := f.fixPATH_SEQ()

	// 修复0x0800索引
	index0800Fixed := f.fixIndex0800()

	totalFixed := pathPreFixed + pathSeqFixed + index0800Fixed

	f.logger.Println("\n=== Fix Summary ===")
	f.logger.Printf("PATH_PRE Index: %d entries fixed", pathPreFixed)
	f.logger.Printf("PATH_SEQ Index: %d entries fixed", pathSeqFixed)
	f.logger.Printf("0x0800 Index: %d entries fixed", index0800Fixed)
	f.logger.Printf("Total: %d entries fixed", totalFixed)

	if !f.dryRun && totalFixed > 0 {
		f.logger.Println("✅ Fix completed successfully!")
		f.logger.Println("💡 Restart WFS service and check web interface")
	} else if f.dryRun {
		f.logger.Println("🔍 Dry run completed - use -fix to apply changes")
	} else {
		f.logger.Println("✅ No fixes needed - database is already healthy")
	}

	return nil
}

// 修复PATH_PRE索引
func (f *LevelDBFixer) fixPATH_PRE() int {
	iter := f.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	fixed := 0
	batch := new(leveldb.Batch)

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_PRE) {
			continue
		}

		path := string(key[len(PATH_PRE):])
		needsFix, newFileName := f.needsFix(path)

		if needsFix {
			if f.dryRun {
				f.logger.Printf("🔍 Would fix PATH_PRE: %s -> %s", path, newFileName)
			} else {
				newKey := append(PATH_PRE, []byte(newFileName)...)
				batch.Put(newKey, value)
				batch.Delete(key)
				f.logger.Printf("🔧 Fixed PATH_PRE: %s -> %s", path, newFileName)
			}
			fixed++
		}
	}

	if !f.dryRun && fixed > 0 {
		f.db.Write(batch, &opt.WriteOptions{Sync: true})
	}

	return fixed
}

// 修复PATH_SEQ索引
func (f *LevelDBFixer) fixPATH_SEQ() int {
	iter := f.db.NewIterator(levelutil.BytesPrefix(PATH_SEQ), nil)
	defer iter.Release()

	fixed := 0
	batch := new(leveldb.Batch)

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		path, timestamp, err := f.parseWfsPathBean(value)
		if err != nil {
			continue
		}

		needsFix, newFileName := f.needsFix(path)
		if needsFix {
			if f.dryRun {
				f.logger.Printf("🔍 Would fix PATH_SEQ: %s -> %s", path, newFileName)
			} else {
				newValue := f.encodeWfsPathBean(newFileName, timestamp)
				batch.Put(key, newValue)
				f.logger.Printf("🔧 Fixed PATH_SEQ: %s -> %s", path, newFileName)
			}
			fixed++
		}
	}

	if !f.dryRun && fixed > 0 {
		f.db.Write(batch, &opt.WriteOptions{Sync: true})
	}

	return fixed
}

// 修复0x0800索引
func (f *LevelDBFixer) fixIndex0800() int {
	iter := f.db.NewIterator(levelutil.BytesPrefix(INDEX_0800), nil)
	defer iter.Release()

	fixed := 0
	batch := new(leveldb.Batch)

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		path, timestamp, err := f.parseWfsPathBean(value)
		if err != nil {
			continue
		}

		needsFix, newFileName := f.needsFix(path)
		if needsFix {
			if f.dryRun {
				f.logger.Printf("🔍 Would fix 0x0800: %s -> %s", path, newFileName)
			} else {
				newValue := f.encodeWfsPathBean(newFileName, timestamp)
				batch.Put(key, newValue)
				f.logger.Printf("🔧 Fixed 0x0800: %s -> %s", path, newFileName)
			}
			fixed++
		}
	}

	if !f.dryRun && fixed > 0 {
		f.db.Write(batch, &opt.WriteOptions{Sync: true})
	}

	return fixed
}

// 主函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("LevelDB Key Fixer for WFS System - Final Version")
		fmt.Println("Usage: leveldb_key_fixer <db_path> [options]")
		fmt.Println("")
		fmt.Println("Options:")
		fmt.Println("  -analyze       : Analyze database and show status")
		fmt.Println("  -dry-run       : Preview fixes without making changes")
		fmt.Println("  -fix           : Perform actual fixes")
		fmt.Println("  -create-content: Create test content for files (after fix)")
		fmt.Println("  -full-repair   : Complete repair (fix + rebuild + create content)")
		fmt.Println("")
		fmt.Println("Examples:")
		fmt.Println("  leveldb_key_fixer db_path -analyze")
		fmt.Println("  leveldb_key_fixer db_path -dry-run")
		fmt.Println("  leveldb_key_fixer db_path -fix")
		fmt.Println("  leveldb_key_fixer db_path -full-repair")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	analyzeOnly := false
	dryRun := false
	performFix := false

	// 解析参数
	for i := 2; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-analyze":
			analyzeOnly = true
		case "-dry-run":
			dryRun = true
		case "-fix":
			performFix = true
		}
	}

	// 默认为分析模式
	if !analyzeOnly && !dryRun && !performFix {
		analyzeOnly = true
	}

	// 创建修复器
	fixer, err := NewLevelDBFixer(dbPath, dryRun || analyzeOnly)
	if err != nil {
		log.Fatalf("Failed to create fixer: %v", err)
	}
	defer fixer.Close()

	// 执行操作
	if analyzeOnly {
		if err := fixer.Analyze(); err != nil {
			log.Fatalf("Analysis failed: %v", err)
		}
	} else {
		if err := fixer.Fix(); err != nil {
			log.Fatalf("Fix failed: %v", err)
		}
	}
}
