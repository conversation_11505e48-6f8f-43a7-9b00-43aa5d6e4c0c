// WFS全数据库扫描工具 - 扫描所有key并提取文件名stem保存到SQLite
package main

import (
	"database/sql"
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	_ "github.com/mattn/go-sqlite3"
	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type FullScannerConfig struct {
	DatabasePath string
	OutputPath   string
	Verbose      bool
	BatchSize    int // 批量插入大小
	ProgressStep int // 进度显示间隔
}

type FullScanner struct {
	config   *FullScannerConfig
	db       *leveldb.DB
	sqliteDB *sql.DB
	logger   *log.Logger
}

type KeyEntry struct {
	ID          int64
	KeyHex      string
	KeyType     string
	KeyLength   int
	ValueHex    string
	ValueLength int
	FileStem    string
	SeqID       int64
	Description string
	CreatedAt   time.Time
}

func NewFullScanner(config *FullScannerConfig) (*FullScanner, error) {
	logger := log.New(os.Stdout, "[FullScanner] ", log.LstdFlags)

	// 打开LevelDB
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	var db *leveldb.DB
	var err error

	for i := 0; i < 5; i++ {
		db, err = leveldb.OpenFile(config.DatabasePath, options)
		if err == nil {
			break
		}
		logger.Printf("Attempt %d failed: %v, retrying in 1 second...", i+1, err)
		time.Sleep(1 * time.Second)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to open LevelDB after 5 attempts: %v", err)
	}

	// 打开SQLite数据库
	sqliteDB, err := sql.Open("sqlite3", config.OutputPath)
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to open SQLite database: %v", err)
	}

	return &FullScanner{
		config:   config,
		db:       db,
		sqliteDB: sqliteDB,
		logger:   logger,
	}, nil
}

func (fs *FullScanner) Scan() error {
	fs.logger.Println("=== WFS Full Database Scan ===")

	// 创建SQLite表
	if err := fs.createTables(); err != nil {
		return fmt.Errorf("failed to create tables: %v", err)
	}

	// 扫描所有条目
	return fs.scanAllEntries()
}

func (fs *FullScanner) createTables() error {
	fs.logger.Println("Creating SQLite tables...")

	createTableSQL := `
	CREATE TABLE IF NOT EXISTS wfs_keys (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		key_hex TEXT NOT NULL,
		key_type TEXT NOT NULL,
		key_length INTEGER NOT NULL,
		value_hex TEXT NOT NULL,
		value_length INTEGER NOT NULL,
		file_stem TEXT,
		seq_id INTEGER,
		description TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_key_type ON wfs_keys(key_type);
	CREATE INDEX IF NOT EXISTS idx_file_stem ON wfs_keys(file_stem);
	CREATE INDEX IF NOT EXISTS idx_seq_id ON wfs_keys(seq_id);
	`

	_, err := fs.sqliteDB.Exec(createTableSQL)
	return err
}

func (fs *FullScanner) scanAllEntries() error {
	fs.logger.Println("Scanning all LevelDB entries...")

	// 准备插入语句
	insertSQL := `
	INSERT INTO wfs_keys (key_hex, key_type, key_length, value_hex, value_length, file_stem, seq_id, description)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`
	stmt, err := fs.sqliteDB.Prepare(insertSQL)
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement: %v", err)
	}
	defer stmt.Close()

	// 开始事务
	tx, err := fs.sqliteDB.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 扫描所有条目
	iter := fs.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		entry := fs.analyzeEntry(key, value)

		_, err := tx.Stmt(stmt).Exec(
			entry.KeyHex,
			entry.KeyType,
			entry.KeyLength,
			entry.ValueHex,
			entry.ValueLength,
			entry.FileStem,
			entry.SeqID,
			entry.Description,
		)
		if err != nil {
			return fmt.Errorf("failed to insert entry: %v", err)
		}

		count++
		if fs.config.Verbose && count%100 == 0 {
			fs.logger.Printf("Processed %d entries...", count)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	fs.logger.Printf("✅ Scanned and saved %d entries to SQLite", count)
	return nil
}

func (fs *FullScanner) analyzeEntry(key, value []byte) KeyEntry {
	keyHex := hex.EncodeToString(key)
	valueHex := hex.EncodeToString(value)

	entry := KeyEntry{
		KeyHex:      keyHex,
		KeyLength:   len(key),
		ValueHex:    valueHex,
		ValueLength: len(value),
		SeqID:       -1,
	}

	// 分析key类型
	if len(key) >= 2 {
		prefix := keyHex[:4]
		switch prefix {
		case "0000":
			entry.KeyType = "PATH_PRE"
			if len(key) > 2 {
				path := string(key[2:])
				entry.FileStem = fs.extractFileStem(path)
				entry.SeqID = fs.bytesToInt64(value)
				entry.Description = fmt.Sprintf("PATH_PRE: %s -> seqID=%d", path, entry.SeqID)
			}
		case "0100":
			entry.KeyType = "PATH_SEQ"
			if len(key) >= 10 {
				entry.SeqID = fs.bytesToInt64(key[2:])
				// 尝试解析WfsPathBean
				if path, timestamp, err := fs.parseWfsPathBean(value); err == nil {
					entry.FileStem = fs.extractFileStem(path)
					entry.Description = fmt.Sprintf("PATH_SEQ: seqID=%d -> path=%s, timestamp=%d", entry.SeqID, path, timestamp)
				} else {
					entry.Description = fmt.Sprintf("PATH_SEQ: seqID=%d (parse error: %v)", entry.SeqID, err)
				}
			}
		case "0800":
			entry.KeyType = "INDEX_0800"
			// 尝试解析WfsPathBean
			if path, timestamp, err := fs.parseWfsPathBean(value); err == nil {
				entry.FileStem = fs.extractFileStem(path)
				entry.Description = fmt.Sprintf("0x0800: path=%s, timestamp=%d", path, timestamp)
			} else {
				entry.Description = fmt.Sprintf("0x0800: parse error: %v", err)
			}
		default:
			// 检查是否是其他已知前缀
			if fs.isKnownPrefix(prefix) {
				entry.KeyType = fmt.Sprintf("INDEX_%s", prefix)
				entry.Description = fmt.Sprintf("Known index: %s", prefix)
			} else {
				// 可能是指纹索引或WfsFileBean
				if len(key) == 8 && len(value) == 8 {
					entry.KeyType = "FINGERPRINT"
					crc64Value := fs.bytesToInt64(key)
					contentID := hex.EncodeToString(value)
					entry.Description = fmt.Sprintf("Fingerprint: CRC64=%d -> ContentID=%s", crc64Value, contentID)
				} else if len(key) == 8 && len(value) > 15 && len(value) < 100 {
					entry.KeyType = "WFS_FILE_BEAN"
					contentID := hex.EncodeToString(key)
					entry.Description = fmt.Sprintf("WfsFileBean: ContentID=%s, size=%d bytes", contentID, len(value))
				} else {
					entry.KeyType = "UNKNOWN"
					entry.Description = fmt.Sprintf("Unknown: keyLen=%d, valueLen=%d", len(key), len(value))
				}
			}
		}
	} else {
		// 特殊key
		keyStr := string(key)
		switch keyStr {
		case "SEQ":
			entry.KeyType = "SEQ"
			entry.SeqID = fs.bytesToInt64(value)
			entry.Description = fmt.Sprintf("SEQ value: %d", entry.SeqID)
		case "COUNT":
			entry.KeyType = "COUNT"
			count := fs.bytesToInt64(value)
			entry.Description = fmt.Sprintf("COUNT value: %d", count)
		default:
			entry.KeyType = "SPECIAL"
			entry.Description = fmt.Sprintf("Special key: %s", keyStr)
		}
	}

	return entry
}

func (fs *FullScanner) extractFileStem(path string) string {
	if path == "" {
		return ""
	}

	// 标准化路径分隔符
	path = strings.ReplaceAll(path, "\\", "/")

	// 获取文件名
	filename := filepath.Base(path)

	// 移除扩展名
	ext := filepath.Ext(filename)
	if ext != "" {
		filename = filename[:len(filename)-len(ext)]
	}

	return filename
}

func (fs *FullScanner) isKnownPrefix(prefix string) bool {
	knownPrefixes := []string{
		"0600", "0700", "0900", "0a00", "0b00", "0c00", "0d00", "0e00", "0f00",
	}

	for _, known := range knownPrefixes {
		if prefix == known {
			return true
		}
	}
	return false
}

func (fs *FullScanner) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (fs *FullScanner) bytesToInt64(bs []byte) int64 {
	if len(bs) == 0 {
		return 0
	}
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (fs *FullScanner) Close() {
	if fs.db != nil {
		fs.db.Close()
	}
	if fs.sqliteDB != nil {
		fs.sqliteDB.Close()
	}
}

func main() {
	config := &FullScannerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "LevelDB database path (required)")
	flag.StringVar(&config.OutputPath, "output", "wfs_scan_results.sqlite", "SQLite output file path")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Full Database Scanner\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -output scan_results.sqlite -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	scanner, err := NewFullScanner(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer scanner.Close()

	if err := scanner.Scan(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
