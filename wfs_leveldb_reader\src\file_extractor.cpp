// 文件提取器实现

#include "file_extractor.hpp"
#include <fstream>
#include <algorithm>
#include <regex>
#include <fmt/format.h>

namespace wfs {

ErrorCode FileExtractor::set_output_directory(const std::string& output_dir) {
    output_directory_ = std::filesystem::path(output_dir);
    return ensure_directory_exists(output_directory_);
}

ErrorCode FileExtractor::extract_file(const FileRecord& record) {
    if (record.content_data.empty()) {
        return ErrorCode::CONTENT_NOT_FOUND;
    }
    
    // 清理文件名
    std::string safe_filename = sanitize_filename(record.file_name);
    if (safe_filename.empty()) {
        safe_filename = fmt::format("file_{}", record.seq_id);
    }
    
    // 生成输出路径
    std::filesystem::path output_path = output_directory_ / safe_filename;
    output_path = generate_unique_path(output_path);
    
    // 写入文件
    auto result = write_file_content(output_path, record.content_data);
    if (result == ErrorCode::SUCCESS) {
        extracted_count_++;
        total_extracted_size_ += record.content_data.size();
    }
    
    return result;
}

ErrorCode FileExtractor::extract_files(const std::map<int64_t, FileRecord>& records) {
    for (const auto& [seq_id, record] : records) {
        auto result = extract_file(record);
        if (result != ErrorCode::SUCCESS) {
            // 记录错误但继续处理其他文件
            fmt::print(stderr, "Warning: Failed to extract file {} (SeqID: {})\n", 
                record.file_name, seq_id);
        }
    }
    
    return ErrorCode::SUCCESS;
}

size_t FileExtractor::get_extracted_count() const {
    return extracted_count_;
}

size_t FileExtractor::get_total_extracted_size() const {
    return total_extracted_size_;
}

ErrorCode FileExtractor::ensure_directory_exists(const std::filesystem::path& dir) {
    std::error_code ec;
    
    if (!std::filesystem::exists(dir, ec)) {
        if (!std::filesystem::create_directories(dir, ec)) {
            return ErrorCode::FILE_WRITE_ERROR;
        }
    } else if (!std::filesystem::is_directory(dir, ec)) {
        return ErrorCode::FILE_WRITE_ERROR;
    }
    
    return ErrorCode::SUCCESS;
}

std::string FileExtractor::sanitize_filename(const std::string& filename) {
    std::string result = filename;
    
    // 移除或替换非法字符
    std::regex illegal_chars(R"([<>:"/\\|?*])");
    result = std::regex_replace(result, illegal_chars, "_");
    
    // 移除控制字符
    result.erase(std::remove_if(result.begin(), result.end(), 
        [](char c) { return c >= 0 && c < 32; }), result.end());
    
    // 限制长度
    if (result.length() > 200) {
        std::filesystem::path path(result);
        std::string stem = path.stem().string();
        std::string ext = path.extension().string();
        
        if (stem.length() > 200 - ext.length()) {
            stem = stem.substr(0, 200 - ext.length());
        }
        result = stem + ext;
    }
    
    // 移除前后空格和点
    result.erase(0, result.find_first_not_of(" ."));
    result.erase(result.find_last_not_of(" .") + 1);
    
    return result;
}

std::filesystem::path FileExtractor::generate_unique_path(const std::filesystem::path& base_path) {
    std::filesystem::path result = base_path;
    
    if (!std::filesystem::exists(result)) {
        return result;
    }
    
    // 生成唯一文件名
    std::filesystem::path stem = base_path.stem();
    std::filesystem::path extension = base_path.extension();
    std::filesystem::path parent = base_path.parent_path();
    
    for (int i = 1; i < 10000; ++i) {
        result = parent / fmt::format("{}_{}{}", stem.string(), i, extension.string());
        if (!std::filesystem::exists(result)) {
            break;
        }
    }
    
    return result;
}

ErrorCode FileExtractor::write_file_content(const std::filesystem::path& file_path, 
                                           const std::vector<uint8_t>& content) {
    try {
        // 确保父目录存在
        auto parent_dir = file_path.parent_path();
        if (!parent_dir.empty()) {
            auto result = ensure_directory_exists(parent_dir);
            if (result != ErrorCode::SUCCESS) {
                return result;
            }
        }
        
        // 写入文件
        std::ofstream file(file_path, std::ios::binary);
        if (!file.is_open()) {
            return ErrorCode::FILE_WRITE_ERROR;
        }
        
        file.write(reinterpret_cast<const char*>(content.data()), content.size());
        if (!file.good()) {
            return ErrorCode::FILE_WRITE_ERROR;
        }
        
        file.close();
        
        fmt::print("Extracted: {} ({} bytes)\n", file_path.string(), content.size());
        return ErrorCode::SUCCESS;
        
    } catch (const std::exception&) {
        return ErrorCode::FILE_WRITE_ERROR;
    }
}

std::unique_ptr<FileExtractor_i> create_file_extractor() {
    return std::make_unique<FileExtractor>();
}

} // namespace wfs
