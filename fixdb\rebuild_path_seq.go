// PATH_SEQ Index Rebuilder for WFS System
// 重建WFS系统中缺失的PATH_SEQ索引

package main

import (
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE = []byte{0, 0} // WFS系统中的PATH_PRE前缀
	PATH_SEQ = []byte{1, 0} // WFS系统中的PATH_SEQ前缀
)

// PATH_SEQ重建器
type PathSeqRebuilder struct {
	db     *leveldb.DB
	logger *log.Logger
	dryRun bool
}

// 创建重建器
func NewPathSeqRebuilder(dbPath string, dryRun bool) (*PathSeqRebuilder, error) {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     64 * 1024 * 1024, // 64MB
		WriteBuffer:            16 * 1024 * 1024, // 16MB
		ReadOnly:               dryRun,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		// 尝试恢复
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}

	return &PathSeqRebuilder{
		db:     db,
		logger: log.New(os.Stdout, "[PathSeqRebuilder] ", log.LstdFlags),
		dryRun: dryRun,
	}, nil
}

// 关闭重建器
func (r *PathSeqRebuilder) Close() error {
	if r.db != nil {
		return r.db.Close()
	}
	return nil
}

// 简化的protobuf编码（用于WfsPathBean）
func (r *PathSeqRebuilder) encodeWfsPathBean(path string, timestamp int64) []byte {
	var buf []byte

	// 编码Path字段 (field 1, wire type 2)
	pathBytes := []byte(path)
	tag1 := (1 << 3) | 2 // field 1, wire type 2 (length-delimited)
	buf = append(buf, byte(tag1))

	// 编码长度
	lengthBuf := make([]byte, binary.MaxVarintLen64)
	n := binary.PutUvarint(lengthBuf, uint64(len(pathBytes)))
	buf = append(buf, lengthBuf[:n]...)

	// 编码路径数据
	buf = append(buf, pathBytes...)

	// 编码Timestramp字段 (field 2, wire type 0)
	tag2 := (2 << 3) | 0 // field 2, wire type 0 (varint)
	buf = append(buf, byte(tag2))

	// 编码时间戳
	timestampBuf := make([]byte, binary.MaxVarintLen64)
	n = binary.PutVarint(timestampBuf, timestamp)
	buf = append(buf, timestampBuf[:n]...)

	return buf
}

// 重建PATH_SEQ索引
func (r *PathSeqRebuilder) Rebuild() error {
	r.logger.Println("Starting PATH_SEQ index rebuild...")

	// 获取当前时间戳
	currentTimestamp := time.Now().Unix()

	// 遍历所有PATH_PRE条目
	iter := r.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	rebuiltCount := 0
	errorCount := 0
	batch := new(leveldb.Batch)

	for iter.Next() {
		pathPreKey := iter.Key()
		seqIDBytes := iter.Value()

		// 提取文件名
		if len(pathPreKey) <= len(PATH_PRE) {
			continue
		}
		fileName := string(pathPreKey[len(PATH_PRE):])

		// 构造PATH_SEQ key
		pathSeqKey := append(PATH_SEQ, seqIDBytes...)

		if r.dryRun {
			r.logger.Printf("DRY RUN: Would create PATH_SEQ entry: seqID=%x, fileName=%s", seqIDBytes, fileName)
			rebuiltCount++
			continue
		}

		// 检查PATH_SEQ条目是否已存在
		exists, err := r.db.Has(pathSeqKey, nil)
		if err != nil {
			r.logger.Printf("Error checking PATH_SEQ existence for %s: %v", fileName, err)
			errorCount++
			continue
		}

		if exists {
			r.logger.Printf("PATH_SEQ already exists for %s (seqID: %x), skipping", fileName, seqIDBytes)
			continue
		}

		// 创建WfsPathBean数据
		pathBeanData := r.encodeWfsPathBean(fileName, currentTimestamp)

		// 添加到批处理
		batch.Put(pathSeqKey, pathBeanData)
		rebuiltCount++

		r.logger.Printf("Rebuilding PATH_SEQ: %s (seqID: %x)", fileName, seqIDBytes)

		// 每100个条目执行一次批处理
		if rebuiltCount%100 == 0 {
			if err := r.db.Write(batch, &opt.WriteOptions{Sync: false}); err != nil {
				r.logger.Printf("Error writing batch: %v", err)
				errorCount++
			}
			batch.Reset()
		}
	}

	// 执行剩余的批处理
	if !r.dryRun && rebuiltCount > 0 {
		if err := r.db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
			r.logger.Printf("Error writing final batch: %v", err)
			errorCount++
		}
	}

	// 检查迭代器错误
	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	r.logger.Printf("PATH_SEQ rebuild completed: %d entries rebuilt, %d errors", rebuiltCount, errorCount)

	if r.dryRun {
		r.logger.Println("DRY RUN completed - no actual changes made")
	}

	return nil
}

// 验证重建结果
func (r *PathSeqRebuilder) Verify() error {
	r.logger.Println("Verifying PATH_SEQ rebuild results...")

	// 遍历所有PATH_PRE条目
	pathPreIter := r.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer pathPreIter.Release()

	verifiedCount := 0
	missingCount := 0

	for pathPreIter.Next() {
		pathPreKey := pathPreIter.Key()
		seqIDBytes := pathPreIter.Value()

		if len(pathPreKey) <= len(PATH_PRE) {
			continue
		}
		fileName := string(pathPreKey[len(PATH_PRE):])

		// 检查对应的PATH_SEQ条目
		pathSeqKey := append(PATH_SEQ, seqIDBytes...)
		pathBeanData, err := r.db.Get(pathSeqKey, nil)
		if err != nil {
			r.logger.Printf("MISSING: PATH_SEQ entry for %s (seqID: %x)", fileName, seqIDBytes)
			missingCount++
			continue
		}

		// 简单验证数据不为空
		if len(pathBeanData) == 0 {
			r.logger.Printf("EMPTY: PATH_SEQ data for %s (seqID: %x)", fileName, seqIDBytes)
			missingCount++
			continue
		}

		r.logger.Printf("VERIFIED: PATH_SEQ entry for %s (seqID: %x, dataLen: %d)", fileName, seqIDBytes, len(pathBeanData))
		verifiedCount++
	}

	r.logger.Printf("Verification completed: %d verified, %d missing", verifiedCount, missingCount)

	if missingCount > 0 {
		return fmt.Errorf("verification failed: %d PATH_SEQ entries are missing", missingCount)
	}

	return nil
}

// 主函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: rebuild_path_seq <db_path> [options]")
		fmt.Println("Options:")
		fmt.Println("  -dry-run    : Dry run mode (no actual changes)")
		fmt.Println("  -verify     : Verify rebuild results")
		fmt.Println("")
		fmt.Println("This tool rebuilds missing PATH_SEQ index entries based on PATH_PRE data")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	dryRun := false
	verifyOnly := false

	// 解析参数
	for i := 2; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-dry-run":
			dryRun = true
		case "-verify":
			verifyOnly = true
		}
	}

	// 创建重建器
	rebuilder, err := NewPathSeqRebuilder(dbPath, dryRun || verifyOnly)
	if err != nil {
		log.Fatalf("Failed to create rebuilder: %v", err)
	}
	defer rebuilder.Close()

	if verifyOnly {
		// 只验证
		if err := rebuilder.Verify(); err != nil {
			log.Fatalf("Verification failed: %v", err)
		}
		log.Println("Verification completed successfully!")
	} else {
		// 重建
		if err := rebuilder.Rebuild(); err != nil {
			log.Fatalf("Rebuild failed: %v", err)
		}

		if !dryRun {
			// 重建后验证
			if err := rebuilder.Verify(); err != nil {
				log.Fatalf("Post-rebuild verification failed: %v", err)
			}
		}

		log.Println("PATH_SEQ rebuild completed successfully!")
	}
}
