1. 全面扫描并深度理解当前项目代码；
2. 当前网络采用Thrift协议，我们为接口文件新增加了一个Exist接口，更新详见 wfs.thrift文件 第 65 行，thrift.exe在根目录； 

要求：
1. 根据新增接口，增加、完善代码并编译出，性能和尺寸优化的可执行文件，增加的功能代码，
可以参考..\wfs目录对同名文件的修改，已知..\wfs实现Exist接口正确

2. 打开CGO，并设置参数：
go clean -cache
go clean -testcache
setx GOARCH amd64
setx CGO_ENABLED 1
setx CC "C:\dev\mingw_devkit-x642.0.0\w64devkit\bin\gcc.exe"
setx CXX "C:\dev\mingw_devkit-x642.0.0\w64devkit\bin\g++.exe"
set PATH=C:\dev\mingw_devkit-x642.0.0\w64devkit\bin;%PATH%
go build -O -trimpath -ldflags="-s -w -linkmode=external" -tags=release 


vcpkg install rocksdb[bzip2,lz4,snappy,zlib,zstd,tbb]:x64-windows