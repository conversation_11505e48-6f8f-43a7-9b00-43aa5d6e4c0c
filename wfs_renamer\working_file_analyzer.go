// 工作文件分析器 - 分析为什么1.jpg能正常显示
package main

import (
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

var (
	PATH_PRE_WORKING = []byte{0x00, 0x00}
	PATH_SEQ_WORKING = []byte{0x01, 0x00}
)

type WorkingFileAnalyzerConfig struct {
	DatabasePath string
	Verbose      bool
}

type WorkingFileAnalyzer struct {
	config   *WorkingFileAnalyzerConfig
	db       *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
}

func NewWorkingFileAnalyzer(config *WorkingFileAnalyzerConfig) (*WorkingFileAnalyzer, error) {
	logger := log.New(os.<PERSON>do<PERSON>, "[WorkingFileAnalyzer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	// 尝试多次打开数据库
	var db *leveldb.DB
	var err error
	
	for i := 0; i < 5; i++ {
		db, err = leveldb.OpenFile(config.DatabasePath, options)
		if err == nil {
			break
		}
		logger.Printf("Attempt %d failed: %v, retrying in 1 second...", i+1, err)
		time.Sleep(1 * time.Second)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to open database after 5 attempts: %v", err)
	}

	crcTable := crc64.MakeTable(crc64.ISO)

	return &WorkingFileAnalyzer{
		config:   config,
		db:       db,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (wfa *WorkingFileAnalyzer) Analyze() error {
	wfa.logger.Println("=== Working File Analysis - Why 1.jpg Works ===")

	// 专门分析1.jpg的完整数据链
	wfa.analyzeSpecificFile("1.jpg")

	return nil
}

func (wfa *WorkingFileAnalyzer) analyzeSpecificFile(filename string) {
	wfa.logger.Printf("\n--- Analyzing %s ---", filename)

	// 1. 查找PATH_PRE条目
	pathPreKey := append(PATH_PRE_WORKING, []byte(filename)...)
	seqIDBytes, err := wfa.db.Get(pathPreKey, nil)
	if err != nil {
		wfa.logger.Printf("❌ PATH_PRE not found for %s", filename)
		return
	}

	seqID := wfa.bytesToInt64(seqIDBytes)
	wfa.logger.Printf("✅ PATH_PRE: %s -> seqID=%d", filename, seqID)

	// 2. 查找PATH_SEQ条目
	pathSeqKey := append(PATH_SEQ_WORKING, wfa.int64ToBytes(seqID)...)
	pathBeanData, err := wfa.db.Get(pathSeqKey, nil)
	if err != nil {
		wfa.logger.Printf("❌ PATH_SEQ not found for seqID=%d", seqID)
		return
	}

	path, timestamp, err := wfa.parseWfsPathBean(pathBeanData)
	if err != nil {
		wfa.logger.Printf("❌ Failed to parse WfsPathBean: %v", err)
		return
	}
	wfa.logger.Printf("✅ PATH_SEQ: seqID=%d -> path=%s, timestamp=%d", seqID, path, timestamp)

	// 3. 计算指纹并查找指纹索引
	crc64Value := wfa.calculateCRC64(path)
	crc64Bytes := wfa.int64ToBytes(int64(crc64Value))
	
	contentID, err := wfa.db.Get(crc64Bytes, nil)
	if err != nil {
		wfa.logger.Printf("❌ Fingerprint index not found: CRC64=%d", crc64Value)
		return
	}
	wfa.logger.Printf("✅ Fingerprint: CRC64=%d -> ContentID=%s", crc64Value, hex.EncodeToString(contentID))

	// 4. 查找WfsFileBean
	wfsFileBean, err := wfa.db.Get(contentID, nil)
	if err != nil {
		wfa.logger.Printf("❌ WfsFileBean not found for ContentID=%s", hex.EncodeToString(contentID))
		return
	}
	wfa.logger.Printf("✅ WfsFileBean: ContentID=%s, size=%d bytes", hex.EncodeToString(contentID), len(wfsFileBean))

	// 5. 解析WfsFileBean
	storenode, offset, size, compressType, err := wfa.parseWfsFileBean(wfsFileBean)
	if err != nil {
		wfa.logger.Printf("❌ Failed to parse WfsFileBean: %v", err)
		return
	}
	wfa.logger.Printf("✅ WfsFileBean details: storenode=%s, offset=%d, size=%d, compress=%d", 
		storenode, offset, size, compressType)

	// 6. 总结为什么这个文件能工作
	wfa.logger.Printf("\n🎯 Why %s works:", filename)
	wfa.logger.Printf("  1. PATH_PRE exists and points to correct seqID")
	wfa.logger.Printf("  2. PATH_SEQ exists and contains correct WfsPathBean")
	wfa.logger.Printf("  3. Fingerprint index exists and points to valid ContentID")
	wfa.logger.Printf("  4. WfsFileBean exists and contains file metadata")
	wfa.logger.Printf("  5. Complete data chain: %s -> seqID=%d -> path=%s -> CRC64=%d -> ContentID=%s -> WfsFileBean",
		filename, seqID, path, crc64Value, hex.EncodeToString(contentID))
}

func (wfa *WorkingFileAnalyzer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wfa *WorkingFileAnalyzer) parseWfsFileBean(data []byte) (string, int64, int64, int32, error) {
	var storenode string
	var offset int64
	var size int64
	var compressType int32

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Storenode字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, 0, 0, fmt.Errorf("invalid storenode field")
				}
				i += n
				storenode = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Offset字段
			if wireType == 0 {
				off, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, 0, 0, fmt.Errorf("invalid offset field")
				}
				offset = off
				i += n
			}
		case 3: // Size字段
			if wireType == 0 {
				sz, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, 0, 0, fmt.Errorf("invalid size field")
				}
				size = sz
				i += n
			}
		case 4: // CompressType字段
			if wireType == 0 {
				ct, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, 0, 0, fmt.Errorf("invalid compress type field")
				}
				compressType = int32(ct)
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, 0, 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, 0, 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, 0, 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return storenode, offset, size, compressType, nil
}

func (wfa *WorkingFileAnalyzer) calculateCRC64(path string) uint64 {
	return crc64.Checksum([]byte(path), wfa.crcTable)
}

func (wfa *WorkingFileAnalyzer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wfa *WorkingFileAnalyzer) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (wfa *WorkingFileAnalyzer) Close() {
	if wfa.db != nil {
		wfa.db.Close()
	}
}

func main() {
	config := &WorkingFileAnalyzerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Working File Analyzer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	analyzer, err := NewWorkingFileAnalyzer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer analyzer.Close()

	if err := analyzer.Analyze(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
