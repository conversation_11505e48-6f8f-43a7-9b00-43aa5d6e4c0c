// 创建缺失的PATH_SEQ索引
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00}
	PATH_SEQ = []byte{0x01, 0x00}
)

func main() {
	var dbPath string
	var dryRun bool
	var verbose bool
	
	flag.StringVar(&dbPath, "db", "", "Database path (required)")
	flag.BoolVar(&dryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&verbose, "verbose", false, "Verbose output")
	flag.Parse()

	if dbPath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		os.Exit(1)
	}

	logger := log.New(os.Stdout, "[CreateMissingPathSeq] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               dryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
	}
	defer db.Close()

	logger.Println("=== Creating Missing PATH_SEQ Indexes ===")

	// 1. 收集PATH_PRE信息
	pathInfo := make(map[string][]byte) // path -> seqID
	
	iter := db.NewIterator(nil, nil)
	defer iter.Release()

	logger.Println("Scanning PATH_PRE index...")
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqID := iter.Value()
				pathInfo[path] = seqID
				
				seqIDInt := bytesToInt64(seqID)
				if verbose {
					logger.Printf("PATH_PRE: %s -> seqID %d (%x)", path, seqIDInt, seqID)
				}
			}
		}
	}

	logger.Printf("Found %d PATH_PRE entries", len(pathInfo))

	// 2. 检查并创建缺失的PATH_SEQ
	logger.Println("Checking and creating missing PATH_SEQ indexes...")
	
	batch := new(leveldb.Batch)
	created := 0
	
	for path, seqID := range pathInfo {
		seqIDInt := bytesToInt64(seqID)
		pathSeqKey := append(PATH_SEQ, int64ToBytes(seqIDInt)...)
		
		if _, err := db.Get(pathSeqKey, nil); err == leveldb.ErrNotFound {
			// PATH_SEQ不存在，需要创建
			timestamp := time.Now().UnixNano()
			pathBeanData := encodeWfsPathBean(path, timestamp)
			
			if dryRun {
				logger.Printf("[DRY RUN] Would create PATH_SEQ: seqID %d -> path=%s, timestamp=%d", seqIDInt, path, timestamp)
			} else {
				batch.Put(pathSeqKey, pathBeanData)
				if verbose {
					logger.Printf("Creating PATH_SEQ: seqID %d -> path=%s, timestamp=%d", seqIDInt, path, timestamp)
				}
			}
			created++
		} else if err == nil {
			if verbose {
				logger.Printf("PATH_SEQ already exists: seqID %d (%s)", seqIDInt, path)
			}
		} else {
			logger.Printf("Error checking PATH_SEQ for seqID %d: %v", seqIDInt, err)
		}
	}

	if created == 0 {
		logger.Println("✅ All PATH_SEQ indexes already exist")
		return
	}

	logger.Printf("Will create %d missing PATH_SEQ entries", created)

	if dryRun {
		logger.Println("[DRY RUN] Would execute batch operation")
	} else {
		if err := db.Write(batch, nil); err != nil {
			logger.Printf("❌ Failed to write batch: %v", err)
			os.Exit(1)
		}
		logger.Printf("✅ Successfully created %d PATH_SEQ entries", created)
	}
}

// 辅助函数
func bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		return 0
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte
	
	// Path字段 (field 1, wire type 2)
	if path != "" {
		result = append(result, 0x0A)
		pathBytes := []byte(path)
		result = append(result, byte(len(pathBytes)))
		result = append(result, pathBytes...)
	}
	
	// Timestamp字段 (field 2, wire type 0)
	if timestamp != 0 {
		result = append(result, 0x10)
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}
	
	return result
}
