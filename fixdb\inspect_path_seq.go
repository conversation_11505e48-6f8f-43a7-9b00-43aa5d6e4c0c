// PATH_SEQ inspection tool
// 检查PATH_SEQ索引中的WfsPathBean数据

package main

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

var (
	PATH_PRE = []byte{0, 0} // WFS系统中的PATH_PRE前缀
	PATH_SEQ = []byte{1, 0} // WFS系统中的PATH_SEQ前缀
)

// 简化的WfsPathBean反序列化
func decodeWfsPathBean(data []byte) (string, int64, error) {
	if len(data) < 12 { // 至少需要4字节长度 + 8字节时间戳
		return "", 0, fmt.Errorf("data too short: %d bytes", len(data))
	}

	// 尝试读取路径长度
	pathLen := binary.LittleEndian.Uint32(data[0:4])
	if len(data) < int(4+pathLen+8) {
		return "", 0, fmt.Errorf("invalid data format: pathLen=%d, dataLen=%d", pathLen, len(data))
	}

	// 读取路径
	path := string(data[4 : 4+pathLen])

	// 读取时间戳
	timestamp := int64(binary.LittleEndian.Uint64(data[4+pathLen : 4+pathLen+8]))

	return path, timestamp, nil
}

func inspectPathSeq(dbPath string) error {
	log.Printf("Inspecting PATH_SEQ data in database: %s", dbPath)

	// 打开数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     8 * 1024 * 1024,
		WriteBuffer:            4 * 1024 * 1024,
		ReadOnly:               true, // 只读模式
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		// 尝试恢复
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}
	defer db.Close()

	// 首先检查PATH_PRE索引
	log.Printf("\n=== PATH_PRE Index ===")
	pathPreIter := db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer pathPreIter.Release()

	pathPreCount := 0
	pathToSeqMap := make(map[string][]byte)

	for pathPreIter.Next() {
		key := pathPreIter.Key()
		value := pathPreIter.Value()
		pathPreCount++

		if len(key) > len(PATH_PRE) {
			path := string(key[len(PATH_PRE):])
			seqID := make([]byte, len(value))
			copy(seqID, value)
			pathToSeqMap[path] = seqID

			log.Printf("PATH_PRE[%d]: %s -> seqID: %x", pathPreCount, path, seqID)
		}
	}

	log.Printf("Total PATH_PRE entries: %d", pathPreCount)

	// 扫描所有可能的PATH_SEQ前缀
	log.Printf("\n=== Scanning for PATH_SEQ-like data ===")
	allIter := db.NewIterator(nil, nil)
	defer allIter.Release()

	pathSeqCount := 0
	problemCount := 0
	prefixMap := make(map[string]int)

	for allIter.Next() {
		key := allIter.Key()
		value := allIter.Value()

		// 分析前缀
		if len(key) >= 2 {
			prefix := fmt.Sprintf("%02x%02x", key[0], key[1])
			prefixMap[prefix]++

			// 尝试解析为WfsPathBean（对于所有非PATH_PRE的key）
			if !bytes.HasPrefix(key, PATH_PRE) && len(value) > 12 {
				path, timestamp, err := decodeWfsPathBean(value)
				if err == nil && len(path) > 0 && path[0] != 0 { // 简单的有效性检查
					pathSeqCount++

					// 检查路径是否包含分隔符
					hasPathSeparator := false
					for _, c := range path {
						if c == '/' || c == '\\' {
							hasPathSeparator = true
							break
						}
					}

					if hasPathSeparator {
						problemCount++
						log.Printf("FOUND PROBLEM PATH_SEQ[%d]: prefix=%s, seqID=%x, PROBLEM PATH: %s (timestamp: %d)",
							pathSeqCount, prefix, key[2:], path, timestamp)
					} else {
						log.Printf("FOUND PATH_SEQ[%d]: prefix=%s, seqID=%x, OK PATH: %s (timestamp: %d)",
							pathSeqCount, prefix, key[2:], path, timestamp)
					}
				}
			}
		}
	}

	log.Printf("\nPrefix distribution:")
	for prefix, count := range prefixMap {
		log.Printf("  %s: %d keys", prefix, count)
	}

	log.Printf("Total PATH_SEQ-like entries found: %d", pathSeqCount)
	log.Printf("Problem PATH_SEQ entries (with path separators): %d", problemCount)

	// 检查一致性
	log.Printf("\n=== Consistency Check ===")
	for path, seqID := range pathToSeqMap {
		pathSeqKey := append(PATH_SEQ, seqID...)
		pathBeanData, err := db.Get(pathSeqKey, nil)
		if err != nil {
			log.Printf("INCONSISTENCY: PATH_PRE path '%s' has seqID %x, but no corresponding PATH_SEQ entry", path, seqID)
			continue
		}

		beanPath, _, err := decodeWfsPathBean(pathBeanData)
		if err != nil {
			log.Printf("INCONSISTENCY: PATH_PRE path '%s' -> PATH_SEQ seqID %x, but failed to decode bean: %v", path, seqID, err)
			continue
		}

		if beanPath != path {
			log.Printf("INCONSISTENCY: PATH_PRE path '%s' != PATH_SEQ bean path '%s' (seqID: %x)", path, beanPath, seqID)
		} else {
			log.Printf("CONSISTENT: PATH_PRE path '%s' == PATH_SEQ bean path '%s' (seqID: %x)", path, beanPath, seqID)
		}
	}

	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: inspect_path_seq <db_path>")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	if err := inspectPathSeq(dbPath); err != nil {
		log.Fatalf("Inspection failed: %v", err)
	}
}
