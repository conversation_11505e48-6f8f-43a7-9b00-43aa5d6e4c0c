// LevelDB数据库诊断工具
// 专门用于排查数据库条目为空的问题

#include <iostream>
#include <string>
#include <filesystem>
#include <memory>
#include <iomanip>

// 简化版本，不依赖复杂的头文件
#ifdef _WIN32
#include <windows.h>
#endif

namespace fs = std::filesystem;

// 模拟LevelDB接口进行诊断
class LevelDBDiagnostic {
public:
    static void diagnose_database(const std::string& db_path) {
        std::cout << "=== LevelDB Database Diagnostic ===" << std::endl;
        std::cout << "Database path: " << db_path << std::endl;
        
        // 1. 检查路径存在性
        if (!fs::exists(db_path)) {
            std::cout << "❌ ERROR: Database path does not exist!" << std::endl;
            return;
        }
        std::cout << "✅ Database path exists" << std::endl;
        
        // 2. 检查是否为目录
        if (!fs::is_directory(db_path)) {
            std::cout << "❌ ERROR: Database path is not a directory!" << std::endl;
            return;
        }
        std::cout << "✅ Database path is a directory" << std::endl;
        
        // 3. 检查目录内容
        std::cout << "\n--- Directory Contents ---" << std::endl;
        try {
            size_t file_count = 0;
            size_t total_size = 0;
            
            for (const auto& entry : fs::directory_iterator(db_path)) {
                file_count++;
                if (entry.is_regular_file()) {
                    auto size = entry.file_size();
                    total_size += size;
                    std::cout << "File: " << entry.path().filename().string() 
                              << " (Size: " << size << " bytes)" << std::endl;
                } else if (entry.is_directory()) {
                    std::cout << "Dir:  " << entry.path().filename().string() << std::endl;
                }
            }
            
            std::cout << "Total files/dirs: " << file_count << std::endl;
            std::cout << "Total size: " << total_size << " bytes" << std::endl;
            
            if (file_count == 0) {
                std::cout << "❌ WARNING: Directory is empty!" << std::endl;
                return;
            }
            
            if (total_size == 0) {
                std::cout << "❌ WARNING: All files are empty!" << std::endl;
                return;
            }
            
        } catch (const std::exception& e) {
            std::cout << "❌ ERROR reading directory: " << e.what() << std::endl;
            return;
        }
        
        // 4. 检查关键LevelDB文件
        std::cout << "\n--- LevelDB File Check ---" << std::endl;
        check_leveldb_file(db_path, "CURRENT");
        check_leveldb_file(db_path, "MANIFEST-000001");
        check_leveldb_file(db_path, "LOG");
        check_leveldb_file(db_path, "LOCK");
        
        // 5. 检查.ldb和.log文件
        std::cout << "\n--- Data Files Check ---" << std::endl;
        size_t ldb_count = 0;
        size_t log_count = 0;
        
        for (const auto& entry : fs::directory_iterator(db_path)) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                if (filename.ends_with(".ldb")) {
                    ldb_count++;
                    std::cout << "LDB file: " << filename 
                              << " (Size: " << entry.file_size() << " bytes)" << std::endl;
                } else if (filename.ends_with(".log")) {
                    log_count++;
                    std::cout << "LOG file: " << filename 
                              << " (Size: " << entry.file_size() << " bytes)" << std::endl;
                }
            }
        }
        
        std::cout << "Total .ldb files: " << ldb_count << std::endl;
        std::cout << "Total .log files: " << log_count << std::endl;
        
        if (ldb_count == 0 && log_count == 0) {
            std::cout << "❌ WARNING: No data files (.ldb/.log) found!" << std::endl;
            std::cout << "This suggests the database is empty or corrupted." << std::endl;
        }
        
        // 6. 权限检查
        std::cout << "\n--- Permission Check ---" << std::endl;
        check_permissions(db_path);
        
        // 7. 给出诊断结论
        std::cout << "\n--- Diagnostic Summary ---" << std::endl;
        if (ldb_count > 0 || log_count > 0) {
            std::cout << "✅ Database appears to have data files" << std::endl;
            std::cout << "💡 If C++ program still shows empty, possible causes:" << std::endl;
            std::cout << "   1. LevelDB version compatibility issues" << std::endl;
            std::cout << "   2. Database corruption requiring repair" << std::endl;
            std::cout << "   3. Iterator creation or usage errors" << std::endl;
            std::cout << "   4. Read permissions issues" << std::endl;
        } else {
            std::cout << "❌ Database appears to be empty or invalid" << std::endl;
            std::cout << "💡 Possible solutions:" << std::endl;
            std::cout << "   1. Check if this is the correct database path" << std::endl;
            std::cout << "   2. Verify the database was properly created" << std::endl;
            std::cout << "   3. Check if data was written to a different location" << std::endl;
        }
    }
    
private:
    static void check_leveldb_file(const std::string& db_path, const std::string& filename) {
        fs::path file_path = fs::path(db_path) / filename;
        if (fs::exists(file_path)) {
            auto size = fs::file_size(file_path);
            std::cout << "✅ " << filename << " exists (Size: " << size << " bytes)" << std::endl;
            
            if (size == 0) {
                std::cout << "   ⚠️  WARNING: File is empty!" << std::endl;
            }
        } else {
            std::cout << "❌ " << filename << " missing" << std::endl;
        }
    }
    
    static void check_permissions(const std::string& db_path) {
#ifdef _WIN32
        // Windows权限检查
        DWORD attrs = GetFileAttributesA(db_path.c_str());
        if (attrs != INVALID_FILE_ATTRIBUTES) {
            std::cout << "✅ Directory is accessible" << std::endl;
            if (attrs & FILE_ATTRIBUTE_READONLY) {
                std::cout << "⚠️  WARNING: Directory is read-only" << std::endl;
            }
        } else {
            std::cout << "❌ Cannot access directory attributes" << std::endl;
        }
#else
        // Linux权限检查
        if (access(db_path.c_str(), R_OK) == 0) {
            std::cout << "✅ Directory is readable" << std::endl;
        } else {
            std::cout << "❌ Directory is not readable" << std::endl;
        }
        
        if (access(db_path.c_str(), W_OK) == 0) {
            std::cout << "✅ Directory is writable" << std::endl;
        } else {
            std::cout << "⚠️  Directory is not writable" << std::endl;
        }
#endif
    }
};

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cout << "Usage: " << argv[0] << " <leveldb_path>" << std::endl;
        std::cout << "Example: " << argv[0] << " C:\\wfsdata\\wfsdb" << std::endl;
        return 1;
    }
    
    std::string db_path = argv[1];
    LevelDBDiagnostic::diagnose_database(db_path);
    
    std::cout << "\nPress Enter to continue..." << std::endl;
    std::cin.get();
    
    return 0;
}
