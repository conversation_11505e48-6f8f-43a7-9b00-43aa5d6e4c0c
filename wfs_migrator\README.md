# WFS高并发迁移工具

基于Thrift协议的高性能WFS数据迁移工具，从指定WFS存档文件夹读取数据，去除路径后通过WFS服务的Append接口写入新数据库。

## 🎯 功能特点

- ✅ **高并发处理**：多线程并发读取和上传
- ✅ **Thrift协议**：通过WFS官方API确保数据格式正确
- ✅ **路径修正**：自动去除文件路径，只保留文件名
- ✅ **智能跳过**：可选择跳过已存在的文件
- ✅ **连接池**：高效的Thrift连接池管理
- ✅ **实时监控**：实时显示迁移进度和速度
- ✅ **安全可靠**：支持dry-run模式预览

## 📋 系统要求

- Go 1.21 或更高版本
- 运行中的WFS服务（目标服务）
- 源WFS存档文件夹（包含logs, wfsdb, wfsfile）

## 🛠️ 编译安装

### 1. 准备依赖
```bash
cd wfs_migrator
go mod tidy
```

### 2. 编译程序
```bash
go build -o wfs_migrator main.go
```

## 🚀 使用方法

### 基本语法
```bash
wfs_migrator <source_wfs_folder> <wfs_host:port> [options]
```

### 参数说明
- `source_wfs_folder`: 源WFS存档文件夹路径（包含logs, wfsdb, wfsfile）
- `wfs_host:port`: 目标WFS服务地址（如：localhost:9090）

### 选项参数
- `-workers <n>`: 工作线程数（默认：CPU核心数）
- `-buffer <n>`: 缓冲区大小（默认：10000）
- `-batch <n>`: 批处理大小（默认：100）
- `-connections <n>`: 连接池大小（默认：10）
- `-dry-run`: 干运行模式（只预览，不实际上传）
- `-skip-existing`: 跳过已存在的文件

## 📊 使用示例

### 1. 预览迁移（推荐第一步）
```bash
# 预览迁移操作，不实际上传文件
./wfs_migrator /path/to/source/wfsdata localhost:9090 -dry-run
```

### 2. 执行迁移
```bash
# 基本迁移
./wfs_migrator /path/to/source/wfsdata localhost:9090

# 高性能迁移（8个工作线程，跳过已存在文件）
./wfs_migrator /path/to/source/wfsdata localhost:9090 -workers 8 -skip-existing

# 远程WFS服务迁移
./wfs_migrator C:\wfsdata *************:9090 -workers 16 -connections 20
```

### 3. Windows示例
```cmd
# 预览迁移
wfs_migrator.exe C:\backup\wfsdata localhost:9090 -dry-run

# 执行迁移
wfs_migrator.exe C:\backup\wfsdata localhost:9090 -workers 8 -skip-existing
```

## 📋 完整迁移流程

### 步骤1：准备目标WFS服务
```bash
# 1. 启动新的WFS服务（指向空白数据库）
./wfs -config new_wfs.json

# 2. 确认服务运行正常
curl http://localhost:9090/ping
```

### 步骤2：执行迁移
```bash
# 1. 预览迁移
./wfs_migrator /old/wfsdata localhost:9090 -dry-run

# 2. 执行迁移
./wfs_migrator /old/wfsdata localhost:9090 -workers 8 -skip-existing

# 3. 验证结果
# 通过WFS网页界面检查文件列表
```

## 📊 性能优化

### 硬件建议
- **CPU**: 多核心处理器，推荐8核以上
- **内存**: 至少4GB，推荐8GB以上
- **网络**: 千兆网络，低延迟连接
- **存储**: SSD硬盘提高读取性能

### 参数调优
```bash
# 小数据量（< 1万文件）
./wfs_migrator source target:9090 -workers 4 -connections 5

# 中等数据量（1万-10万文件）
./wfs_migrator source target:9090 -workers 8 -connections 10

# 大数据量（> 10万文件）
./wfs_migrator source target:9090 -workers 16 -connections 20 -buffer 20000
```

## 🔍 监控和日志

### 实时监控
工具会每5秒输出进度信息：
```
[WfsMigrator] Progress: 1500/5000 files, 1450 success, 50 skipped, 0 errors, 2.1 GB/5.0 GB, 15.2 MB/s, elapsed: 2m30s
```

### 日志信息
- **成功**: `Successfully migrated path/file.jpg -> file.jpg (1024 bytes)`
- **跳过**: `Skipping existing file file.jpg`
- **错误**: `Error processing path/file.jpg: connection failed`

## ⚠️ 注意事项

### 安全提醒
1. **备份数据**：迁移前备份源数据
2. **测试连接**：确保目标WFS服务正常运行
3. **网络稳定**：确保网络连接稳定
4. **权限检查**：确保有读取源数据的权限

### 故障排除
1. **连接失败**：检查WFS服务是否运行，端口是否正确
2. **权限错误**：检查源文件夹读取权限
3. **内存不足**：减少worker数量和buffer大小
4. **网络超时**：减少并发连接数

## 🎯 预期效果

迁移完成后：
- ✅ 文件名显示正确（如 `image.jpg`）
- ✅ 不再显示带路径的文件名（如 `folder/image.jpg`）
- ✅ 所有WFS功能正常工作
- ✅ 数据完整性得到保证

## 📞 技术支持

### 常见问题
1. **Q**: 迁移过程中断怎么办？
   **A**: 重新运行工具，使用 `-skip-existing` 跳过已迁移文件

2. **Q**: 如何提高迁移速度？
   **A**: 增加worker数量和连接池大小，使用高性能网络

3. **Q**: 文件名冲突怎么办？
   **A**: 工具会自动跳过已存在的同名文件

### 性能基准
- **本地迁移**: 50-100 MB/s
- **千兆网络**: 20-50 MB/s  
- **百兆网络**: 5-10 MB/s

---

**工具版本**: v1.0  
**开发时间**: 2025年7月28日  
**适用范围**: WFS系统高并发数据迁移
