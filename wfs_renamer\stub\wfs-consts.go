// Code generated by Thrift Compiler (0.20.0). DO NOT EDIT.

package stub

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"strings"
	"regexp"
)

// (needed to ensure safety because of naive import list construction.)
var _ = bytes.Equal
var _ = context.Background
var _ = errors.New
var _ = fmt.Printf
var _ = slog.Log
var _ = time.Now
var _ = thrift.ZERO
// (needed by validator.)
var _ = strings.Contains
var _ = regexp.MatchString


func init() {
}

