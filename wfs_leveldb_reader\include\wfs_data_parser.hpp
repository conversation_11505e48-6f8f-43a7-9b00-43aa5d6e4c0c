// WFS数据解析器接口
// 负责解析WFS特定的数据格式

#pragma once

#include "datatype.hpp"
#include <vector>
#include <cstdint>

namespace wfs {

// WFS数据解析器接口
class WfsDataParser_i {
public:
    virtual ~WfsDataParser_i() = default;
    
    // 解析protobuf格式的WfsPathBean
    virtual WfsPathBeanResult parse_wfs_path_bean(const std::vector<uint8_t>& data) = 0;
    
    // 编码WfsPathBean为protobuf格式
    virtual std::vector<uint8_t> encode_wfs_path_bean(const WfsPathBean& bean) = 0;
    
    // 解析varint编码
    virtual std::pair<uint64_t, size_t> parse_varint(const uint8_t* data, size_t len) = 0;
    
    // 编码varint
    virtual std::vector<uint8_t> encode_varint(uint64_t value) = 0;
};

// WFS数据解析器实现
class WfsDataParser : public WfsDataParser_i {
private:
    // 内部解析方法
    std::pair<uint64_t, size_t> read_varint(const uint8_t* data, size_t len, size_t& pos);
    std::pair<int64_t, size_t> read_signed_varint(const uint8_t* data, size_t len, size_t& pos);
    std::pair<std::string, size_t> read_length_delimited(const uint8_t* data, size_t len, size_t& pos);
    
public:
    WfsDataParser() = default;
    ~WfsDataParser() override = default;
    
    // 实现接口方法
    WfsPathBeanResult parse_wfs_path_bean(const std::vector<uint8_t>& data) override;
    std::vector<uint8_t> encode_wfs_path_bean(const WfsPathBean& bean) override;
    std::pair<uint64_t, size_t> parse_varint(const uint8_t* data, size_t len) override;
    std::vector<uint8_t> encode_varint(uint64_t value) override;
};

// 工厂函数
std::unique_ptr<WfsDataParser_i> create_wfs_data_parser();

} // namespace wfs
