package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/donnie4w/gothrift/thrift"
	"github.com/donnie4w/wfs/stub"
)

func main() {
	fmt.Println("Testing WFS Exist interface...")

	// 创建传输层
	transport, err := thrift.NewTSocket("localhost:9001")
	if err != nil {
		log.Fatalf("Error creating transport: %v", err)
	}

	// 创建协议层
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()
	client := stub.NewWfsIfaceClientFactory(transport, protocolFactory)

	// 打开连接
	if err := transport.Open(); err != nil {
		log.Fatalf("Error opening transport: %v", err)
	}
	defer transport.Close()

	ctx := context.Background()

	// 测试认证
	fmt.Println("Testing authentication...")
	auth := &stub.WfsAuth{
		Name: thrift.StringPtr("admin"),
		Pwd:  thrift.StringPtr("123456"),
	}
	
	authResult, err := client.Auth(ctx, auth)
	if err != nil {
		log.Fatalf("Auth error: %v", err)
	}
	
	if !authResult.Ok {
		log.Fatalf("Authentication failed")
	}
	fmt.Println("Authentication successful!")

	// 测试上传文件
	fmt.Println("Testing file upload...")
	testData := []byte("Hello, WFS with Exist interface!")
	testFile := &stub.WfsFile{
		Name: "test_file.txt",
		Data: testData,
	}
	
	uploadResult, err := client.Append(ctx, testFile)
	if err != nil {
		log.Fatalf("Upload error: %v", err)
	}
	
	if !uploadResult.Ok {
		log.Fatalf("Upload failed")
	}
	fmt.Println("File uploaded successfully!")

	// 等待一下确保文件已保存
	time.Sleep(100 * time.Millisecond)

	// 测试Exist接口 - 检查存在的文件
	fmt.Println("Testing Exist interface for existing file...")
	existResult, err := client.Exist(ctx, "test_file.txt")
	if err != nil {
		log.Fatalf("Exist error: %v", err)
	}
	
	fmt.Printf("File exists: %v\n", existResult.Exists)
	if existResult.Size != nil {
		fmt.Printf("File size: %d bytes\n", *existResult.Size)
	}
	
	if !existResult.Exists {
		log.Fatalf("Expected file to exist, but it doesn't")
	}
	
	if existResult.Size == nil || *existResult.Size != int64(len(testData)) {
		log.Fatalf("Expected file size %d, got %v", len(testData), existResult.Size)
	}

	// 测试Exist接口 - 检查不存在的文件
	fmt.Println("Testing Exist interface for non-existing file...")
	nonExistResult, err := client.Exist(ctx, "non_existing_file.txt")
	if err != nil {
		log.Fatalf("Exist error for non-existing file: %v", err)
	}
	
	fmt.Printf("Non-existing file exists: %v\n", nonExistResult.Exists)
	if nonExistResult.Exists {
		log.Fatalf("Expected file to not exist, but it does")
	}

	// 测试Ping
	fmt.Println("Testing Ping...")
	pingResult, err := client.Ping(ctx)
	if err != nil {
		log.Fatalf("Ping error: %v", err)
	}
	fmt.Printf("Ping result: %d\n", pingResult)

	fmt.Println("All tests passed! Exist interface is working correctly.")
}
