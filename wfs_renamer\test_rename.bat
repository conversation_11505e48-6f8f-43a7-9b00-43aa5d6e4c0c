@echo off
REM WFS高并发重命名工具测试脚本

echo ========================================
echo WFS High-Concurrency Rename Tool Test
echo ========================================

set DB_PATH=..\wfsdata\wfsdb

echo 测试数据库路径: %DB_PATH%
echo.

if not exist "%DB_PATH%" (
    echo 错误: 数据库路径不存在: %DB_PATH%
    pause
    exit /b 1
)

echo === 步骤1: 预览自动重命名规则 ===
echo 检测包含路径分隔符的文件，生成重命名规则...
echo.
db_renamer_fixed.exe -db "%DB_PATH%" -auto -dry-run -verbose -workers 4

echo.
echo === 步骤2: 执行重命名操作 ===
echo 确认要执行实际的重命名操作吗？
echo 这将修改数据库中的文件路径！
echo.
set /p confirm="输入 'yes' 确认执行: "

if not "%confirm%"=="yes" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 正在执行重命名操作...
db_renamer_fixed.exe -db "%DB_PATH%" -auto -verbose -workers 8

echo.
echo === 步骤3: 验证结果 ===
echo 重新扫描数据库，检查是否还有包含路径的文件...
echo.
db_renamer_fixed.exe -db "%DB_PATH%" -auto -dry-run -workers 4

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 说明:
echo - 第一次运行应该显示需要重命名的文件
echo - 执行重命名后，第二次运行应该显示0个需要重命名的文件
echo - 这表明所有包含路径的文件都已成功重命名为纯文件名
echo.
pause
