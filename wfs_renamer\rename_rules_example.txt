# WFS文件重命名规则示例
# 格式：old_path -> new_path 或 old_path,new_path
# 以#开头的行为注释

# 示例1：去除路径，只保留文件名（基于实际WFS数据）
a\2.jpg -> 2.jpg
b/3.jpg -> 3.jpg
/a/b/c/4.jpg -> 4.jpg
\a\d\5.jpg -> 5.jpg

# 示例2：重命名文件
old_document.txt -> new_document.txt
temp_file.dat -> final_file.dat

# 示例3：批量重命名（使用逗号分隔）
image001.png,photo001.png
image002.png,photo002.png
image003.png,photo003.png

# 示例4：添加前缀
report.pdf -> 2024_report.pdf
summary.doc -> 2024_summary.doc

# 示例5：修改扩展名（注意：这只是重命名，不会转换文件格式）
data.txt -> data.csv
config.conf -> config.json

# 示例6：整理文件名格式
My Document (1).docx -> my_document_1.docx
Photo 2024-01-01.jpg -> photo_20240101.jpg

# 注意事项：
# 1. 路径区分大小写
# 2. 确保新文件名不与现有文件冲突
# 3. 建议先使用-dry-run参数预览
# 4. 支持中文文件名
测试文件.txt -> test_file.txt
