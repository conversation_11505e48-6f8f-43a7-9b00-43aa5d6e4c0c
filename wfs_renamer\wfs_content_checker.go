// WFS文件内容检查工具 - 检查文件内容是否存在
package main

import (
	"crypto/md5"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_CONTENT = []byte{0x00, 0x00}
	PATH_SEQ_CONTENT = []byte{0x01, 0x00}
)

type ContentCheckerConfig struct {
	DatabasePath string
	Verbose      bool
}

type WFSContentChecker struct {
	config *ContentCheckerConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewWFSContentChecker(config *ContentCheckerConfig) (*WFSContentChecker, error) {
	logger := log.New(os.Stdout, "[WFSContentChecker] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &WFSContentChecker{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (wcc *WFSContentChecker) Check() error {
	wcc.logger.Println("=== WFS Content Check ===")

	// 1. 收集所有文件
	files := wcc.collectFiles()

	// 2. 检查每个文件的完整性
	wcc.checkFileIntegrity(files)

	return nil
}

func (wcc *WFSContentChecker) collectFiles() map[int64]string {
	wcc.logger.Println("Collecting files from PATH_PRE...")

	files := make(map[int64]string)
	iter := wcc.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_CONTENT[0] && key[1] == PATH_PRE_CONTENT[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := wcc.bytesToInt64(seqIDBytes)
				files[seqID] = path

				if wcc.config.Verbose {
					wcc.logger.Printf("Found file: seqID=%d, path=%s", seqID, path)
				}
			}
		}
	}

	wcc.logger.Printf("Found %d files", len(files))
	return files
}

func (wcc *WFSContentChecker) checkFileIntegrity(files map[int64]string) {
	wcc.logger.Println("\n=== File Integrity Check ===")

	for seqID, path := range files {
		wcc.logger.Printf("\nChecking seqID %d: %s", seqID, path)

		// 1. 检查PATH_SEQ
		hasPathSeq, pathSeqPath := wcc.checkPATH_SEQ(seqID)
		wcc.logger.Printf("  PATH_SEQ: %v (%s)", hasPathSeq, pathSeqPath)

		// 2. 检查指纹索引
		hasFingerprint, fingerprintContent := wcc.checkFingerprint(path)
		wcc.logger.Printf("  Fingerprint: %v", hasFingerprint)

		// 3. 检查文件内容
		hasContent := wcc.checkFileContent(fingerprintContent)
		wcc.logger.Printf("  File Content: %v", hasContent)

		// 4. 检查0x0800索引
		has0x0800 := wcc.check0x0800(seqID)
		wcc.logger.Printf("  0x0800 Index: %v", has0x0800)

		// 5. 综合判断
		isComplete := hasPathSeq && hasFingerprint && hasContent
		status := "❌ INCOMPLETE"
		if isComplete {
			status = "✅ COMPLETE"
		}
		wcc.logger.Printf("  Status: %s", status)

		if !isComplete {
			wcc.logger.Printf("  Issues:")
			if !hasPathSeq {
				wcc.logger.Printf("    - Missing PATH_SEQ")
			}
			if !hasFingerprint {
				wcc.logger.Printf("    - Missing fingerprint index")
			}
			if !hasContent {
				wcc.logger.Printf("    - Missing file content data")
			}
		}
	}
}

func (wcc *WFSContentChecker) checkPATH_SEQ(seqID int64) (bool, string) {
	seqIDBytes := wcc.int64ToBytes(seqID)
	pathSeqKey := append(PATH_SEQ_CONTENT, seqIDBytes...)

	value, err := wcc.db.Get(pathSeqKey, nil)
	if err != nil {
		return false, ""
	}

	path, _, parseErr := wcc.parseWfsPathBean(value)
	if parseErr != nil {
		return false, ""
	}

	return true, path
}

func (wcc *WFSContentChecker) checkFingerprint(path string) (bool, []byte) {
	fingerprint := wcc.calculateFingerprint(path)
	content, err := wcc.db.Get(fingerprint, nil)
	if err != nil {
		return false, nil
	}
	return true, content
}

func (wcc *WFSContentChecker) checkFileContent(contentID []byte) bool {
	if contentID == nil {
		return false
	}

	// 尝试获取文件内容
	content, err := wcc.db.Get(contentID, nil)
	if err != nil {
		return false
	}

	// 检查内容是否为空
	return len(content) > 0
}

func (wcc *WFSContentChecker) check0x0800(seqID int64) bool {
	prefix0x0800 := []byte{0x08, 0x00}
	seqIDBytes := wcc.int64ToBytes(seqID)

	iter := wcc.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0x0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		// 检查key是否包含此seqID
		if wcc.keyContainsSeqID(key, seqIDBytes) {
			return true
		}
	}

	return false
}

func (wcc *WFSContentChecker) keyContainsSeqID(key, seqID []byte) bool {
	if len(key) >= len(seqID) {
		keyEnd := key[len(key)-len(seqID):]
		for i, b := range seqID {
			if keyEnd[i] != b {
				return false
			}
		}
		return true
	}
	return false
}

func (wcc *WFSContentChecker) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wcc *WFSContentChecker) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (wcc *WFSContentChecker) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func (wcc *WFSContentChecker) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wcc *WFSContentChecker) Close() {
	if wcc.db != nil {
		wcc.db.Close()
	}
}

func main() {
	config := &ContentCheckerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Content Checker\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	checker, err := NewWFSContentChecker(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer checker.Close()

	if err := checker.Check(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
