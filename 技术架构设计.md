# WFS 文件存储系统技术架构设计

## 1. 项目概述

### 1.1 主要特点
- **高性能存储引擎**: 专门针对海量小文件存储优化，实现微秒级响应
- **分布式架构**: 支持水平扩展和负载均衡
- **多协议支持**: 同时支持HTTP/HTTPS和Thrift协议
- **内置图像处理**: 提供丰富的图像处理功能
- **零依赖部署**: 核心功能无外部依赖

### 1.2 技术栈
- **后端语言**: Go 1.22.4
- **通信协议**: Apache Thrift + HTTP/HTTPS
- **存储引擎**: LevelDB
- **压缩算法**: Zlib + 自定义压缩
- **图像处理**: Go Image库 + WebP支持
- **Web框架**: 自研轻量级HTTP服务器

### 1.3 外部依赖
- `github.com/donnie4w/gothrift`: Thrift Go实现
- `github.com/syndtr/goleveldb`: LevelDB Go实现
- `github.com/donnie4w/tlnet`: 网络通信库
- `github.com/donnie4w/gofer`: 工具库集合

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TB
    Client[客户端应用] --> LB[负载均衡器 Nginx]
    WebClient[Web客户端] --> LB
    
    LB --> WFS1[WFS实例1]
    LB --> WFS2[WFS实例2]
    LB --> WFS3[WFS实例N]
    
    subgraph "WFS实例内部架构"
        HTTP[HTTP服务器:6801] --> TC[Web管理模块]
        Thrift[Thrift服务器:6802] --> Level1[业务逻辑层]
        
        TC --> Sys[系统核心]
        Level1 --> Sys
        
        Sys --> Stor[存储引擎]
        Stor --> LevelDB[(LevelDB)]
        Stor --> FileSystem[(文件系统)]
    end
```

### 2.2 数据流

```mermaid
sequenceDiagram
    participant C as 客户端
    participant T as Thrift服务
    participant P as 处理器
    participant S as 存储引擎
    participant L as LevelDB
    participant F as 文件系统

    C->>T: Exist(path)请求
    T->>P: 调用Exist处理器
    P->>P: 认证检查
    P->>S: GetData(path)
    S->>L: 查询文件索引
    L-->>S: 返回文件元数据
    S->>F: 读取文件数据
    F-->>S: 返回文件内容
    S-->>P: 返回文件数据
    P->>P: 计算文件大小
    P-->>T: 返回WfsExist结果
    T-->>C: 返回响应
```

## 3. 主要组件详解

### 3.1 类图

```mermaid
classDiagram
    class WfsIface {
        <<interface>>
        +Append(file) WfsAck
        +Delete(path) WfsAck
        +Rename(path, newpath) WfsAck
        +Auth(auth) WfsAck
        +Get(path) WfsData
        +Exist(path) WfsExist
        +Ping() int8
    }
    
    class processhandle {
        +Append(ctx, file) WfsAck
        +Delete(ctx, path) WfsAck
        +Rename(ctx, path, newpath) WfsAck
        +Auth(ctx, auth) WfsAck
        +Get(ctx, path) WfsData
        +Exist(ctx, path) WfsExist
        +Ping(ctx) int8
    }
    
    class WfsExist {
        +Exists bool
        +Size *int64
        +GetExists() bool
        +GetSize() int64
        +IsSetSize() bool
    }
    
    class StorageEngine {
        +AppendData(path, data, compress) int64
        +GetData(path) []byte
        +DelData(path) ERROR
        +SearchLike(prefix) []*PathBean
    }
    
    WfsIface <|.. processhandle
    processhandle --> StorageEngine
    processhandle --> WfsExist
```

### 3.2 存储引擎组件
存储引擎是WFS的核心组件，负责：
- **文件聚合**: 将多个小文件合并存储
- **索引管理**: 维护文件路径到物理位置的映射
- **压缩处理**: 支持多种压缩算法
- **缓存管理**: LRU缓存提高访问性能

### 3.3 业务处理器组件
处理器层实现具体的业务逻辑：
- **认证管理**: 用户身份验证
- **权限控制**: 访问权限检查
- **数据转换**: 协议数据与内部数据转换
- **错误处理**: 统一的错误处理机制

## 4. 关键数据结构

### 4.1 主要存储结构
```go
// 文件存在性响应结构
type WfsExist struct {
    Exists bool   `thrift:"exists,1,required"`
    Size   *int64 `thrift:"size,2"`
}

// 文件上传结构
type WfsFile struct {
    Data     []byte `thrift:"data,1,required"`
    Name     string `thrift:"name,2,required"`
    Compress *int8  `thrift:"compress,3"`
}

// 通用响应结构
type WfsAck struct {
    Ok    bool      `thrift:"ok,1,required"`
    Error *WfsError `thrift:"error,2"`
}
```

### 4.2 内存数据结构
- **LRU缓存**: 缓存热点文件数据
- **索引缓存**: 缓存文件路径映射
- **连接池**: 管理数据库连接

### 4.3 物理存储布局
```
数据文件结构:
[文件头2字节][文件类型1字节][压缩标志1字节][数据块1][数据块2]...[数据块N]

数据块结构:
[长度4字节][数据内容]

索引结构:
Key: 文件路径哈希
Value: {存储节点, 偏移量, 大小, 压缩类型, 时间戳}
```

## 5. 关键算法和流程

### 5.1 文件写入流程

```mermaid
flowchart TD
    A[接收文件上传请求] --> B[身份认证]
    B --> C{认证成功?}
    C -->|否| D[返回认证错误]
    C -->|是| E[检查文件大小限制]
    E --> F{大小合规?}
    F -->|否| G[返回大小超限错误]
    F -->|是| H[生成文件指纹]
    H --> I[检查重复文件]
    I --> J{文件已存在?}
    J -->|是| K[更新索引引用计数]
    J -->|否| L[选择压缩算法]
    L --> M[压缩文件数据]
    M --> N[分配存储空间]
    N --> O[写入物理文件]
    O --> P[更新LevelDB索引]
    P --> Q[更新缓存]
    Q --> R[返回成功响应]
```

### 5.2 文件读取流程

```mermaid
flowchart TD
    A[接收文件读取请求] --> B[身份认证]
    B --> C{认证成功?}
    C -->|否| D[返回认证错误]
    C -->|是| E[生成路径指纹]
    E --> F[检查LRU缓存]
    F --> G{缓存命中?}
    G -->|是| H[返回缓存数据]
    G -->|否| I[查询LevelDB索引]
    I --> J{索引存在?}
    J -->|否| K[返回文件不存在]
    J -->|是| L[读取物理文件]
    L --> M[解压缩数据]
    M --> N[更新LRU缓存]
    N --> O[返回文件数据]
```

### 5.3 Exist接口实现流程

```mermaid
flowchart TD
    A[接收Exist请求] --> B[身份认证]
    B --> C{认证成功?}
    C -->|否| D[返回认证错误]
    C -->|是| E[检查路径参数]
    E --> F{路径有效?}
    F -->|否| G[返回exists=false]
    F -->|是| H[调用GetData]
    H --> I[检查返回数据]
    I --> J{数据存在且长度>0?}
    J -->|否| K[返回exists=false]
    J -->|是| L[计算数据长度]
    L --> M[构造WfsExist响应]
    M --> N[设置exists=true, size=长度]
    N --> O[返回响应]
```

### 5.4 索引压缩算法
- **前缀压缩**: 相同前缀路径共享存储
- **增量编码**: 时间戳使用增量编码
- **变长编码**: 数值使用变长编码节省空间

### 5.5 块分配策略
- **顺序分配**: 新文件顺序写入当前活跃块
- **空间回收**: 删除文件后的空间标记为可重用
- **碎片整理**: 定期整理碎片空间

## 6. 并发控制

### 6.1 锁机制
- **读写锁**: 保护共享数据结构
- **文件锁**: 防止并发写入冲突
- **缓存锁**: 保护LRU缓存一致性

### 6.2 线程模型
- **主线程**: 处理服务启动和信号
- **HTTP服务线程**: 处理Web请求
- **Thrift服务线程**: 处理RPC请求
- **后台线程**: 执行清理和维护任务

## 7. 容错和恢复机制

### 7.1 索引保护
- **事务性写入**: 确保索引更新的原子性
- **检查点机制**: 定期创建索引快照
- **自动修复**: 启动时检查并修复损坏索引

### 7.2 数据完整性
- **校验和验证**: 文件数据包含校验和
- **重复检测**: 基于内容哈希的去重
- **备份机制**: 支持数据导出和导入

## 8. 性能优化

### 8.1 针对HDD的优化
- **顺序写入**: 减少磁盘寻道时间
- **批量操作**: 合并小的I/O操作
- **预读机制**: 预测性数据加载

### 8.2 并发优化
- **无锁数据结构**: 关键路径使用无锁算法
- **连接复用**: 复用数据库连接
- **异步处理**: 非关键操作异步执行

### 8.3 内存优化
- **对象池**: 重用频繁分配的对象
- **内存映射**: 大文件使用内存映射
- **垃圾回收调优**: 优化GC参数

## 9. 可扩展性考虑

- **水平扩展**: 通过负载均衡支持多实例部署
- **存储扩展**: 支持多存储节点配置
- **协议扩展**: 模块化设计便于添加新协议
- **功能扩展**: 插件化架构支持功能扩展

## 10. 限制和约束

- **单文件大小**: 默认限制10MB（可配置）
- **内存使用**: 默认限制128MB（可配置）
- **并发连接**: 受操作系统文件描述符限制
- **存储容量**: 受文件系统和硬件限制

## 11. 未来改进方向

- **分布式存储**: 原生分布式存储支持
- **多副本机制**: 数据冗余和高可用
- **流式处理**: 大文件流式上传下载
- **智能缓存**: 基于机器学习的缓存策略
- **监控告警**: 完善的监控和告警系统

## 12. 系统测试与性能评估

### 12.1 测试方法
采用Go语言内置的benchmark测试框架进行性能测试，测试环境包括不同CPU核心数配置。

### 12.2 基本功能测试
- **接口完整性**: 验证所有Thrift接口正常工作
- **数据一致性**: 验证读写数据的一致性
- **错误处理**: 验证各种错误场景的处理

### 12.3 性能测试
基于benchmark测试结果：

**写入性能**:
- 4核环境: 27,405 - 34,129 ops/s
- 8核环境: 31,945 - 41,593 ops/s

**读取性能**:
- 4核环境: 536,000 - 1,085,776 ops/s  
- 8核环境: 578,034 - 1,572,327 ops/s

### 12.4 Exist接口测试
新增的Exist接口经过以下测试：
- **存在文件**: 正确返回exists=true和文件大小
- **不存在文件**: 正确返回exists=false
- **空路径**: 正确处理边界情况
- **认证检查**: 验证权限控制机制

### 12.5 性能基准
- **响应时间**: 读取操作平均636-1865纳秒
- **写入时间**: 写入操作平均24,042-36,489纳秒
- **内存使用**: 每操作0-48字节内存分配
- **吞吐量**: 读取可达150万ops/s，写入可达4万ops/s

### 12.6 测试工具辅助功能
- **压力测试**: 模拟高并发访问场景
- **稳定性测试**: 长时间运行稳定性验证
- **兼容性测试**: 多平台兼容性验证

### 12.7 测试结果分析
测试结果表明WFS系统在处理海量小文件方面具有优异性能，特别是读取操作性能突出，能够满足高并发Web应用的需求。新增的Exist接口在保持高性能的同时，为用户提供了便捷的文件存在性检查功能。

---

本技术架构设计文档详细描述了WFS文件存储系统的整体架构、关键组件、核心算法和性能特征，为系统的开发、部署和维护提供了全面的技术指导。

## 附录：新增Exist接口技术细节

### A.1 Thrift接口定义
```thrift
// 文件存在性响应结构
struct WfsExist {
    1: required bool exists,
    2: optional i64 size
}

// 服务接口
service WfsIface {
    // 检查文件是否存在并获取大小
    WfsExist Exist(1: string path)
}
```

### A.2 Go实现代码示例
```go
// 业务逻辑实现
func (t *processhandle) Exist(ctx context.Context, path string) (_r *WfsExist, _err error) {
    defer util.Recover()
    cc := ctx2CliContext(ctx)
    cc.mux.Lock()
    defer cc.mux.Unlock()

    if noAuthAndClose(cc) {
        _err = sys.ERR_AUTH.Error()
        return
    }

    _r = &WfsExist{Exists: false}
    if path != "" {
        if data := sys.GetData(path); len(data) > 0 {
            _r.Exists = true
            size := int64(len(data))
            _r.Size = &size
        }
    }
    return
}
```

### A.3 客户端调用示例
```go
// 创建客户端连接
client := stub.NewWfsIfaceClient(transport)

// 调用Exist接口
existResult, err := client.Exist(context.Background(), "test/file.txt")
if err != nil {
    log.Printf("调用失败: %v", err)
} else {
    fmt.Printf("文件存在: %v", existResult.GetExists())
    if existResult.IsSetSize() {
        fmt.Printf("文件大小: %d 字节", existResult.GetSize())
    }
}
```
