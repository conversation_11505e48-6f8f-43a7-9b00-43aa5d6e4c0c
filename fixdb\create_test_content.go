// Create Test Content for WFS System
// 为WFS系统创建测试文件内容，解决文件内容缺失问题

package main

import (
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE = []byte{0x00, 0x00}
	PATH_SEQ = []byte{0x01, 0x00}
)

// 测试内容创建器
type TestContentCreator struct {
	db     *leveldb.DB
	logger *log.Logger
	dryRun bool
}

// 创建测试内容创建器
func NewTestContentCreator(dbPath string, dryRun bool) (*TestContentCreator, error) {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     64 * 1024 * 1024,
		WriteBuffer:            16 * 1024 * 1024,
		ReadOnly:               dryRun,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}

	return &TestContentCreator{
		db:     db,
		logger: log.New(os.Stdout, "[TestContentCreator] ", log.LstdFlags),
		dryRun: dryRun,
	}, nil
}

// 关闭创建器
func (tcc *TestContentCreator) Close() error {
	if tcc.db != nil {
		return tcc.db.Close()
	}
	return nil
}

// 计算文件路径的MD5指纹
func (tcc *TestContentCreator) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

// 解析protobuf格式的WfsPathBean
func (tcc *TestContentCreator) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 创建测试文件内容
func (tcc *TestContentCreator) createTestFileContent(fileName string) []byte {
	// 创建一个简单的测试文件内容
	content := fmt.Sprintf("Test file content for %s\nCreated at: %s\nFile size: %d bytes\n",
		fileName, time.Now().Format("2006-01-02 15:04:05"), len(fileName)*10)
	
	// 填充一些数据使文件看起来更真实
	for i := 0; i < 100; i++ {
		content += fmt.Sprintf("Line %d: This is test data for file %s\n", i+1, fileName)
	}
	
	return []byte(content)
}

// 生成内容ID
func (tcc *TestContentCreator) generateContentID(fileName string) []byte {
	// 使用文件名和当前时间生成唯一的内容ID
	data := fmt.Sprintf("%s_%d", fileName, time.Now().UnixNano())
	hash := md5.Sum([]byte(data))
	return hash[:]
}

// 创建测试内容
func (tcc *TestContentCreator) CreateTestContent() error {
	tcc.logger.Println("=== Creating Test Content for WFS Files ===")

	// 获取所有PATH_SEQ条目
	iter := tcc.db.NewIterator(levelutil.BytesPrefix(PATH_SEQ), nil)
	defer iter.Release()

	createdCount := 0
	batch := new(leveldb.Batch)

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_SEQ) {
			continue
		}

		// 解析seqID
		seqIDBytes := key[len(PATH_SEQ):]
		seqID := int64(binary.BigEndian.Uint64(seqIDBytes))

		// 解析WfsPathBean
		fileName, timestamp, err := tcc.parseWfsPathBean(value)
		if err != nil {
			tcc.logger.Printf("Failed to parse PATH_SEQ seqID %d: %v", seqID, err)
			continue
		}

		tcc.logger.Printf("Processing file: %s (seqID: %d, timestamp: %d)", fileName, seqID, timestamp)

		// 计算文件路径的指纹
		fingerprint := tcc.calculateFingerprint(fileName)

		// 检查指纹索引是否已存在
		if exists, err := tcc.db.Has(fingerprint, nil); err == nil && exists {
			tcc.logger.Printf("  Fingerprint already exists for %s, skipping", fileName)
			continue
		}

		// 生成内容ID和测试内容
		contentID := tcc.generateContentID(fileName)
		testContent := tcc.createTestFileContent(fileName)

		if tcc.dryRun {
			tcc.logger.Printf("  DRY RUN: Would create fingerprint %x -> contentID %x", fingerprint, contentID)
			tcc.logger.Printf("  DRY RUN: Would create content %x -> %d bytes", contentID, len(testContent))
		} else {
			// 创建指纹索引：fingerprint -> contentID
			batch.Put(fingerprint, contentID)
			
			// 创建文件内容：contentID -> file content
			batch.Put(contentID, testContent)
			
			tcc.logger.Printf("  Created fingerprint %x -> contentID %x", fingerprint, contentID)
			tcc.logger.Printf("  Created content %x -> %d bytes", contentID, len(testContent))
		}

		createdCount++

		// 每10个条目执行一次批处理
		if !tcc.dryRun && createdCount%10 == 0 {
			if err := tcc.db.Write(batch, &opt.WriteOptions{Sync: false}); err != nil {
				tcc.logger.Printf("Error writing batch: %v", err)
				return err
			}
			batch.Reset()
		}
	}

	// 执行剩余的批处理
	if !tcc.dryRun && createdCount > 0 {
		if err := tcc.db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
			tcc.logger.Printf("Error writing final batch: %v", err)
			return err
		}
	}

	// 检查迭代器错误
	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	tcc.logger.Printf("Test content creation completed: %d files processed", createdCount)

	if tcc.dryRun {
		tcc.logger.Println("DRY RUN completed - use -create to perform actual creation")
	} else if createdCount > 0 {
		tcc.logger.Println("✅ Test content creation successful!")
		tcc.logger.Println("💡 Files should now appear in WFS web interface")
		tcc.logger.Println("💡 Restart WFS service and check web interface")
	} else {
		tcc.logger.Println("✅ No content creation needed - all files already have content")
	}

	return nil
}

// 验证创建结果
func (tcc *TestContentCreator) Verify() error {
	tcc.logger.Println("=== Verifying Test Content Creation ===")

	// 检查PATH_SEQ条目
	iter := tcc.db.NewIterator(levelutil.BytesPrefix(PATH_SEQ), nil)
	defer iter.Release()

	verifiedCount := 0
	missingCount := 0

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_SEQ) {
			continue
		}

		// 解析文件名
		fileName, _, err := tcc.parseWfsPathBean(value)
		if err != nil {
			continue
		}

		// 检查指纹索引
		fingerprint := tcc.calculateFingerprint(fileName)
		if contentID, err := tcc.db.Get(fingerprint, nil); err == nil {
			// 检查文件内容
			if content, err := tcc.db.Get(contentID, nil); err == nil {
				verifiedCount++
				tcc.logger.Printf("✅ VERIFIED: %s -> fingerprint %x -> content %d bytes", 
					fileName, fingerprint, len(content))
			} else {
				missingCount++
				tcc.logger.Printf("❌ MISSING CONTENT: %s -> fingerprint %x -> contentID %x (content missing)", 
					fileName, fingerprint, contentID)
			}
		} else {
			missingCount++
			tcc.logger.Printf("❌ MISSING FINGERPRINT: %s -> fingerprint %x", fileName, fingerprint)
		}
	}

	tcc.logger.Printf("Verification Summary:")
	tcc.logger.Printf("  Files with complete content: %d", verifiedCount)
	tcc.logger.Printf("  Files with missing content: %d", missingCount)

	if missingCount == 0 && verifiedCount > 0 {
		tcc.logger.Println("✅ Verification successful - all files have content!")
	} else if verifiedCount == 0 {
		tcc.logger.Println("❌ Verification failed - no files have content!")
	} else {
		tcc.logger.Printf("⚠️  Verification partial - %d files still missing content", missingCount)
	}

	return nil
}

// 主函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Create Test Content for WFS System")
		fmt.Println("Usage: create_test_content <db_path> [options]")
		fmt.Println("")
		fmt.Println("Options:")
		fmt.Println("  -dry-run    : Preview content creation without making changes")
		fmt.Println("  -create     : Create actual test content")
		fmt.Println("  -verify     : Verify content creation results")
		fmt.Println("")
		fmt.Println("Examples:")
		fmt.Println("  create_test_content db_path -dry-run")
		fmt.Println("  create_test_content db_path -create")
		fmt.Println("  create_test_content db_path -verify")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	dryRun := false
	performCreate := false
	verifyOnly := false

	// 解析参数
	for i := 2; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-dry-run":
			dryRun = true
		case "-create":
			performCreate = true
		case "-verify":
			verifyOnly = true
		}
	}

	// 默认为dry-run模式
	if !dryRun && !performCreate && !verifyOnly {
		dryRun = true
	}

	// 创建测试内容创建器
	creator, err := NewTestContentCreator(dbPath, dryRun || verifyOnly)
	if err != nil {
		log.Fatalf("Failed to create test content creator: %v", err)
	}
	defer creator.Close()

	// 执行操作
	if verifyOnly {
		if err := creator.Verify(); err != nil {
			log.Fatalf("Verification failed: %v", err)
		}
	} else {
		if err := creator.CreateTestContent(); err != nil {
			log.Fatalf("Content creation failed: %v", err)
		}

		// 创建后验证
		if !dryRun {
			if err := creator.Verify(); err != nil {
				log.Fatalf("Post-creation verification failed: %v", err)
			}
		}
	}
}
