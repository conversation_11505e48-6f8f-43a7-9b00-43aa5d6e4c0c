// WFS集成修复工具 - 一键修复文件名路径前缀问题
// 支持高并发，集成所有修复步骤
package main

import (
	"fmt"
	"hash/crc64"
	"log"
	"os"
	"sync"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_INTEGRATED = []byte{0x00, 0x00}
	PATH_SEQ_INTEGRATED = []byte{0x01, 0x00}
	INDEX_0800          = []byte{0x08, 0x00}
)

type IntegratedFixerConfig struct {
	DatabasePath  string
	ReferencePath string
	DryRun        bool
	Verbose       bool
	Workers       int // 并发工作线程数
}

type IntegratedFixer struct {
	config   *IntegratedFixerConfig
	db       *leveldb.DB
	refDB    *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
	mu       sync.RWMutex
}

type FileInfo struct {
	Path      string
	SeqID     int64
	CleanPath string
}

type FixResult struct {
	Step     string
	Success  bool
	Count    int
	Error    error
	Duration time.Duration
}

func NewIntegratedFixer(config *IntegratedFixerConfig) (*IntegratedFixer, error) {
	logger := log.New(os.Stdout, "[WFSIntegratedFixer] ", log.LstdFlags)

	// 数据库选项
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	// 打开主数据库
	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open database normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	// 打开参考数据库（如果提供）
	var refDB *leveldb.DB
	if config.ReferencePath != "" {
		refOptions := &opt.Options{
			Filter:                 filter.NewBloomFilter(10),
			OpenFilesCacheCapacity: 1 << 8,
			BlockCacheCapacity:     32 * 1024 * 1024,
			WriteBuffer:            8 * 1024 * 1024,
			ReadOnly:               true,
			ErrorIfMissing:         false,
			Strict:                 opt.NoStrict,
		}

		refDB, err = leveldb.OpenFile(config.ReferencePath, refOptions)
		if err != nil {
			logger.Printf("Warning: Failed to open reference database: %v", err)
		} else {
			logger.Printf("Successfully opened reference database")
		}
	}

	// 创建CRC64表
	crcTable := crc64.MakeTable(crc64.ISO)

	return &IntegratedFixer{
		config:   config,
		db:       db,
		refDB:    refDB,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (iff *IntegratedFixer) Fix() error {
	iff.logger.Println("=== WFS Integrated Fix - Complete File Path Cleanup ===")
	iff.logger.Printf("Workers: %d", iff.config.Workers)

	startTime := time.Now()
	results := make([]FixResult, 0)

	// 步骤1: 收集所有文件信息
	iff.logger.Println("\n🔍 Step 1: Collecting file information...")
	stepStart := time.Now()
	files, err := iff.collectFiles()
	if err != nil {
		return fmt.Errorf("step 1 failed: %v", err)
	}
	results = append(results, FixResult{
		Step:     "Collect Files",
		Success:  true,
		Count:    len(files),
		Duration: time.Since(stepStart),
	})

	// 步骤2: 修复PATH_PRE索引
	iff.logger.Println("\n🔧 Step 2: Fixing PATH_PRE index...")
	stepStart = time.Now()
	count, err := iff.fixPATH_PRE(files)
	results = append(results, FixResult{
		Step:     "Fix PATH_PRE",
		Success:  err == nil,
		Count:    count,
		Error:    err,
		Duration: time.Since(stepStart),
	})
	if err != nil {
		return fmt.Errorf("step 2 failed: %v", err)
	}

	// 步骤3: 修复0x0800索引
	iff.logger.Println("\n🔧 Step 3: Fixing 0x0800 index...")
	stepStart = time.Now()
	count, err = iff.fix0x0800(files)
	results = append(results, FixResult{
		Step:     "Fix 0x0800",
		Success:  err == nil,
		Count:    count,
		Error:    err,
		Duration: time.Since(stepStart),
	})
	if err != nil {
		return fmt.Errorf("step 3 failed: %v", err)
	}

	// 步骤4: 修复CRC64指纹索引
	iff.logger.Println("\n🔧 Step 4: Fixing CRC64 fingerprint index...")
	stepStart = time.Now()
	count, err = iff.fixFingerprints(files)
	results = append(results, FixResult{
		Step:     "Fix Fingerprints",
		Success:  err == nil,
		Count:    count,
		Error:    err,
		Duration: time.Since(stepStart),
	})
	if err != nil {
		return fmt.Errorf("step 4 failed: %v", err)
	}

	// 打印总结
	iff.printSummary(results, time.Since(startTime))

	return nil
}

// collectFiles 收集所有文件信息
func (iff *IntegratedFixer) collectFiles() (map[int64]*FileInfo, error) {
	iff.logger.Println("Collecting files from PATH_PRE index...")

	files := make(map[int64]*FileInfo)
	iter := iff.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_INTEGRATED[0] && key[1] == PATH_PRE_INTEGRATED[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := iff.bytesToInt64(seqIDBytes)

				cleanPath := iff.cleanFileName(path)
				needsClean := path != cleanPath

				files[seqID] = &FileInfo{
					Path:      path,
					SeqID:     seqID,
					CleanPath: cleanPath,
				}

				if iff.config.Verbose && needsClean {
					iff.logger.Printf("File needs cleaning: seqID=%d, %s -> %s", seqID, path, cleanPath)
				}
			}
		}
	}

	iff.logger.Printf("Found %d files", len(files))
	return files, nil
}

// fixPATH_PRE 修复PATH_PRE索引
func (iff *IntegratedFixer) fixPATH_PRE(files map[int64]*FileInfo) (int, error) {
	iff.logger.Println("Fixing PATH_PRE index...")

	batch := new(leveldb.Batch)
	fixedCount := 0

	for _, file := range files {
		if file.Path != file.CleanPath {
			// 删除旧的PATH_PRE
			oldPathPre := append(PATH_PRE_INTEGRATED, []byte(file.Path)...)
			batch.Delete(oldPathPre)

			// 添加新的PATH_PRE
			newPathPre := append(PATH_PRE_INTEGRATED, []byte(file.CleanPath)...)
			seqIDBytes := iff.int64ToBytes(file.SeqID)
			batch.Put(newPathPre, seqIDBytes)

			fixedCount++
			if iff.config.Verbose {
				iff.logger.Printf("Fixing PATH_PRE: seqID=%d, %s -> %s", file.SeqID, file.Path, file.CleanPath)
			}
		}
	}

	if fixedCount > 0 {
		if iff.config.DryRun {
			iff.logger.Printf("[DRY RUN] Would fix %d PATH_PRE entries", fixedCount)
		} else {
			if err := iff.db.Write(batch, nil); err != nil {
				return 0, fmt.Errorf("failed to write batch: %v", err)
			}
			iff.logger.Printf("✅ Fixed %d PATH_PRE entries", fixedCount)
		}
	} else {
		iff.logger.Println("✅ All PATH_PRE entries are already clean")
	}

	return fixedCount, nil
}

// fix0x0800 修复0x0800索引
func (iff *IntegratedFixer) fix0x0800(files map[int64]*FileInfo) (int, error) {
	iff.logger.Println("Fixing 0x0800 index...")

	// 收集所有0x0800条目
	iter := iff.db.NewIterator(nil, nil)
	defer iter.Release()

	batch := new(leveldb.Batch)
	fixedCount := 0

	for iter.Seek(INDEX_0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != INDEX_0800[0] || key[1] != INDEX_0800[1] {
			break
		}

		value := iter.Value()

		// 解析WfsPathBean
		path, timestamp, err := iff.parseWfsPathBean(value)
		if err != nil {
			iff.logger.Printf("Warning: Failed to parse 0x0800 entry: %v", err)
			continue
		}

		// 清理路径
		cleanPath := iff.cleanFileName(path)
		if path != cleanPath {
			// 创建新的WfsPathBean
			newValue := iff.encodeWfsPathBean(cleanPath, timestamp)

			if iff.config.Verbose {
				iff.logger.Printf("Fixing 0x0800: %s -> %s", path, cleanPath)
			}

			if iff.config.DryRun {
				iff.logger.Printf("[DRY RUN] Would fix: %s -> %s", path, cleanPath)
			} else {
				batch.Put(key, newValue)
			}
			fixedCount++
		}
	}

	if fixedCount > 0 && !iff.config.DryRun {
		if err := iff.db.Write(batch, nil); err != nil {
			return 0, fmt.Errorf("failed to write batch: %v", err)
		}
		iff.logger.Printf("✅ Fixed %d 0x0800 entries", fixedCount)
	} else if fixedCount > 0 {
		iff.logger.Printf("[DRY RUN] Would fix %d 0x0800 entries", fixedCount)
	} else {
		iff.logger.Println("✅ All 0x0800 entries are already clean")
	}

	return fixedCount, nil
}
