// Database inspection tool
// 数据库检查工具，用于查看数据库中的key结构

package main

import (
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

func inspectDatabase(dbPath string) error {
	log.Printf("Inspecting database: %s", dbPath)

	// 打开数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     8 * 1024 * 1024,
		WriteBuffer:            4 * 1024 * 1024,
		ReadOnly:               true, // 只读模式
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		// 尝试恢复
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}
	defer db.Close()

	// 遍历所有key
	iter := db.NewIterator(nil, nil)
	defer iter.Release()

	keyCount := 0
	pathKeyCount := 0
	prefixMap := make(map[string]int)
	pathKeys := make([]string, 0)

	log.Printf("Scanning all keys...")

	for iter.Next() {
		key := iter.Key()
		keyCount++

		// 分析key的前缀
		if len(key) >= 2 {
			prefix := fmt.Sprintf("%02x%02x", key[0], key[1])
			prefixMap[prefix]++
		}

		// 检查是否包含路径分隔符
		keyStr := string(key)
		if len(keyStr) > 2 { // 跳过前缀部分
			pathPart := keyStr[2:] // 假设前2字节是前缀
			if containsPathSeparator(pathPart) {
				pathKeyCount++
				pathKeys = append(pathKeys, keyStr)
				log.Printf("Found path key: %s (hex: %x)", keyStr, key)
			}
		}

		// 限制输出数量
		if keyCount <= 20 {
			log.Printf("Key %d: %s (hex: %x)", keyCount, keyStr, key)
		}
	}

	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	// 输出统计信息
	log.Printf("\n=== Database Statistics ===")
	log.Printf("Total keys: %d", keyCount)
	log.Printf("Keys with path separators: %d", pathKeyCount)
	
	log.Printf("\nPrefix distribution:")
	for prefix, count := range prefixMap {
		log.Printf("  %s: %d keys", prefix, count)
	}

	if len(pathKeys) > 0 {
		log.Printf("\nPath keys found:")
		for i, key := range pathKeys {
			if i < 10 { // 只显示前10个
				log.Printf("  %s", key)
			}
		}
		if len(pathKeys) > 10 {
			log.Printf("  ... and %d more", len(pathKeys)-10)
		}
	}

	// 特别检查PATH_PRE前缀 (0x0000)
	PATH_PRE := []byte{0, 0}
	pathPreCount := 0
	iter2 := db.NewIterator(nil, nil)
	defer iter2.Release()

	for iter2.Next() {
		key := iter2.Key()
		if len(key) >= len(PATH_PRE) {
			hasPrefix := true
			for i, b := range PATH_PRE {
				if key[i] != b {
					hasPrefix = false
					break
				}
			}
			if hasPrefix {
				pathPreCount++
				if pathPreCount <= 5 {
					log.Printf("PATH_PRE key: %s (hex: %x)", string(key), key)
				}
			}
		}
	}

	log.Printf("\nPATH_PRE (0x0000) prefixed keys: %d", pathPreCount)

	return nil
}

func containsPathSeparator(s string) bool {
	for _, c := range s {
		if c == '/' || c == '\\' {
			return true
		}
	}
	return false
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: inspect_db <db_path>")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	if err := inspectDatabase(dbPath); err != nil {
		log.Fatalf("Inspection failed: %v", err)
	}
}
