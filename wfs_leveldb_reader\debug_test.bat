@echo off
chcp 65001 >nul

echo LevelDB Database Diagnostic Tool
echo =================================

if "%1"=="" (
    echo Usage: %0 ^<leveldb_path^>
    echo Example: %0 C:\wfsdata\wfsdb
    pause
    exit /b 1
)

set DB_PATH=%1

echo Compiling diagnostic tool...
g++ -std=c++17 debug_leveldb.cpp -o debug_leveldb.exe 2>nul

if not exist debug_leveldb.exe (
    echo Compilation failed, creating simple diagnostic...
    goto :simple_diagnostic
)

echo Running diagnostic tool...
debug_leveldb.exe "%DB_PATH%"
goto :end

:simple_diagnostic
echo.
echo === Simple Database Diagnostic ===
echo Database path: %DB_PATH%

if not exist "%DB_PATH%" (
    echo ERROR: Database path does not exist!
    goto :end
)

echo Database path exists

if not exist "%DB_PATH%\CURRENT" (
    echo WARNING: CURRENT file missing
) else (
    echo CURRENT file exists
)

if not exist "%DB_PATH%\MANIFEST-*" (
    echo WARNING: No MANIFEST files found
) else (
    echo MANIFEST files found
)

echo.
echo Directory contents:
dir "%DB_PATH%" /b

echo.
echo File sizes:
for %%f in ("%DB_PATH%\*") do (
    echo %%~nxf: %%~zf bytes
)

echo.
echo === Diagnostic Complete ===

:end
pause
