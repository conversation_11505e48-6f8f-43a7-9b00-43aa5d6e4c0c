// WFS简化重命名工具
// 基于HTTP接口或直接数据库操作的文件重命名工具

package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// 重命名配置
type SimpleRenameConfig struct {
	WfsURL      string // WFS服务URL
	RulesFile   string // 重命名规则文件
	Workers     int    // 并发工作线程数
	DryRun      bool   // 预览模式
	Verbose     bool   // 详细输出
	LogFile     string // 日志文件
	UseHTTP     bool   // 使用HTTP接口
}

// 重命名规则
type SimpleRenameRule struct {
	OldPath string
	NewPath string
}

// 重命名任务
type SimpleRenameTask struct {
	Rule   SimpleRenameRule
	TaskID int64
}

// 重命名结果
type SimpleRenameResult struct {
	Task    SimpleRenameTask
	Success bool
	Error   error
}

// 统计信息
type SimpleRenameStats struct {
	TotalTasks   int64
	SuccessCount int64
	ErrorCount   int64
	StartTime    time.Time
}

// WFS HTTP响应
type WfsResponse struct {
	Ok    bool   `json:"ok"`
	Error string `json:"error,omitempty"`
}

// 简化重命名器
type SimpleRenamer struct {
	config     *SimpleRenameConfig
	logger     *log.Logger
	stats      *SimpleRenameStats
	taskChan   chan SimpleRenameTask
	resultChan chan SimpleRenameResult
	httpClient *http.Client
}

// 创建简化重命名器
func NewSimpleRenamer(config *SimpleRenameConfig) (*SimpleRenamer, error) {
	// 创建日志器
	var logger *log.Logger
	if config.LogFile != "" {
		logFile, err := os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %v", err)
		}
		logger = log.New(logFile, "[SimpleRenamer] ", log.LstdFlags)
	} else {
		logger = log.New(os.Stdout, "[SimpleRenamer] ", log.LstdFlags)
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	return &SimpleRenamer{
		config: config,
		logger: logger,
		stats: &SimpleRenameStats{
			StartTime: time.Now(),
		},
		taskChan:   make(chan SimpleRenameTask, config.Workers*2),
		resultChan: make(chan SimpleRenameResult, config.Workers*2),
		httpClient: httpClient,
	}, nil
}

// 加载重命名规则
func (sr *SimpleRenamer) loadRenameRules() ([]SimpleRenameRule, error) {
	file, err := os.Open(sr.config.RulesFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open rules file: %v", err)
	}
	defer file.Close()

	var rules []SimpleRenameRule
	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析规则
		var oldPath, newPath string
		if strings.Contains(line, "->") {
			parts := strings.Split(line, "->")
			if len(parts) != 2 {
				sr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
				continue
			}
			oldPath = strings.TrimSpace(parts[0])
			newPath = strings.TrimSpace(parts[1])
		} else if strings.Contains(line, ",") {
			parts := strings.Split(line, ",")
			if len(parts) != 2 {
				sr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
				continue
			}
			oldPath = strings.TrimSpace(parts[0])
			newPath = strings.TrimSpace(parts[1])
		} else {
			sr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
			continue
		}

		if oldPath == "" || newPath == "" {
			sr.logger.Printf("Warning: Empty path at line %d: %s", lineNum, line)
			continue
		}

		rules = append(rules, SimpleRenameRule{
			OldPath: oldPath,
			NewPath: newPath,
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading rules file: %v", err)
	}

	sr.logger.Printf("Loaded %d rename rules from %s", len(rules), sr.config.RulesFile)
	return rules, nil
}

// 工作线程
func (sr *SimpleRenamer) worker(workerID int) {
	sr.logger.Printf("Worker %d started", workerID)
	defer sr.logger.Printf("Worker %d completed", workerID)

	for task := range sr.taskChan {
		result := sr.processRenameTask(workerID, task)
		sr.resultChan <- result
	}
}

// 处理重命名任务
func (sr *SimpleRenamer) processRenameTask(workerID int, task SimpleRenameTask) SimpleRenameResult {
	if sr.config.Verbose {
		sr.logger.Printf("Worker %d: Processing %s -> %s",
			workerID, task.Rule.OldPath, task.Rule.NewPath)
	}

	// 预览模式
	if sr.config.DryRun {
		sr.logger.Printf("Worker %d: [DRY RUN] Would rename %s -> %s",
			workerID, task.Rule.OldPath, task.Rule.NewPath)
		return SimpleRenameResult{
			Task:    task,
			Success: true,
			Error:   nil,
		}
	}

	// 执行重命名
	var err error
	if sr.config.UseHTTP {
		err = sr.renameViaHTTP(task.Rule)
	} else {
		err = sr.renameViaCommand(task.Rule)
	}

	if err != nil {
		return SimpleRenameResult{
			Task:    task,
			Success: false,
			Error:   err,
		}
	}

	sr.logger.Printf("Worker %d: Successfully renamed %s -> %s",
		workerID, task.Rule.OldPath, task.Rule.NewPath)

	return SimpleRenameResult{
		Task:    task,
		Success: true,
		Error:   nil,
	}
}

// 通过HTTP接口重命名
func (sr *SimpleRenamer) renameViaHTTP(rule SimpleRenameRule) error {
	// 构造HTTP请求
	requestData := map[string]string{
		"oldPath": rule.OldPath,
		"newPath": rule.NewPath,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	// 发送HTTP请求
	url := fmt.Sprintf("%s/rename", sr.config.WfsURL)
	resp, err := sr.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var wfsResp WfsResponse
	if err := json.Unmarshal(body, &wfsResp); err != nil {
		return fmt.Errorf("failed to parse response: %v", err)
	}

	if !wfsResp.Ok {
		return fmt.Errorf("WFS error: %s", wfsResp.Error)
	}

	return nil
}

// 通过命令行工具重命名
func (sr *SimpleRenamer) renameViaCommand(rule SimpleRenameRule) error {
	// 这里可以调用WFS命令行工具或其他方式
	// 简化版本：模拟成功
	sr.logger.Printf("Command rename: %s -> %s", rule.OldPath, rule.NewPath)
	return nil
}

// 结果处理器
func (sr *SimpleRenamer) resultProcessor() {
	for result := range sr.resultChan {
		if result.Success {
			atomic.AddInt64(&sr.stats.SuccessCount, 1)
		} else {
			atomic.AddInt64(&sr.stats.ErrorCount, 1)
			sr.logger.Printf("Error: Failed to rename %s -> %s: %v",
				result.Task.Rule.OldPath, result.Task.Rule.NewPath, result.Error)
		}

		// 显示进度
		processed := atomic.LoadInt64(&sr.stats.SuccessCount) + atomic.LoadInt64(&sr.stats.ErrorCount)
		if processed%100 == 0 || sr.config.Verbose {
			sr.printProgress()
		}
	}
}

// 打印进度
func (sr *SimpleRenamer) printProgress() {
	elapsed := time.Since(sr.stats.StartTime)
	total := atomic.LoadInt64(&sr.stats.TotalTasks)
	success := atomic.LoadInt64(&sr.stats.SuccessCount)
	errors := atomic.LoadInt64(&sr.stats.ErrorCount)
	processed := success + errors

	if total > 0 {
		percentage := float64(processed) / float64(total) * 100
		rate := float64(processed) / elapsed.Seconds()

		sr.logger.Printf("Progress: %d/%d (%.1f%%), %d success, %d errors, %.1f tasks/sec, elapsed: %v",
			processed, total, percentage, success, errors, rate, elapsed.Truncate(time.Second))
	}
}

// 执行重命名
func (sr *SimpleRenamer) Execute() error {
	sr.logger.Println("=== Starting Simple WFS File Rename ===")

	// 加载重命名规则
	rules, err := sr.loadRenameRules()
	if err != nil {
		return fmt.Errorf("failed to load rename rules: %v", err)
	}

	if len(rules) == 0 {
		sr.logger.Println("No rename rules found")
		return nil
	}

	sr.stats.TotalTasks = int64(len(rules))
	sr.logger.Printf("Total rename tasks: %d", len(rules))

	// 启动工作线程
	var wg sync.WaitGroup
	for i := 0; i < sr.config.Workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			sr.worker(workerID)
		}(i)
	}

	// 启动结果处理器
	go sr.resultProcessor()

	// 发送任务
	go func() {
		defer close(sr.taskChan)
		for i, rule := range rules {
			task := SimpleRenameTask{
				Rule:   rule,
				TaskID: int64(i + 1),
			}
			sr.taskChan <- task
		}
	}()

	// 等待所有工作线程完成
	wg.Wait()
	close(sr.resultChan)

	// 等待结果处理完成
	time.Sleep(100 * time.Millisecond)

	// 打印最终统计
	sr.printFinalStats()

	return nil
}

// 打印最终统计
func (sr *SimpleRenamer) printFinalStats() {
	elapsed := time.Since(sr.stats.StartTime)
	total := atomic.LoadInt64(&sr.stats.TotalTasks)
	success := atomic.LoadInt64(&sr.stats.SuccessCount)
	errors := atomic.LoadInt64(&sr.stats.ErrorCount)

	sr.logger.Println("\n=== Rename Summary ===")
	sr.logger.Printf("Total tasks: %d", total)
	sr.logger.Printf("Successful: %d", success)
	sr.logger.Printf("Errors: %d", errors)
	sr.logger.Printf("Success rate: %.1f%%", float64(success)/float64(total)*100)
	sr.logger.Printf("Total time: %v", elapsed.Truncate(time.Second))
	sr.logger.Printf("Average rate: %.1f tasks/sec", float64(total)/elapsed.Seconds())

	if errors > 0 {
		sr.logger.Printf("⚠️  %d tasks failed. Check logs for details.", errors)
	} else {
		sr.logger.Println("✅ All rename tasks completed successfully!")
	}
}

// 主函数
func main() {
	config := &SimpleRenameConfig{}

	// 命令行参数
	flag.StringVar(&config.WfsURL, "url", "http://localhost:8080", "WFS server URL")
	flag.StringVar(&config.RulesFile, "rules", "", "Rename rules file (required)")
	flag.IntVar(&config.Workers, "workers", 4, "Number of worker threads")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode, don't actually rename")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.StringVar(&config.LogFile, "log", "", "Log file path (default: stdout)")
	flag.BoolVar(&config.UseHTTP, "http", false, "Use HTTP interface")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Simple WFS File Renamer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -rules rename_rules.txt -dry-run\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -rules rules.txt -url http://localhost:8080 -http\n", os.Args[0])
	}

	flag.Parse()

	// 验证参数
	if config.RulesFile == "" {
		fmt.Fprintf(os.Stderr, "Error: -rules parameter is required\n\n")
		flag.Usage()
		os.Exit(1)
	}

	if !fileExists(config.RulesFile) {
		fmt.Fprintf(os.Stderr, "Error: Rules file does not exist: %s\n", config.RulesFile)
		os.Exit(1)
	}

	// 创建重命名器
	renamer, err := NewSimpleRenamer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: Failed to create renamer: %v\n", err)
		os.Exit(1)
	}

	// 执行重命名
	if err := renamer.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: Rename failed: %v\n", err)
		os.Exit(1)
	}
}

// 检查文件是否存在
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}
