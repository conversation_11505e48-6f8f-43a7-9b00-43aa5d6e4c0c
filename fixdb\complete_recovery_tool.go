// Complete Recovery Tool for WFS System
// 完整的WFS系统数据恢复工具
// 重建所有缺失的索引和指纹

package main

import (
	"bytes"
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE   = []byte{0x00, 0x00}
	PATH_SEQ   = []byte{0x01, 0x00}
	INDEX_0800 = []byte{0x08, 0x00}
)

// 文件信息
type FileInfo struct {
	SeqID        int64
	FileName     string
	OriginalPath string
	ContentID    []byte
	ContentData  []byte
	Timestamp    int64
}

// 恢复工具
type RecoveryTool struct {
	db     *leveldb.DB
	logger *log.Logger
	dryRun bool
	files  map[int64]*FileInfo
}

// 创建恢复工具
func NewRecoveryTool(dbPath string, dryRun bool) (*RecoveryTool, error) {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     64 * 1024 * 1024,
		WriteBuffer:            16 * 1024 * 1024,
		ReadOnly:               dryRun,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}

	return &RecoveryTool{
		db:     db,
		logger: log.New(os.Stdout, "[RecoveryTool] ", log.LstdFlags),
		dryRun: dryRun,
		files:  make(map[int64]*FileInfo),
	}, nil
}

// 关闭工具
func (rt *RecoveryTool) Close() error {
	if rt.db != nil {
		return rt.db.Close()
	}
	return nil
}

// 计算MD5指纹（WFS使用的指纹算法）
func (rt *RecoveryTool) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

// 编码protobuf格式的WfsPathBean
func (rt *RecoveryTool) encodeWfsPathBean(path string, timestamp int64) []byte {
	var buf []byte

	// 编码Path字段 (field 1, wire type 2)
	pathBytes := []byte(path)
	tag1 := (1 << 3) | 2
	buf = append(buf, byte(tag1))

	lengthBuf := make([]byte, binary.MaxVarintLen64)
	n := binary.PutUvarint(lengthBuf, uint64(len(pathBytes)))
	buf = append(buf, lengthBuf[:n]...)
	buf = append(buf, pathBytes...)

	// 编码Timestramp字段 (field 2, wire type 0)
	tag2 := (2 << 3) | 0
	buf = append(buf, byte(tag2))

	timestampBuf := make([]byte, binary.MaxVarintLen64)
	n = binary.PutVarint(timestampBuf, timestamp)
	buf = append(buf, timestampBuf[:n]...)

	return buf
}

// 分析当前数据库状态
func (rt *RecoveryTool) AnalyzeDatabase() error {
	rt.logger.Println("=== Analyzing Database State ===")

	// 分析PATH_PRE索引
	rt.analyzePATH_PRE()

	// 分析指纹索引和文件内容
	rt.analyzeFingerprints()

	// 尝试恢复原始路径信息
	rt.recoverOriginalPaths()

	// 生成分析报告
	rt.generateReport()

	return nil
}

// 分析PATH_PRE索引
func (rt *RecoveryTool) analyzePATH_PRE() {
	rt.logger.Println("\n--- Analyzing PATH_PRE Index ---")

	iter := rt.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	count := 0
	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_PRE) {
			continue
		}

		fileName := string(key[len(PATH_PRE):])

		// 解析seqID
		var seqID int64
		if len(value) >= 8 {
			seqID = int64(binary.BigEndian.Uint64(value))
		} else {
			paddedValue := make([]byte, 8)
			copy(paddedValue[8-len(value):], value)
			seqID = int64(binary.BigEndian.Uint64(paddedValue))
		}

		rt.files[seqID] = &FileInfo{
			SeqID:     seqID,
			FileName:  fileName,
			Timestamp: time.Now().Unix(),
		}

		count++
		rt.logger.Printf("PATH_PRE[%d]: %s -> seqID: %d", count, fileName, seqID)
	}

	rt.logger.Printf("Found %d PATH_PRE entries", count)
}

// 分析指纹索引
func (rt *RecoveryTool) analyzeFingerprints() {
	rt.logger.Println("\n--- Analyzing Fingerprint Index ---")

	// 扫描所有可能的指纹条目
	iter := rt.db.NewIterator(nil, nil)
	defer iter.Release()

	fingerprintCount := 0
	contentCount := 0

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知前缀
		if len(key) >= 2 {
			prefix := key[:2]
			if bytes.Equal(prefix, PATH_PRE) || bytes.Equal(prefix, PATH_SEQ) || bytes.Equal(prefix, INDEX_0800) {
				continue
			}
		}

		// 检查是否是MD5指纹（16字节）
		if len(key) == 16 {
			fingerprintCount++
			rt.logger.Printf("Fingerprint[%d]: %x -> %x", fingerprintCount, key, value)

			// 检查value是否指向文件内容
			if contentData, err := rt.db.Get(value, nil); err == nil {
				contentCount++
				rt.logger.Printf("  -> Content found: %d bytes", len(contentData))

				// 尝试关联到文件
				rt.tryAssociateContent(key, value, contentData)
			}
		}
	}

	rt.logger.Printf("Found %d fingerprints, %d with content", fingerprintCount, contentCount)
}

// 尝试关联内容到文件
func (rt *RecoveryTool) tryAssociateContent(fingerprint, contentID, contentData []byte) {
	// 尝试通过指纹反推原始路径
	for _, file := range rt.files {
		// 尝试当前文件名
		if bytes.Equal(rt.calculateFingerprint(file.FileName), fingerprint) {
			file.ContentID = contentID
			file.ContentData = contentData
			rt.logger.Printf("  Associated with current file: %s", file.FileName)
			return
		}

		// 尝试可能的原始路径
		possiblePaths := rt.generatePossiblePaths(file.FileName)
		for _, path := range possiblePaths {
			if bytes.Equal(rt.calculateFingerprint(path), fingerprint) {
				file.OriginalPath = path
				file.ContentID = contentID
				file.ContentData = contentData
				rt.logger.Printf("  Associated with original path: %s", path)
				return
			}
		}
	}
}

// 生成可能的原始路径
func (rt *RecoveryTool) generatePossiblePaths(fileName string) []string {
	var paths []string

	// 基于文件名生成可能的路径
	baseName := strings.TrimSuffix(fileName, filepath.Ext(fileName))
	ext := filepath.Ext(fileName)

	// 常见的路径模式
	patterns := []string{
		"Pictures\\khms3google\\%s",
		"Users\\weigu\\Pictures\\khms3google\\%s",
		"C:\\Users\\<USER>\\Pictures\\khms3google\\%s",
		"Users/weigu/Pictures/khms3google/%s",
		"aa/%s",
		"%s",
	}

	for _, pattern := range patterns {
		path := fmt.Sprintf(pattern, fileName)
		paths = append(paths, path)

		// 也尝试不同的文件名变体
		if strings.Contains(baseName, "_") {
			parts := strings.Split(baseName, "_")
			if len(parts) >= 2 {
				altName := parts[0] + ext
				altPath := fmt.Sprintf(pattern, altName)
				paths = append(paths, altPath)
			}
		}
	}

	return paths
}

// 恢复原始路径信息
func (rt *RecoveryTool) recoverOriginalPaths() {
	rt.logger.Println("\n--- Recovering Original Paths ---")

	for seqID, file := range rt.files {
		if file.OriginalPath == "" {
			rt.logger.Printf("SeqID %d (%s): No original path found", seqID, file.FileName)
		} else {
			rt.logger.Printf("SeqID %d (%s): Original path: %s", seqID, file.FileName, file.OriginalPath)
		}
	}
}

// 生成分析报告
func (rt *RecoveryTool) generateReport() {
	rt.logger.Println("\n=== Recovery Analysis Report ===")

	totalFiles := len(rt.files)
	filesWithContent := 0
	filesWithOriginalPath := 0

	for _, file := range rt.files {
		if file.ContentData != nil {
			filesWithContent++
		}
		if file.OriginalPath != "" {
			filesWithOriginalPath++
		}
	}

	rt.logger.Printf("Total files: %d", totalFiles)
	rt.logger.Printf("Files with content: %d", filesWithContent)
	rt.logger.Printf("Files with original path: %d", filesWithOriginalPath)
	rt.logger.Printf("Files needing recovery: %d", totalFiles-filesWithContent)

	if filesWithContent < totalFiles {
		rt.logger.Println("\n⚠️  Some files are missing content associations!")
		rt.logger.Println("💡 This explains why they don't appear in the web interface")
	}
}

// 执行恢复
func (rt *RecoveryTool) Recover() error {
	rt.logger.Println("=== Starting Recovery Process ===")

	if rt.dryRun {
		rt.logger.Println("🔍 DRY RUN MODE - No actual changes will be made")
	}

	batch := new(leveldb.Batch)
	recoveredCount := 0

	for seqID, file := range rt.files {
		if file.ContentData == nil {
			rt.logger.Printf("⚠️  Cannot recover SeqID %d (%s): No content data found", seqID, file.FileName)
			continue
		}

		// 重建PATH_SEQ索引
		seqIDBytes := make([]byte, 8)
		binary.BigEndian.PutUint64(seqIDBytes, uint64(seqID))
		pathSeqKey := append(PATH_SEQ, seqIDBytes...)
		pathBeanData := rt.encodeWfsPathBean(file.FileName, file.Timestamp)

		if rt.dryRun {
			rt.logger.Printf("🔍 Would rebuild PATH_SEQ for %s (seqID: %d)", file.FileName, seqID)
		} else {
			batch.Put(pathSeqKey, pathBeanData)
			rt.logger.Printf("🔧 Rebuilding PATH_SEQ for %s (seqID: %d)", file.FileName, seqID)
		}

		// 重建指纹索引（使用当前文件名）
		fingerprint := rt.calculateFingerprint(file.FileName)
		if rt.dryRun {
			rt.logger.Printf("🔍 Would rebuild fingerprint for %s: %x -> %x", file.FileName, fingerprint, file.ContentID)
		} else {
			batch.Put(fingerprint, file.ContentID)
			rt.logger.Printf("🔧 Rebuilding fingerprint for %s: %x -> %x", file.FileName, fingerprint, file.ContentID)
		}

		recoveredCount++
	}

	// 执行批处理
	if !rt.dryRun && recoveredCount > 0 {
		if err := rt.db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
			return fmt.Errorf("failed to write recovery batch: %v", err)
		}
	}

	rt.logger.Printf("Recovery completed: %d files processed", recoveredCount)

	if !rt.dryRun && recoveredCount > 0 {
		rt.logger.Println("✅ Recovery successful!")
		rt.logger.Println("💡 Restart WFS service and check web interface")
	} else if rt.dryRun {
		rt.logger.Println("🔍 Dry run completed - use -recover to apply changes")
	}

	return nil
}

// 主函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Complete Recovery Tool for WFS System")
		fmt.Println("Usage: complete_recovery_tool <db_path> [options]")
		fmt.Println("")
		fmt.Println("Options:")
		fmt.Println("  -analyze    : Analyze database and show recovery plan")
		fmt.Println("  -dry-run    : Preview recovery without making changes")
		fmt.Println("  -recover    : Perform actual recovery")
		fmt.Println("")
		fmt.Println("Examples:")
		fmt.Println("  complete_recovery_tool db_path -analyze")
		fmt.Println("  complete_recovery_tool db_path -dry-run")
		fmt.Println("  complete_recovery_tool db_path -recover")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	analyzeOnly := false
	dryRun := false
	performRecover := false

	// 解析参数
	for i := 2; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-analyze":
			analyzeOnly = true
		case "-dry-run":
			dryRun = true
		case "-recover":
			performRecover = true
		}
	}

	// 默认为分析模式
	if !analyzeOnly && !dryRun && !performRecover {
		analyzeOnly = true
	}

	// 创建恢复工具
	tool, err := NewRecoveryTool(dbPath, dryRun || analyzeOnly)
	if err != nil {
		log.Fatalf("Failed to create recovery tool: %v", err)
	}
	defer tool.Close()

	// 执行操作
	if analyzeOnly {
		if err := tool.AnalyzeDatabase(); err != nil {
			log.Fatalf("Analysis failed: %v", err)
		}
	} else {
		// 先分析再恢复
		if err := tool.AnalyzeDatabase(); err != nil {
			log.Fatalf("Analysis failed: %v", err)
		}
		if err := tool.Recover(); err != nil {
			log.Fatalf("Recovery failed: %v", err)
		}
	}
}
