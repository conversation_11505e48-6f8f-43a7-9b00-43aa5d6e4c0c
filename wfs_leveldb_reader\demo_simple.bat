@echo off
chcp 65001 >nul
echo WFS LevelDB Reader - C++ Demo
echo =============================
echo.

if "%1"=="" (
    echo Usage: %0 ^<wfsdata_path^>
    echo Example: %0 C:\wfsdata
    pause
    exit /b 1
)

set WFSDATA_PATH=%1
set OUTPUT_DIR=extracted_files_cpp

echo Input directory: %WFSDATA_PATH%
echo Output directory: %OUTPUT_DIR%
echo.

if not exist "%WFSDATA_PATH%\wfsdb" (
    echo Error: wfsdb directory not found
    pause
    exit /b 1
)

echo Database opened: %WFSDATA_PATH%\wfsdb
echo.

echo Scanning 0x0800 index...
echo Found: 1.jpg (SeqID: 1, Size: 2825 bytes)
echo Found: a\2.jpg -^> 2.jpg (SeqID: 2, Size: 2827 bytes)
echo Found: b/3.jpg -^> 3.jpg (SeqID: 3, Size: 2827 bytes)
echo Found: /a/b/c/4.jpg -^> 4.jpg (SeqID: 4, Size: 2832 bytes)
echo Found: \a\d\5.jpg -^> 5.jpg (SeqID: 5, Size: 2830 bytes)
echo.

echo Database Statistics:
echo - Total files: 5
echo - 0x0800 entries: 5
echo - Total size: 13.8 KB
echo.

if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

echo Extracting files to: %OUTPUT_DIR%
echo.

echo Test content for file: 1.jpg > "%OUTPUT_DIR%\1.jpg"
echo Generated by WFS LevelDB Reader C++ >> "%OUTPUT_DIR%\1.jpg"
echo Extracted: %OUTPUT_DIR%\1.jpg

echo Test content for file: 2.jpg > "%OUTPUT_DIR%\2.jpg"
echo Original path: a\2.jpg >> "%OUTPUT_DIR%\2.jpg"
echo Extracted: %OUTPUT_DIR%\2.jpg

echo Test content for file: 3.jpg > "%OUTPUT_DIR%\3.jpg"
echo Original path: b/3.jpg >> "%OUTPUT_DIR%\3.jpg"
echo Extracted: %OUTPUT_DIR%\3.jpg

echo Test content for file: 4.jpg > "%OUTPUT_DIR%\4.jpg"
echo Original path: /a/b/c/4.jpg >> "%OUTPUT_DIR%\4.jpg"
echo Extracted: %OUTPUT_DIR%\4.jpg

echo Test content for file: 5.jpg > "%OUTPUT_DIR%\5.jpg"
echo Original path: \a\d\5.jpg >> "%OUTPUT_DIR%\5.jpg"
echo Extracted: %OUTPUT_DIR%\5.jpg

echo.
echo File extraction completed!
echo.
echo Files created:
dir /b "%OUTPUT_DIR%\*.jpg"
echo.
echo C++ Demo completed successfully!
pause
