// LevelDB读取器接口
// 负责读取和解析LevelDB数据

#pragma once

#include "datatype.hpp"
#include <leveldb/db.h>
#include <leveldb/options.h>
#include <leveldb/cache.h>
#include <memory>
#include <map>
#include <functional>

namespace wfs
{

    // LevelDB读取器接口
    class LevelDBReader_i
    {
    public:
        virtual ~LevelDBReader_i() = default;

        // 打开数据库
        virtual ErrorCode open_database(const std::string &db_path) = 0;

        // 关闭数据库
        virtual void close_database() = 0;

        // 扫描所有数据条目
        virtual ErrorCode scan_all_entries() = 0;

        // 获取统计信息
        virtual const DatabaseStats &get_stats() const = 0;

        // 获取文件记录
        virtual const std::map<int64_t, FileRecord> &get_file_records() const = 0;

        // 设置进度回调
        virtual void set_progress_callback(std::function<void(size_t, size_t)> callback) = 0;
    };

    // LevelDB读取器实现
    class LevelDBReader : public LevelDBReader_i
    {
    private:
        std::unique_ptr<leveldb::DB> db_;
        leveldb::Options options_;
        DatabaseStats stats_;
        std::map<int64_t, FileRecord> file_records_;
        std::function<void(size_t, size_t)> progress_callback_;

        // 内部方法
        ErrorCode scan_path_pre_index();
        ErrorCode scan_path_seq_index();
        ErrorCode scan_0800_index();
        ErrorCode scan_content_data();

        // 解析方法
        int64_t parse_seq_id_from_value(const std::string &value) const;
        int64_t parse_seq_id_from_key(const std::string &key, size_t prefix_len) const;
        uint16_t get_key_prefix(const std::string &key) const;
        std::string extract_file_name(const std::string &path) const;

        // 内容查找方法
        std::vector<uint8_t> find_file_content(const std::string &original_path);
        std::vector<uint8_t> find_content_by_fingerprint(const std::string &path);
        std::vector<uint8_t> scan_for_content(const std::string &file_name);
        std::vector<uint8_t> create_test_content(const std::string &file_name);

        // 工具方法
        std::vector<uint8_t> calculate_md5_fingerprint(const std::string &data) const;
        bool looks_like_file_content(const std::vector<uint8_t> &data, const std::string &file_name) const;

    public:
        LevelDBReader();
        ~LevelDBReader() override;

        // 实现接口方法
        ErrorCode open_database(const std::string &db_path) override;
        void close_database() override;
        ErrorCode scan_all_entries() override;
        const DatabaseStats &get_stats() const override;
        const std::map<int64_t, FileRecord> &get_file_records() const override;
        void set_progress_callback(std::function<void(size_t, size_t)> callback) override;
    };

    // 工厂函数
    std::unique_ptr<LevelDBReader_i> create_leveldb_reader();

} // namespace wfs
