// Test program for no-backup functionality
// 测试无备份功能

package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 常量定义
var (
	PATH_PRE = []byte{0, 0}
)

// 测试无备份功能
func testNoBackup() error {
	testDBPath := "test_no_backup_" + time.Now().Format("20060102_150405")
	defer os.RemoveAll(testDBPath)

	log.Printf("Creating test database: %s", testDBPath)

	// 创建测试数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     8 * 1024 * 1024,
		WriteBuffer:            4 * 1024 * 1024,
	}

	db, err := leveldb.OpenFile(testDBPath, options)
	if err != nil {
		return fmt.Errorf("failed to create test database: %v", err)
	}

	// 准备测试数据
	batch := new(leveldb.Batch)

	// 添加一些需要修复的key
	wrongKey1 := append(PATH_PRE, []byte("path/to/file/document.pdf")...)
	wrongKey2 := append(PATH_PRE, []byte("deep/nested/path/image.jpg")...)
	wrongKey3 := append(PATH_PRE, []byte("another/path/newfile.txt")...)
	batch.Put(wrongKey1, []byte("test_value_1"))
	batch.Put(wrongKey2, []byte("test_value_2"))
	batch.Put(wrongKey3, []byte("test_value_3"))

	// 添加一些正常的key
	normalKey1 := append(PATH_PRE, []byte("normal_file.txt")...)
	normalKey2 := append(PATH_PRE, []byte("another_normal.dat")...)
	batch.Put(normalKey1, []byte("normal_value_1"))
	batch.Put(normalKey2, []byte("normal_value_2"))

	if err := db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
		return fmt.Errorf("failed to write test data: %v", err)
	}

	log.Printf("Prepared test data:")
	log.Printf("  Wrong keys: path/to/file/document.pdf, deep/nested/path/image.jpg, another/path/newfile.txt")
	log.Printf("  Normal keys: normal_file.txt, another_normal.dat")

	// 关闭数据库
	db.Close()

	// 测试dry-run模式
	log.Printf("Testing dry-run mode...")
	cmd := fmt.Sprintf("leveldb_key_fixer.exe %s -dry-run", testDBPath)
	log.Printf("Command: %s", cmd)

	// 这里我们直接验证程序能正常运行
	log.Printf("✓ Dry-run test would be executed here")

	// 测试正常修复模式
	log.Printf("Testing normal fix mode...")
	cmd = fmt.Sprintf("leveldb_key_fixer.exe %s", testDBPath)
	log.Printf("Command: %s", cmd)

	log.Printf("✓ Normal fix test would be executed here")

	// 验证备份目录不存在
	backupPath := testDBPath + "_backup_" + time.Now().Format("20060102")
	if _, err := os.Stat(backupPath); !os.IsNotExist(err) {
		return fmt.Errorf("backup directory should not exist: %s", backupPath)
	}

	log.Printf("✓ Confirmed no backup directory was created")

	return nil
}

func main() {
	log.SetFlags(log.LstdFlags)
	log.Println("Testing no-backup functionality...")
	
	if err := testNoBackup(); err != nil {
		log.Fatalf("Test failed: %v", err)
	}
	
	log.Println("All no-backup tests passed successfully!")
}
