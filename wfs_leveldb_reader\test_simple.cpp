// 简化的WFS LevelDB读取测试程序
// 用于快速验证功能

#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <filesystem>
#include <fstream>
#include <leveldb/db.h>
#include <leveldb/options.h>
#include <leveldb/iterator.h>

namespace fs = std::filesystem;

// 简化的文件记录
struct SimpleFileRecord {
    int64_t seq_id = 0;
    std::string original_path;
    std::string file_name;
    std::vector<uint8_t> content_data;
    size_t content_size = 0;
};

// 提取文件名
std::string extract_file_name(const std::string& path) {
    fs::path fs_path(path);
    return fs_path.filename().string();
}

// 从value解析seqID
int64_t parse_seq_id_from_value(const std::string& value) {
    if (value.length() >= 8) {
        int64_t seq_id = 0;
        for (size_t i = 0; i < 8; ++i) {
            seq_id = (seq_id << 8) | static_cast<uint8_t>(value[i]);
        }
        return seq_id;
    } else {
        int64_t seq_id = 0;
        for (size_t i = 0; i < value.length(); ++i) {
            seq_id = (seq_id << 8) | static_cast<uint8_t>(value[i]);
        }
        return seq_id;
    }
}

// 获取key前缀
uint16_t get_key_prefix(const std::string& key) {
    if (key.length() >= 2) {
        return (static_cast<uint8_t>(key[0]) << 8) | static_cast<uint8_t>(key[1]);
    }
    return 0;
}

// 创建测试内容
std::vector<uint8_t> create_test_content(const std::string& file_name) {
    std::string content = "Test content for file: " + file_name + "\n";
    content += "Generated by WFS LevelDB Reader\n";
    content += "This is a test file for demonstration.\n\n";
    
    for (int i = 0; i < 20; ++i) {
        content += "Line " + std::to_string(i + 1) + ": Test data\n";
    }
    
    return std::vector<uint8_t>(content.begin(), content.end());
}

// 写入文件
bool write_file(const std::string& file_path, const std::vector<uint8_t>& content) {
    std::ofstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }
    
    file.write(reinterpret_cast<const char*>(content.data()), content.size());
    return file.good();
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cout << "Usage: " << argv[0] << " <wfsdata_path>" << std::endl;
        return 1;
    }
    
    std::string wfsdata_path = argv[1];
    std::string db_path = wfsdata_path + "/wfsdb";
    
    // 检查路径
    if (!fs::exists(db_path)) {
        std::cerr << "Error: Database path does not exist: " << db_path << std::endl;
        return 1;
    }
    
    std::cout << "Opening database: " << db_path << std::endl;
    
    // 打开LevelDB
    leveldb::DB* db = nullptr;
    leveldb::Options options;
    options.create_if_missing = false;
    
    leveldb::Status status = leveldb::DB::Open(options, db_path, &db);
    if (!status.ok()) {
        std::cerr << "Error opening database: " << status.ToString() << std::endl;
        return 1;
    }
    
    std::cout << "Database opened successfully" << std::endl;
    
    // 扫描PATH_PRE索引 (0x0000)
    std::map<int64_t, SimpleFileRecord> file_records;
    
    std::string path_pre_prefix;
    path_pre_prefix.resize(2);
    path_pre_prefix[0] = 0x00;
    path_pre_prefix[1] = 0x00;
    
    std::cout << "\nScanning PATH_PRE index..." << std::endl;
    
    leveldb::Iterator* iter = db->NewIterator(leveldb::ReadOptions());
    int count = 0;
    
    for (iter->Seek(path_pre_prefix); iter->Valid(); iter->Next()) {
        std::string key = iter->key().ToString();
        std::string value = iter->value().ToString();
        
        if (!key.starts_with(path_pre_prefix)) {
            break;
        }
        
        if (key.length() <= path_pre_prefix.length()) {
            continue;
        }
        
        std::string original_path = key.substr(path_pre_prefix.length());
        int64_t seq_id = parse_seq_id_from_value(value);
        std::string file_name = extract_file_name(original_path);
        
        SimpleFileRecord record;
        record.seq_id = seq_id;
        record.original_path = original_path;
        record.file_name = file_name;
        record.content_data = create_test_content(file_name);
        record.content_size = record.content_data.size();
        
        file_records[seq_id] = record;
        count++;
        
        std::cout << "Found file: " << original_path << " -> " << file_name 
                  << " (SeqID: " << seq_id << ")" << std::endl;
    }
    
    delete iter;
    
    std::cout << "\nFound " << count << " files in PATH_PRE index" << std::endl;
    
    // 如果PATH_PRE为空，尝试0x0800索引
    if (count == 0) {
        std::cout << "\nTrying 0x0800 index..." << std::endl;
        
        std::string index_0800_prefix;
        index_0800_prefix.resize(2);
        index_0800_prefix[0] = 0x08;
        index_0800_prefix[1] = 0x00;
        
        iter = db->NewIterator(leveldb::ReadOptions());
        
        for (iter->Seek(index_0800_prefix); iter->Valid(); iter->Next()) {
            std::string key = iter->key().ToString();
            std::string value = iter->value().ToString();
            
            if (!key.starts_with(index_0800_prefix)) {
                break;
            }
            
            if (key.length() < index_0800_prefix.length() + 8) {
                continue;
            }
            
            // 从key的最后8字节提取seqID
            int64_t seq_id = 0;
            size_t offset = key.length() - 8;
            for (size_t i = 0; i < 8; ++i) {
                seq_id = (seq_id << 8) | static_cast<uint8_t>(key[offset + i]);
            }
            
            // 简单解析protobuf数据（假设第一个字符串字段是路径）
            std::string original_path = "unknown_path_" + std::to_string(seq_id);
            if (value.length() > 10) {
                // 尝试提取路径信息
                for (size_t i = 0; i < value.length() - 1; ++i) {
                    if (value[i] == 0x0A) { // protobuf string field tag
                        size_t len = static_cast<uint8_t>(value[i + 1]);
                        if (i + 2 + len <= value.length()) {
                            original_path = value.substr(i + 2, len);
                            break;
                        }
                    }
                }
            }
            
            std::string file_name = extract_file_name(original_path);
            
            SimpleFileRecord record;
            record.seq_id = seq_id;
            record.original_path = original_path;
            record.file_name = file_name;
            record.content_data = create_test_content(file_name);
            record.content_size = record.content_data.size();
            
            file_records[seq_id] = record;
            count++;
            
            std::cout << "Found file: " << original_path << " -> " << file_name 
                      << " (SeqID: " << seq_id << ")" << std::endl;
        }
        
        delete iter;
        std::cout << "\nFound " << count << " files in 0x0800 index" << std::endl;
    }
    
    // 提取文件到磁盘
    if (!file_records.empty()) {
        std::string output_dir = "extracted_files";
        fs::create_directories(output_dir);
        
        std::cout << "\nExtracting files to: " << output_dir << std::endl;
        
        for (const auto& [seq_id, record] : file_records) {
            std::string output_path = output_dir + "/" + record.file_name;
            
            // 处理文件名冲突
            int counter = 1;
            while (fs::exists(output_path)) {
                fs::path path(record.file_name);
                std::string stem = path.stem().string();
                std::string ext = path.extension().string();
                output_path = output_dir + "/" + stem + "_" + std::to_string(counter) + ext;
                counter++;
            }
            
            if (write_file(output_path, record.content_data)) {
                std::cout << "Extracted: " << output_path << " (" << record.content_size << " bytes)" << std::endl;
            } else {
                std::cerr << "Failed to write: " << output_path << std::endl;
            }
        }
        
        std::cout << "\nExtraction completed!" << std::endl;
        std::cout << "Total files: " << file_records.size() << std::endl;
        
        size_t total_size = 0;
        for (const auto& [seq_id, record] : file_records) {
            total_size += record.content_size;
        }
        std::cout << "Total size: " << total_size << " bytes" << std::endl;
    } else {
        std::cout << "\nNo files found in database" << std::endl;
    }
    
    delete db;
    return 0;
}
