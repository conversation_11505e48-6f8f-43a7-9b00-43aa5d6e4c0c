@echo off
REM WFS LevelDB Reader Build Script
REM 构建WFS LevelDB读取器

echo ========================================
echo WFS LevelDB Reader Builder
echo ========================================

REM 检查CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: CMake is not installed or not in PATH
    echo Please install CMake 3.20 or later
    pause
    exit /b 1
)

echo CMake found
cmake --version

REM 检查vcpkg
if not exist "C:\dev\vcpkg\vcpkg.exe" (
    echo Error: vcpkg not found at C:\dev\vcpkg\
    echo Please install vcpkg or update the path in CMakeLists.txt
    pause
    exit /b 1
)

echo vcpkg found

REM 创建构建目录
if not exist "C:\VisualStudioRollback\CMakeBuild_Temp" (
    mkdir "C:\VisualStudioRollback\CMakeBuild_Temp"
)

set BUILD_DIR=C:\VisualStudioRollback\CMakeBuild_Temp\wfs_leveldb_reader
if exist "%BUILD_DIR%" (
    echo Cleaning previous build...
    rmdir /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

REM 创建发布目录
if not exist "redist_desk" (
    mkdir "redist_desk"
)

echo.
echo Configuring project...
cd "%BUILD_DIR%"
cmake "%~dp0" -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake -DCMAKE_BUILD_TYPE=Release

if errorlevel 1 (
    echo Error: CMake configuration failed
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Release

if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo Installing...
cmake --install . --config Release

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable location: %~dp0redist_desk\wfs_leveldb_reader.exe
echo.
echo Usage:
echo   wfs_leveldb_reader.exe ^<wfsdata_path^> [options]
echo.
echo Examples:
echo   wfs_leveldb_reader.exe C:\wfsdata
echo   wfs_leveldb_reader.exe C:\wfsdata -o extracted -v
echo.
pause
