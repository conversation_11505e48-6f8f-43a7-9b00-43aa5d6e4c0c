// WFS Thrift Rename完整修复工具 - 完全按照WFS modify函数实现
package main

import (
	"crypto/md5"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_THRIFT = []byte{0x00, 0x00}
	PATH_SEQ_THRIFT = []byte{0x01, 0x00}
)

type ThriftRenameConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type FileEntry struct {
	OriginalPath string
	CleanPath    string
	SeqID        []byte
	SeqIDInt     int64
	NeedsRename  bool
}

type WFSThriftRenameFixer struct {
	config *ThriftRenameConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewWFSThriftRenameFixer(config *ThriftRenameConfig) (*WFSThriftRenameFixer, error) {
	logger := log.New(os.Stdout, "[WFSThriftRenameFixer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &WFSThriftRenameFixer{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (wtrf *WFSThriftRenameFixer) Fix() error {
	wtrf.logger.Println("=== WFS Thrift Rename Complete Fix ===")

	// 1. 收集所有需要重命名的文件
	files, err := wtrf.collectFiles()
	if err != nil {
		return fmt.Errorf("failed to collect files: %v", err)
	}

	wtrf.logger.Printf("Found %d files to process", len(files))

	// 2. 对每个需要重命名的文件执行完整的WFS modify操作
	for _, file := range files {
		if file.NeedsRename {
			if err := wtrf.executeWFSModify(file); err != nil {
				wtrf.logger.Printf("Error processing %s: %v", file.OriginalPath, err)
			}
		} else {
			// 即使不需要重命名，也要确保所有索引存在
			if err := wtrf.ensureAllIndexes(file); err != nil {
				wtrf.logger.Printf("Error ensuring indexes for %s: %v", file.OriginalPath, err)
			}
		}
	}

	return nil
}

func (wtrf *WFSThriftRenameFixer) collectFiles() ([]FileEntry, error) {
	wtrf.logger.Println("Collecting files from PATH_PRE...")

	var files []FileEntry
	iter := wtrf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_THRIFT[0] && key[1] == PATH_PRE_THRIFT[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])
				seqID := make([]byte, len(iter.Value()))
				copy(seqID, iter.Value())

				cleanPath := wtrf.cleanFileName(originalPath)
				needsRename := originalPath != cleanPath

				file := FileEntry{
					OriginalPath: originalPath,
					CleanPath:    cleanPath,
					SeqID:        seqID,
					SeqIDInt:     wtrf.bytesToInt64(seqID),
					NeedsRename:  needsRename,
				}

				files = append(files, file)

				if wtrf.config.Verbose {
					wtrf.logger.Printf("File: %s -> %s, seqID=%d, needsRename=%v",
						originalPath, cleanPath, file.SeqIDInt, needsRename)
				}
			}
		}
	}

	return files, nil
}

func (wtrf *WFSThriftRenameFixer) executeWFSModify(file FileEntry) error {
	if wtrf.config.Verbose {
		wtrf.logger.Printf("Executing WFS modify: %s -> %s", file.OriginalPath, file.CleanPath)
	}

	if wtrf.config.DryRun {
		wtrf.logger.Printf("[DRY RUN] Would execute WFS modify: %s -> %s", file.OriginalPath, file.CleanPath)
		return nil
	}

	// 完全按照WFS modify函数实现
	// am := make(map[*[]byte][]byte, 0)  // 要添加的键值对
	// dm := make([][]byte, 0)            // 要删除的键

	batch := new(leveldb.Batch)

	// 1. 删除旧的指纹索引
	oldFingerprint := wtrf.calculateFingerprint(file.OriginalPath)
	batch.Delete(oldFingerprint)

	// 2. 计算新的指纹
	newFingerprint := wtrf.calculateFingerprint(file.CleanPath)

	// 3. 处理PATH_PRE和PATH_SEQ索引
	// 删除旧的PATH_PRE
	oldPathPre := append(PATH_PRE_THRIFT, []byte(file.OriginalPath)...)
	batch.Delete(oldPathPre)

	// 添加新的PATH_PRE
	newPathPre := append(PATH_PRE_THRIFT, []byte(file.CleanPath)...)
	batch.Put(newPathPre, file.SeqID)

	// 更新PATH_SEQ中的WfsPathBean
	pathSeqKey := append(PATH_SEQ_THRIFT, file.SeqID...)

	// 获取当前时间戳或使用现有时间戳
	timestamp := time.Now().UnixNano()
	if pathBeanData, err := wtrf.db.Get(pathSeqKey, nil); err == nil {
		if _, ts, parseErr := wtrf.parseWfsPathBean(pathBeanData); parseErr == nil && ts != 0 {
			timestamp = ts
		}
	}

	newPathBeanData := wtrf.encodeWfsPathBean(file.CleanPath, timestamp)
	batch.Put(pathSeqKey, newPathBeanData)

	// 4. 更新指纹索引映射
	// 获取旧指纹对应的文件内容ID
	var fileContentID []byte
	if oldContentID, err := wtrf.db.Get(oldFingerprint, nil); err == nil {
		fileContentID = oldContentID
	} else {
		// 如果旧指纹不存在，使用seqID作为内容ID
		fileContentID = file.SeqID
	}
	batch.Put(newFingerprint, fileContentID)

	// 5. 更新0x0800索引
	if err := wtrf.update0x0800InBatch(batch, file, timestamp); err != nil {
		wtrf.logger.Printf("Warning: Failed to update 0x0800 for %s: %v", file.OriginalPath, err)
	}

	// 6. 执行批量操作（关键：原子性）
	if err := wtrf.db.Write(batch, nil); err != nil {
		return fmt.Errorf("failed to write batch: %v", err)
	}

	wtrf.logger.Printf("✅ WFS modify completed: %s -> %s", file.OriginalPath, file.CleanPath)
	return nil
}

func (wtrf *WFSThriftRenameFixer) ensureAllIndexes(file FileEntry) error {
	if wtrf.config.Verbose {
		wtrf.logger.Printf("Ensuring all indexes for: %s", file.OriginalPath)
	}

	if wtrf.config.DryRun {
		wtrf.logger.Printf("[DRY RUN] Would ensure indexes for: %s", file.OriginalPath)
		return nil
	}

	batch := new(leveldb.Batch)
	needsUpdate := false

	// 确保PATH_SEQ存在
	pathSeqKey := append(PATH_SEQ_THRIFT, file.SeqID...)
	if _, err := wtrf.db.Get(pathSeqKey, nil); err == leveldb.ErrNotFound {
		timestamp := time.Now().UnixNano()
		pathBeanData := wtrf.encodeWfsPathBean(file.OriginalPath, timestamp)
		batch.Put(pathSeqKey, pathBeanData)
		needsUpdate = true

		if wtrf.config.Verbose {
			wtrf.logger.Printf("Creating missing PATH_SEQ for %s", file.OriginalPath)
		}
	}

	// 确保指纹索引存在
	fingerprint := wtrf.calculateFingerprint(file.OriginalPath)
	if _, err := wtrf.db.Get(fingerprint, nil); err == leveldb.ErrNotFound {
		batch.Put(fingerprint, file.SeqID)
		needsUpdate = true

		if wtrf.config.Verbose {
			wtrf.logger.Printf("Creating missing fingerprint for %s", file.OriginalPath)
		}
	}

	if needsUpdate {
		if err := wtrf.db.Write(batch, nil); err != nil {
			return fmt.Errorf("failed to write batch: %v", err)
		}
		wtrf.logger.Printf("✅ Ensured indexes for: %s", file.OriginalPath)
	}

	return nil
}

func (wtrf *WFSThriftRenameFixer) update0x0800InBatch(batch *leveldb.Batch, file FileEntry, timestamp int64) error {
	// 扫描0x0800索引，查找包含此seqID的条目
	prefix0x0800 := []byte{0x08, 0x00}
	iter := wtrf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0x0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		// 检查key是否包含此seqID
		if wtrf.keyContainsSeqID(key, file.SeqID) {
			value := iter.Value()

			// 解析WfsPathBean
			oldPath, oldTimestamp, err := wtrf.parseWfsPathBean(value)
			if err != nil {
				continue
			}

			// 如果路径匹配，更新为新路径
			if oldPath == file.OriginalPath {
				newPathBeanData := wtrf.encodeWfsPathBean(file.CleanPath, oldTimestamp)
				batch.Put(key, newPathBeanData)

				if wtrf.config.Verbose {
					wtrf.logger.Printf("Updated 0x0800: %s -> %s", oldPath, file.CleanPath)
				}
			}
		}
	}

	return nil
}

func (wtrf *WFSThriftRenameFixer) keyContainsSeqID(key, seqID []byte) bool {
	// 检查key是否以seqID结尾
	if len(key) >= len(seqID) {
		keyEnd := key[len(key)-len(seqID):]
		for i, b := range seqID {
			if keyEnd[i] != b {
				return false
			}
		}
		return true
	}
	return false
}

func (wtrf *WFSThriftRenameFixer) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	parts := strings.Split(path, "/")
	return parts[len(parts)-1]
}

// 辅助函数
func (wtrf *WFSThriftRenameFixer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wtrf *WFSThriftRenameFixer) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func (wtrf *WFSThriftRenameFixer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wtrf *WFSThriftRenameFixer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// Path字段 (field 1, wire type 2)
	if path != "" {
		result = append(result, 0x0A)
		pathBytes := []byte(path)
		result = append(result, byte(len(pathBytes)))
		result = append(result, pathBytes...)
	}

	// Timestamp字段 (field 2, wire type 0)
	if timestamp != 0 {
		result = append(result, 0x10)
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

func (wtrf *WFSThriftRenameFixer) Close() {
	if wtrf.db != nil {
		wtrf.db.Close()
	}
}

func main() {
	config := &ThriftRenameConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Thrift Rename Complete Fixer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	fixer, err := NewWFSThriftRenameFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
