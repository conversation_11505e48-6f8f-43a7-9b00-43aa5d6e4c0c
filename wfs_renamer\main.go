// WFS文件重命名工具
// 基于WFS Rename接口实现批量文件重命名功能

package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"wfs_renamer/stub"

	"github.com/apache/thrift/lib/go/thrift"
)

// 重命名配置
type RenameConfig struct {
	WfsHost     string // WFS服务地址
	WfsPort     int    // WFS服务端口
	Workers     int    // 并发工作线程数
	Connections int    // 连接池大小
	DryRun      bool   // 预览模式
	SkipErrors  bool   // 跳过错误继续处理
	RenameRules string // 重命名规则文件
	LogFile     string // 日志文件
	Verbose     bool   // 详细输出
}

// 重命名规则
type RenameRule struct {
	OldPath string // 原路径
	NewPath string // 新路径
}

// 重命名任务
type RenameTask struct {
	Rule   RenameRule
	TaskID int64
}

// 重命名结果
type RenameResult struct {
	Task    RenameTask
	Success bool
	Error   error
}

// 统计信息
type RenameStats struct {
	TotalTasks    int64
	SuccessCount  int64
	ErrorCount    int64
	SkippedCount  int64
	ProcessedSize int64
	StartTime     time.Time
}

// WFS客户端连接池
type WfsClientPool struct {
	clients chan *WfsClient
	config  *RenameConfig
	mu      sync.Mutex
}

// WFS客户端
type WfsClient struct {
	client    stub.WfsIface
	transport thrift.TTransport
}

// WFS重命名器
type WfsRenamer struct {
	config     *RenameConfig
	clientPool *WfsClientPool
	logger     *log.Logger
	stats      *RenameStats
	taskChan   chan RenameTask
	resultChan chan RenameResult
}

// 创建WFS客户端连接池
func NewWfsClientPool(config *RenameConfig) (*WfsClientPool, error) {
	pool := &WfsClientPool{
		clients: make(chan *WfsClient, config.Connections),
		config:  config,
	}

	// 预创建连接
	for i := 0; i < config.Connections; i++ {
		client, err := pool.createClient()
		if err != nil {
			return nil, fmt.Errorf("failed to create client %d: %v", i, err)
		}
		pool.clients <- client
	}

	return pool, nil
}

// 创建单个WFS客户端
func (pool *WfsClientPool) createClient() (*WfsClient, error) {
	addr := fmt.Sprintf("%s:%d", pool.config.WfsHost, pool.config.WfsPort)

	transportFactory := thrift.NewTBufferedTransportFactory(8192)
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()

	transport, err := thrift.NewTSocket(addr)
	if err != nil {
		return nil, fmt.Errorf("failed to create socket: %v", err)
	}

	transport = transportFactory.GetTransport(transport)
	if err := transport.Open(); err != nil {
		return nil, fmt.Errorf("failed to open transport: %v", err)
	}

	client := stub.NewWfsIfaceClientFactory(transport, protocolFactory)

	return &WfsClient{
		client:    client,
		transport: transport,
	}, nil
}

// 获取客户端
func (pool *WfsClientPool) GetClient() (*WfsClient, error) {
	select {
	case client := <-pool.clients:
		return client, nil
	case <-time.After(30 * time.Second):
		return nil, fmt.Errorf("timeout waiting for client")
	}
}

// 归还客户端
func (pool *WfsClientPool) ReturnClient(client *WfsClient) {
	select {
	case pool.clients <- client:
	default:
		// 连接池满了，关闭连接
		client.transport.Close()
	}
}

// 关闭连接池
func (pool *WfsClientPool) Close() {
	close(pool.clients)
	for client := range pool.clients {
		client.transport.Close()
	}
}

// 创建WFS重命名器
func NewWfsRenamer(config *RenameConfig) (*WfsRenamer, error) {
	// 创建日志器
	var logger *log.Logger
	if config.LogFile != "" {
		logFile, err := os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %v", err)
		}
		logger = log.New(logFile, "[WfsRenamer] ", log.LstdFlags)
	} else {
		logger = log.New(os.Stdout, "[WfsRenamer] ", log.LstdFlags)
	}

	// 创建连接池
	clientPool, err := NewWfsClientPool(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create client pool: %v", err)
	}

	return &WfsRenamer{
		config:     config,
		clientPool: clientPool,
		logger:     logger,
		stats: &RenameStats{
			StartTime: time.Now(),
		},
		taskChan:   make(chan RenameTask, config.Workers*2),
		resultChan: make(chan RenameResult, config.Workers*2),
	}, nil
}

// 加载重命名规则
func (wr *WfsRenamer) loadRenameRules() ([]RenameRule, error) {
	file, err := os.Open(wr.config.RenameRules)
	if err != nil {
		return nil, fmt.Errorf("failed to open rules file: %v", err)
	}
	defer file.Close()

	var rules []RenameRule
	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析规则：old_path -> new_path 或 old_path,new_path
		var oldPath, newPath string
		if strings.Contains(line, "->") {
			parts := strings.Split(line, "->")
			if len(parts) != 2 {
				wr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
				continue
			}
			oldPath = strings.TrimSpace(parts[0])
			newPath = strings.TrimSpace(parts[1])
		} else if strings.Contains(line, ",") {
			parts := strings.Split(line, ",")
			if len(parts) != 2 {
				wr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
				continue
			}
			oldPath = strings.TrimSpace(parts[0])
			newPath = strings.TrimSpace(parts[1])
		} else {
			wr.logger.Printf("Warning: Invalid rule format at line %d: %s", lineNum, line)
			continue
		}

		if oldPath == "" || newPath == "" {
			wr.logger.Printf("Warning: Empty path at line %d: %s", lineNum, line)
			continue
		}

		rules = append(rules, RenameRule{
			OldPath: oldPath,
			NewPath: newPath,
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading rules file: %v", err)
	}

	wr.logger.Printf("Loaded %d rename rules from %s", len(rules), wr.config.RenameRules)
	return rules, nil
}

// 工作线程
func (wr *WfsRenamer) worker(workerID int) {
	wr.logger.Printf("Worker %d started", workerID)
	defer wr.logger.Printf("Worker %d completed", workerID)

	for task := range wr.taskChan {
		result := wr.processRenameTask(workerID, task)
		wr.resultChan <- result
	}
}

// 处理重命名任务
func (wr *WfsRenamer) processRenameTask(workerID int, task RenameTask) RenameResult {
	if wr.config.Verbose {
		wr.logger.Printf("Worker %d: Processing %s -> %s",
			workerID, task.Rule.OldPath, task.Rule.NewPath)
	}

	// 预览模式
	if wr.config.DryRun {
		wr.logger.Printf("Worker %d: [DRY RUN] Would rename %s -> %s",
			workerID, task.Rule.OldPath, task.Rule.NewPath)
		return RenameResult{
			Task:    task,
			Success: true,
			Error:   nil,
		}
	}

	// 获取WFS客户端
	client, err := wr.clientPool.GetClient()
	if err != nil {
		return RenameResult{
			Task:    task,
			Success: false,
			Error:   fmt.Errorf("failed to get client: %v", err),
		}
	}
	defer wr.clientPool.ReturnClient(client)

	// 调用WFS Rename接口
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	ack, err := client.client.Rename(ctx, task.Rule.OldPath, task.Rule.NewPath)
	if err != nil {
		return RenameResult{
			Task:    task,
			Success: false,
			Error:   fmt.Errorf("rename failed: %v", err),
		}
	}

	if ack == nil || !ack.Ok {
		errorMsg := "unknown error"
		if ack != nil && ack.Error != nil && ack.Error.Info != nil {
			errorMsg = *ack.Error.Info
		}
		return RenameResult{
			Task:    task,
			Success: false,
			Error:   fmt.Errorf("rename rejected: %s", errorMsg),
		}
	}

	wr.logger.Printf("Worker %d: Successfully renamed %s -> %s",
		workerID, task.Rule.OldPath, task.Rule.NewPath)

	return RenameResult{
		Task:    task,
		Success: true,
		Error:   nil,
	}
}

// 结果处理器
func (wr *WfsRenamer) resultProcessor() {
	for result := range wr.resultChan {
		if result.Success {
			atomic.AddInt64(&wr.stats.SuccessCount, 1)
		} else {
			atomic.AddInt64(&wr.stats.ErrorCount, 1)
			wr.logger.Printf("Error: Failed to rename %s -> %s: %v",
				result.Task.Rule.OldPath, result.Task.Rule.NewPath, result.Error)
		}

		// 显示进度
		processed := atomic.LoadInt64(&wr.stats.SuccessCount) + atomic.LoadInt64(&wr.stats.ErrorCount)
		if processed%100 == 0 || wr.config.Verbose {
			wr.printProgress()
		}
	}
}

// 打印进度
func (wr *WfsRenamer) printProgress() {
	elapsed := time.Since(wr.stats.StartTime)
	total := atomic.LoadInt64(&wr.stats.TotalTasks)
	success := atomic.LoadInt64(&wr.stats.SuccessCount)
	errors := atomic.LoadInt64(&wr.stats.ErrorCount)
	processed := success + errors

	if total > 0 {
		percentage := float64(processed) / float64(total) * 100
		rate := float64(processed) / elapsed.Seconds()

		wr.logger.Printf("Progress: %d/%d (%.1f%%), %d success, %d errors, %.1f tasks/sec, elapsed: %v",
			processed, total, percentage, success, errors, rate, elapsed.Truncate(time.Second))
	}
}

// 执行重命名
func (wr *WfsRenamer) Execute() error {
	wr.logger.Println("=== Starting WFS File Rename ===")

	// 加载重命名规则
	rules, err := wr.loadRenameRules()
	if err != nil {
		return fmt.Errorf("failed to load rename rules: %v", err)
	}

	if len(rules) == 0 {
		wr.logger.Println("No rename rules found")
		return nil
	}

	wr.stats.TotalTasks = int64(len(rules))
	wr.logger.Printf("Total rename tasks: %d", len(rules))

	// 启动工作线程
	var wg sync.WaitGroup
	for i := 0; i < wr.config.Workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			wr.worker(workerID)
		}(i)
	}

	// 启动结果处理器
	go wr.resultProcessor()

	// 发送任务
	go func() {
		defer close(wr.taskChan)
		for i, rule := range rules {
			task := RenameTask{
				Rule:   rule,
				TaskID: int64(i + 1),
			}
			wr.taskChan <- task
		}
	}()

	// 等待所有工作线程完成
	wg.Wait()
	close(wr.resultChan)

	// 等待结果处理完成
	time.Sleep(100 * time.Millisecond)

	// 打印最终统计
	wr.printFinalStats()

	return nil
}

// 打印最终统计
func (wr *WfsRenamer) printFinalStats() {
	elapsed := time.Since(wr.stats.StartTime)
	total := atomic.LoadInt64(&wr.stats.TotalTasks)
	success := atomic.LoadInt64(&wr.stats.SuccessCount)
	errors := atomic.LoadInt64(&wr.stats.ErrorCount)

	wr.logger.Println("\n=== Rename Summary ===")
	wr.logger.Printf("Total tasks: %d", total)
	wr.logger.Printf("Successful: %d", success)
	wr.logger.Printf("Errors: %d", errors)
	wr.logger.Printf("Success rate: %.1f%%", float64(success)/float64(total)*100)
	wr.logger.Printf("Total time: %v", elapsed.Truncate(time.Second))
	wr.logger.Printf("Average rate: %.1f tasks/sec", float64(total)/elapsed.Seconds())

	if errors > 0 {
		wr.logger.Printf("⚠️  %d tasks failed. Check logs for details.", errors)
	} else {
		wr.logger.Println("✅ All rename tasks completed successfully!")
	}
}

// 关闭资源
func (wr *WfsRenamer) Close() {
	if wr.clientPool != nil {
		wr.clientPool.Close()
	}
}

// 主函数
func main() {
	config := &RenameConfig{}

	// 命令行参数
	flag.StringVar(&config.WfsHost, "host", "localhost", "WFS server host")
	flag.IntVar(&config.WfsPort, "port", 5122, "WFS server port")
	flag.IntVar(&config.Workers, "workers", 4, "Number of worker threads")
	flag.IntVar(&config.Connections, "connections", 10, "Connection pool size")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode, don't actually rename")
	flag.BoolVar(&config.SkipErrors, "skip-errors", true, "Skip errors and continue")
	flag.StringVar(&config.RenameRules, "rules", "", "Rename rules file (required)")
	flag.StringVar(&config.LogFile, "log", "", "Log file path (default: stdout)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS File Renamer - Batch rename files using WFS Rename API\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nRename Rules File Format:\n")
		fmt.Fprintf(os.Stderr, "  old_path -> new_path\n")
		fmt.Fprintf(os.Stderr, "  old_path,new_path\n")
		fmt.Fprintf(os.Stderr, "  # Comments start with #\n\n")
		fmt.Fprintf(os.Stderr, "Examples:\n")
		fmt.Fprintf(os.Stderr, "  %s -rules rename_rules.txt -host localhost -port 5122\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -rules rules.txt -dry-run -verbose\n", os.Args[0])
	}

	flag.Parse()

	// 验证参数
	if config.RenameRules == "" {
		fmt.Fprintf(os.Stderr, "Error: -rules parameter is required\n\n")
		flag.Usage()
		os.Exit(1)
	}

	if !fileExists(config.RenameRules) {
		fmt.Fprintf(os.Stderr, "Error: Rules file does not exist: %s\n", config.RenameRules)
		os.Exit(1)
	}

	// 创建重命名器
	renamer, err := NewWfsRenamer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: Failed to create renamer: %v\n", err)
		os.Exit(1)
	}
	defer renamer.Close()

	// 执行重命名
	if err := renamer.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: Rename failed: %v\n", err)
		os.Exit(1)
	}
}

// 检查文件是否存在
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}
