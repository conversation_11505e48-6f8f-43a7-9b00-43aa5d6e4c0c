// WFS数据库检查工具 - 诊断网页显示问题
// 检查所有索引的完整性和一致性

package main

import (
	"crypto/md5"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00}
	PATH_SEQ = []byte{0x01, 0x00}
)

// 检查器配置
type InspectorConfig struct {
	DatabasePath string
	Verbose      bool
	CheckAll     bool
}

// 文件信息
type FileInfo struct {
	Path         string
	SeqID        []byte
	HasPATH_PRE  bool
	HasPATH_SEQ  bool
	Has0x0800    bool
	HasFingerprint bool
	FingerprintKey []byte
}

// 数据库检查器
type DBInspector struct {
	config *InspectorConfig
	db     *leveldb.DB
	logger *log.Logger
}

// 创建检查器
func NewDBInspector(config *InspectorConfig) (*DBInspector, error) {
	logger := log.New(os.Stdout, "[DBInspector] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &DBInspector{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

// 计算指纹
func (di *DBInspector) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

// 解析WfsPathBean
func (di *DBInspector) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64
	
	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n
		
		fieldNum := tag >> 3
		wireType := tag & 0x7
		
		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}
	
	return path, timestamp, nil
}

// 检查数据库完整性
func (di *DBInspector) Inspect() error {
	di.logger.Println("=== WFS Database Inspection ===")

	// 收集所有文件信息
	files := make(map[string]*FileInfo)

	// 1. 扫描PATH_PRE索引
	di.logger.Println("Scanning PATH_PRE index...")
	iter := di.db.NewIterator(nil, nil)
	defer iter.Release()

	pathPreCount := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqID := iter.Value()
				
				if files[path] == nil {
					files[path] = &FileInfo{Path: path}
				}
				files[path].SeqID = seqID
				files[path].HasPATH_PRE = true
				pathPreCount++
				
				if di.config.Verbose {
					di.logger.Printf("PATH_PRE: %s -> seqID %x", path, seqID)
				}
			}
		}
	}
	di.logger.Printf("Found %d PATH_PRE entries", pathPreCount)

	// 2. 检查PATH_SEQ索引
	di.logger.Println("Checking PATH_SEQ index...")
	pathSeqCount := 0
	for path, info := range files {
		if info.SeqID != nil {
			seqID := binary.BigEndian.Uint64(info.SeqID)
			pathSeqKey := append(PATH_SEQ, info.SeqID...)
			
			if data, err := di.db.Get(pathSeqKey, nil); err == nil {
				info.HasPATH_SEQ = true
				pathSeqCount++
				
				if seqPath, timestamp, err := di.parseWfsPathBean(data); err == nil {
					if di.config.Verbose {
						di.logger.Printf("PATH_SEQ: seqID %d -> path=%s, timestamp=%d", seqID, seqPath, timestamp)
					}
					if seqPath != path {
						di.logger.Printf("⚠️  PATH_SEQ mismatch: expected %s, got %s", path, seqPath)
					}
				}
			}
		}
	}
	di.logger.Printf("Found %d PATH_SEQ entries", pathSeqCount)

	// 3. 检查0x0800索引
	di.logger.Println("Checking 0x0800 index...")
	prefix0800 := []byte{0x08, 0x00}
	iter = di.db.NewIterator(nil, nil)
	defer iter.Release()

	index0800Count := 0
	for iter.Seek(prefix0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}
		
		value := iter.Value()
		if path, timestamp, err := di.parseWfsPathBean(value); err == nil {
			index0800Count++
			
			if info, exists := files[path]; exists {
				info.Has0x0800 = true
			} else {
				di.logger.Printf("⚠️  0x0800 orphan: %s (no PATH_PRE)", path)
			}
			
			if di.config.Verbose {
				di.logger.Printf("0x0800: key %x -> path=%s, timestamp=%d", key, path, timestamp)
			}
		}
	}
	di.logger.Printf("Found %d 0x0800 entries", index0800Count)

	// 4. 检查指纹索引
	di.logger.Println("Checking fingerprint index...")
	fingerprintCount := 0
	for path, info := range files {
		fingerprint := di.calculateFingerprint(path)
		info.FingerprintKey = fingerprint
		
		if _, err := di.db.Get(fingerprint, nil); err == nil {
			info.HasFingerprint = true
			fingerprintCount++
			
			if di.config.Verbose {
				di.logger.Printf("Fingerprint: %s -> %x", path, fingerprint)
			}
		}
	}
	di.logger.Printf("Found %d fingerprint entries", fingerprintCount)

	// 5. 生成完整性报告
	di.generateReport(files)

	return nil
}

// 生成完整性报告
func (di *DBInspector) generateReport(files map[string]*FileInfo) {
	di.logger.Println("\n=== Database Integrity Report ===")

	completeFiles := 0
	incompleteFiles := 0

	for path, info := range files {
		isComplete := info.HasPATH_PRE && info.HasPATH_SEQ && info.Has0x0800 && info.HasFingerprint
		
		if isComplete {
			completeFiles++
			if di.config.Verbose {
				di.logger.Printf("✅ Complete: %s", path)
			}
		} else {
			incompleteFiles++
			status := ""
			if !info.HasPATH_PRE { status += " [No PATH_PRE]" }
			if !info.HasPATH_SEQ { status += " [No PATH_SEQ]" }
			if !info.Has0x0800 { status += " [No 0x0800]" }
			if !info.HasFingerprint { status += " [No Fingerprint]" }
			
			di.logger.Printf("❌ Incomplete: %s%s", path, status)
		}
	}

	di.logger.Printf("\nSummary:")
	di.logger.Printf("Total files: %d", len(files))
	di.logger.Printf("Complete files: %d", completeFiles)
	di.logger.Printf("Incomplete files: %d", incompleteFiles)
	
	if incompleteFiles > 0 {
		di.logger.Printf("\n⚠️  %d files have missing indexes, which may cause display issues in WFS web interface", incompleteFiles)
		di.logger.Printf("Missing fingerprint indexes are the most common cause of files not displaying properly")
	} else {
		di.logger.Printf("\n✅ All files have complete indexes")
	}
}

// 关闭
func (di *DBInspector) Close() {
	if di.db != nil {
		di.db.Close()
	}
}

// 主函数
func main() {
	config := &InspectorConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.BoolVar(&config.CheckAll, "all", false, "Check all indexes")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Database Inspector - Diagnose web display issues\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	inspector, err := NewDBInspector(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer inspector.Close()

	if err := inspector.Inspect(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
