@echo off
REM 简单测试程序构建脚本

echo Building simple WFS LevelDB reader test...

REM 设置vcpkg路径
set VCPKG_ROOT=C:\dev\vcpkg
set LEVELDB_INCLUDE=%VCPKG_ROOT%\installed\x64-windows\include
set LEVELDB_LIB=%VCPKG_ROOT%\installed\x64-windows\lib

REM 编译
g++ -std=c++20 ^
    -I"%LEVELDB_INCLUDE%" ^
    -L"%LEVELDB_LIB%" ^
    test_simple.cpp ^
    -lleveldb ^
    -o test_simple.exe

if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo Build successful!
echo.
echo Usage: test_simple.exe ^<wfsdata_path^>
echo Example: test_simple.exe C:\wfsdata
echo.
pause
