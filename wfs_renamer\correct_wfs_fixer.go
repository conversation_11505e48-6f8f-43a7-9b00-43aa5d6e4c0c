// 正确的WFS修复工具 - 从参考数据库复制正确的数据结构
package main

import (
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type CorrectWFSFixerConfig struct {
	DatabasePath  string
	ReferencePath string
	DryRun        bool
	Verbose       bool
}

type CorrectWFSFixer struct {
	config *CorrectWFSFixerConfig
	db     *leveldb.DB
	refDB  *leveldb.DB
	logger *log.Logger
}

func NewCorrectWFSFixer(config *CorrectWFSFixerConfig) (*CorrectWFSFixer, error) {
	logger := log.New(os.Stdout, "[CorrectWFSFixer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	refOptions := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	refDB, err := leveldb.OpenFile(config.ReferencePath, refOptions)
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to open reference database: %v", err)
	}

	return &CorrectWFSFixer{
		config: config,
		db:     db,
		refDB:  refDB,
		logger: logger,
	}, nil
}

func (cwf *CorrectWFSFixer) Fix() error {
	cwf.logger.Println("=== Correct WFS Fix - Copy from Reference Database ===")

	// 1. 收集参考数据库的所有条目
	refEntries := cwf.collectReferenceEntries()

	// 2. 收集当前数据库的所有条目
	currentEntries := cwf.collectCurrentEntries()

	// 3. 找出需要复制的条目
	missingEntries := cwf.findMissingEntries(refEntries, currentEntries)

	// 4. 找出需要删除的错误条目
	extraEntries := cwf.findExtraEntries(currentEntries, refEntries)

	// 5. 执行修复
	return cwf.performFix(missingEntries, extraEntries)
}

func (cwf *CorrectWFSFixer) collectReferenceEntries() map[string][]byte {
	cwf.logger.Println("Collecting reference database entries...")

	entries := make(map[string][]byte)
	iter := cwf.refDB.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		keyHex := hex.EncodeToString(key)
		valueBytes := make([]byte, len(value))
		copy(valueBytes, value)
		entries[keyHex] = valueBytes

		count++
		if cwf.config.Verbose && count <= 10 {
			cwf.logger.Printf("Reference entry: %s → %s", keyHex, hex.EncodeToString(value))
		}
	}

	cwf.logger.Printf("Found %d entries in reference database", count)
	return entries
}

func (cwf *CorrectWFSFixer) collectCurrentEntries() map[string][]byte {
	cwf.logger.Println("Collecting current database entries...")

	entries := make(map[string][]byte)
	iter := cwf.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		keyHex := hex.EncodeToString(key)
		valueBytes := make([]byte, len(value))
		copy(valueBytes, value)
		entries[keyHex] = valueBytes

		count++
	}

	cwf.logger.Printf("Found %d entries in current database", count)
	return entries
}

func (cwf *CorrectWFSFixer) findMissingEntries(refEntries, currentEntries map[string][]byte) map[string][]byte {
	cwf.logger.Println("Finding missing entries...")

	missingEntries := make(map[string][]byte)

	for keyHex, value := range refEntries {
		if _, exists := currentEntries[keyHex]; !exists {
			missingEntries[keyHex] = value
		}
	}

	cwf.logger.Printf("Found %d missing entries", len(missingEntries))

	if cwf.config.Verbose {
		count := 0
		for keyHex, value := range missingEntries {
			if count >= 10 {
				cwf.logger.Printf("... and %d more missing entries", len(missingEntries)-10)
				break
			}
			cwf.logger.Printf("Missing: %s → %s", keyHex, hex.EncodeToString(value))
			count++
		}
	}

	return missingEntries
}

func (cwf *CorrectWFSFixer) findExtraEntries(currentEntries, refEntries map[string][]byte) map[string][]byte {
	cwf.logger.Println("Finding extra entries...")

	extraEntries := make(map[string][]byte)

	for keyHex, value := range currentEntries {
		if _, exists := refEntries[keyHex]; !exists {
			// 只删除我们可能创建的错误条目，保留PATH_PRE和0x0800
			if cwf.shouldDeleteEntry(keyHex) {
				extraEntries[keyHex] = value
			}
		}
	}

	cwf.logger.Printf("Found %d extra entries to delete", len(extraEntries))

	if cwf.config.Verbose {
		count := 0
		for keyHex, value := range extraEntries {
			if count >= 10 {
				cwf.logger.Printf("... and %d more extra entries", len(extraEntries)-10)
				break
			}
			cwf.logger.Printf("Extra: %s → %s", keyHex, hex.EncodeToString(value))
			count++
		}
	}

	return extraEntries
}

func (cwf *CorrectWFSFixer) shouldDeleteEntry(keyHex string) bool {
	// 不删除PATH_PRE (0000) 和 0x0800 (0800) 条目
	if len(keyHex) >= 4 {
		prefix := keyHex[:4]
		if prefix == "0000" || prefix == "0800" {
			return false
		}
	}

	// 删除其他条目（PATH_SEQ、指纹索引、WfsFileBean等）
	return true
}

func (cwf *CorrectWFSFixer) performFix(missingEntries, extraEntries map[string][]byte) error {
	cwf.logger.Println("Performing fix...")

	batch := new(leveldb.Batch)
	totalOperations := 0

	// 删除多余的条目
	for keyHex := range extraEntries {
		key, err := hex.DecodeString(keyHex)
		if err != nil {
			cwf.logger.Printf("Warning: Failed to decode key %s: %v", keyHex, err)
			continue
		}

		batch.Delete(key)
		totalOperations++

		if cwf.config.Verbose {
			cwf.logger.Printf("Deleting: %s", keyHex)
		}
	}

	// 添加缺失的条目
	for keyHex, value := range missingEntries {
		key, err := hex.DecodeString(keyHex)
		if err != nil {
			cwf.logger.Printf("Warning: Failed to decode key %s: %v", keyHex, err)
			continue
		}

		batch.Put(key, value)
		totalOperations++

		if cwf.config.Verbose {
			cwf.logger.Printf("Adding: %s → %s", keyHex, hex.EncodeToString(value))
		}
	}

	if totalOperations > 0 {
		if cwf.config.DryRun {
			cwf.logger.Printf("[DRY RUN] Would perform %d operations (%d deletions, %d additions)",
				totalOperations, len(extraEntries), len(missingEntries))
		} else {
			if err := cwf.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			cwf.logger.Printf("✅ Performed %d operations (%d deletions, %d additions)",
				totalOperations, len(extraEntries), len(missingEntries))
		}
	} else {
		cwf.logger.Println("✅ No operations needed - database is already correct")
	}

	return nil
}

func (cwf *CorrectWFSFixer) Close() {
	if cwf.db != nil {
		cwf.db.Close()
	}
	if cwf.refDB != nil {
		cwf.refDB.Close()
	}
}

func main() {
	config := &CorrectWFSFixerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.StringVar(&config.ReferencePath, "ref", "", "Reference database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Correct WFS Fixer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -ref C:\\wfsdata_new\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -ref C:\\wfsdata_new\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" || config.ReferencePath == "" {
		fmt.Fprintf(os.Stderr, "Error: Both -db and -ref parameters are required\n")
		flag.Usage()
		os.Exit(1)
	}

	fixer, err := NewCorrectWFSFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
