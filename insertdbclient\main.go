// WFS Database Migration Tool
// 高并发WFS数据库迁移工具，修复文件名路径问题
// 从原有数据库读取数据，修正文件名后写入新数据库

package main

import (
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE   = []byte{0x00, 0x00} // PATH_PRE前缀
	PATH_SEQ   = []byte{0x01, 0x00} // PATH_SEQ前缀
	INDEX_0800 = []byte{0x08, 0x00} // 第三索引前缀
)

// 文件信息结构
type FileRecord struct {
	SeqID        int64
	OriginalPath string
	FileName     string
	PathBeanData []byte
	ContentID    []byte
	ContentData  []byte
	Timestamp    int64
}

// 统计信息
type MigrationStats struct {
	TotalFiles     int64
	ProcessedFiles int64
	SuccessFiles   int64
	ErrorFiles     int64
	StartTime      time.Time
	BytesProcessed int64
}

// 迁移配置
type MigrationConfig struct {
	SourceDBPath string
	TargetDBPath string
	WorkerCount  int
	BatchSize    int
	BufferSize   int
	DryRun       bool
	CreateDirs   bool
}

// 数据库迁移器
type DatabaseMigrator struct {
	config   *MigrationConfig
	sourceDB *leveldb.DB
	targetDB *leveldb.DB
	stats    *MigrationStats
	logger   *log.Logger
	workChan chan *FileRecord
	wg       sync.WaitGroup
	mutex    sync.RWMutex
}

// 创建迁移器
func NewDatabaseMigrator(config *MigrationConfig) *DatabaseMigrator {
	return &DatabaseMigrator{
		config:   config,
		stats:    &MigrationStats{StartTime: time.Now()},
		logger:   log.New(os.Stdout, "[DBMigrator] ", log.LstdFlags),
		workChan: make(chan *FileRecord, config.BufferSize),
	}
}

// 打开源数据库
func (dm *DatabaseMigrator) openSourceDB() error {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     128 * 1024 * 1024, // 128MB
		WriteBuffer:            32 * 1024 * 1024,  // 32MB
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	var err error
	dm.sourceDB, err = leveldb.OpenFile(dm.config.SourceDBPath, options)
	if err != nil {
		dm.logger.Printf("Failed to open source DB normally, attempting recovery...")
		dm.sourceDB, err = leveldb.RecoverFile(dm.config.SourceDBPath, options)
		if err != nil {
			return fmt.Errorf("failed to open/recover source database: %v", err)
		}
		dm.logger.Printf("Source database recovered successfully")
	}

	dm.logger.Printf("Successfully opened source database: %s", dm.config.SourceDBPath)
	return nil
}

// 创建目标数据库目录结构
func (dm *DatabaseMigrator) createTargetDirs() error {
	if !dm.config.CreateDirs {
		return nil
	}

	targetRoot := filepath.Dir(dm.config.TargetDBPath)
	dirs := []string{
		filepath.Join(targetRoot, "logs"),
		filepath.Join(targetRoot, "wfsdb"),
		filepath.Join(targetRoot, "wfsfile"),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %v", dir, err)
		}
		dm.logger.Printf("Created directory: %s", dir)
	}

	return nil
}

// 打开目标数据库
func (dm *DatabaseMigrator) openTargetDB() error {
	if err := dm.createTargetDirs(); err != nil {
		return err
	}

	// 确保目标数据库目录存在
	if err := os.MkdirAll(dm.config.TargetDBPath, 0755); err != nil {
		return fmt.Errorf("failed to create target database directory: %v", err)
	}

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     128 * 1024 * 1024, // 128MB
		WriteBuffer:            32 * 1024 * 1024,  // 32MB
		ReadOnly:               dm.config.DryRun,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	var err error
	dm.targetDB, err = leveldb.OpenFile(dm.config.TargetDBPath, options)
	if err != nil {
		return fmt.Errorf("failed to open target database: %v", err)
	}

	dm.logger.Printf("Successfully opened target database: %s", dm.config.TargetDBPath)
	return nil
}

// 关闭数据库
func (dm *DatabaseMigrator) closeDatabases() error {
	var errs []error

	if dm.sourceDB != nil {
		if err := dm.sourceDB.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close source DB: %v", err))
		}
	}

	if dm.targetDB != nil {
		if err := dm.targetDB.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close target DB: %v", err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("database close errors: %v", errs)
	}

	return nil
}

// 解析protobuf格式的WfsPathBean
func (dm *DatabaseMigrator) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 编码protobuf格式的WfsPathBean
func (dm *DatabaseMigrator) encodeWfsPathBean(path string, timestamp int64) []byte {
	var buf []byte

	// 编码Path字段 (field 1, wire type 2)
	pathBytes := []byte(path)
	tag1 := (1 << 3) | 2
	buf = append(buf, byte(tag1))

	lengthBuf := make([]byte, binary.MaxVarintLen64)
	n := binary.PutUvarint(lengthBuf, uint64(len(pathBytes)))
	buf = append(buf, lengthBuf[:n]...)
	buf = append(buf, pathBytes...)

	// 编码Timestramp字段 (field 2, wire type 0)
	tag2 := (2 << 3) | 0
	buf = append(buf, byte(tag2))

	timestampBuf := make([]byte, binary.MaxVarintLen64)
	n = binary.PutVarint(timestampBuf, timestamp)
	buf = append(buf, timestampBuf[:n]...)

	return buf
}

// 计算文件路径的MD5指纹
func (dm *DatabaseMigrator) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

// 检查路径是否需要修复
func (dm *DatabaseMigrator) needsFix(path string) (bool, string) {
	if strings.Contains(path, "/") || strings.Contains(path, "\\") {
		return true, filepath.Base(path)
	}
	return false, ""
}

// 扫描源数据库
func (dm *DatabaseMigrator) scanSourceDatabase() error {
	dm.logger.Println("=== Scanning Source Database ===")

	// 扫描PATH_PRE索引
	pathPreFiles := make(map[int64]string)
	iter := dm.sourceDB.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_PRE) {
			continue
		}

		path := string(key[len(PATH_PRE):])
		var seqID int64
		if len(value) >= 8 {
			seqID = int64(binary.BigEndian.Uint64(value))
		} else {
			paddedValue := make([]byte, 8)
			copy(paddedValue[8-len(value):], value)
			seqID = int64(binary.BigEndian.Uint64(paddedValue))
		}

		pathPreFiles[seqID] = path
		atomic.AddInt64(&dm.stats.TotalFiles, 1)
	}

	dm.logger.Printf("Found %d files in PATH_PRE index", len(pathPreFiles))

	// 处理每个文件
	for seqID, originalPath := range pathPreFiles {
		needsFix, fileName := dm.needsFix(originalPath)
		if !needsFix {
			fileName = originalPath // 如果不需要修复，保持原文件名
		}

		record := &FileRecord{
			SeqID:        seqID,
			OriginalPath: originalPath,
			FileName:     fileName,
		}

		// 获取PATH_SEQ数据
		if err := dm.getPathSeqData(record); err != nil {
			dm.logger.Printf("Warning: Failed to get PATH_SEQ data for seqID %d: %v", seqID, err)
		}

		// 获取文件内容
		if err := dm.getFileContent(record); err != nil {
			dm.logger.Printf("Warning: Failed to get file content for seqID %d: %v", seqID, err)
		}

		// 发送到工作队列
		dm.workChan <- record
	}

	close(dm.workChan)
	return iter.Error()
}

// 获取PATH_SEQ数据
func (dm *DatabaseMigrator) getPathSeqData(record *FileRecord) error {
	seqIDBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(seqIDBytes, uint64(record.SeqID))
	pathSeqKey := append(PATH_SEQ, seqIDBytes...)

	pathBeanData, err := dm.sourceDB.Get(pathSeqKey, nil)
	if err != nil {
		return err
	}

	// 解析原始数据获取时间戳
	_, timestamp, err := dm.parseWfsPathBean(pathBeanData)
	if err != nil {
		return err
	}

	record.Timestamp = timestamp
	// 重新编码使用新的文件名
	record.PathBeanData = dm.encodeWfsPathBean(record.FileName, timestamp)
	return nil
}

// 获取文件内容
func (dm *DatabaseMigrator) getFileContent(record *FileRecord) error {
	// 计算原始路径的指纹
	originalFingerprint := dm.calculateFingerprint(record.OriginalPath)

	// 尝试获取内容ID
	contentID, err := dm.sourceDB.Get(originalFingerprint, nil)
	if err != nil {
		return err
	}

	// 获取实际文件内容
	contentData, err := dm.sourceDB.Get(contentID, nil)
	if err != nil {
		return err
	}

	record.ContentID = contentID
	record.ContentData = contentData
	atomic.AddInt64(&dm.stats.BytesProcessed, int64(len(contentData)))
	return nil
}

// 工作线程处理函数
func (dm *DatabaseMigrator) worker(workerID int) {
	defer dm.wg.Done()

	batch := new(leveldb.Batch)
	batchCount := 0

	dm.logger.Printf("Worker %d started", workerID)

	for record := range dm.workChan {
		if err := dm.processRecord(record, batch); err != nil {
			dm.logger.Printf("Worker %d: Error processing seqID %d: %v", workerID, record.SeqID, err)
			atomic.AddInt64(&dm.stats.ErrorFiles, 1)
		} else {
			atomic.AddInt64(&dm.stats.SuccessFiles, 1)
		}

		atomic.AddInt64(&dm.stats.ProcessedFiles, 1)
		batchCount++

		// 每达到批处理大小就执行一次写入
		if batchCount >= dm.config.BatchSize {
			if err := dm.writeBatch(batch, workerID); err != nil {
				dm.logger.Printf("Worker %d: Batch write error: %v", workerID, err)
			}
			batch.Reset()
			batchCount = 0
		}
	}

	// 处理剩余的批处理
	if batchCount > 0 {
		if err := dm.writeBatch(batch, workerID); err != nil {
			dm.logger.Printf("Worker %d: Final batch write error: %v", workerID, err)
		}
	}

	dm.logger.Printf("Worker %d completed", workerID)
}

// 处理单个记录
func (dm *DatabaseMigrator) processRecord(record *FileRecord, batch *leveldb.Batch) error {
	seqIDBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(seqIDBytes, uint64(record.SeqID))

	if dm.config.DryRun {
		dm.logger.Printf("DRY RUN: Would migrate seqID %d: %s -> %s",
			record.SeqID, record.OriginalPath, record.FileName)
		return nil
	}

	// 1. 写入PATH_PRE索引：新文件名 -> seqID
	pathPreKey := append(PATH_PRE, []byte(record.FileName)...)
	batch.Put(pathPreKey, seqIDBytes)

	// 2. 写入PATH_SEQ索引：seqID -> 新的WfsPathBean
	if record.PathBeanData != nil {
		pathSeqKey := append(PATH_SEQ, seqIDBytes...)
		batch.Put(pathSeqKey, record.PathBeanData)
	}

	// 3. 写入0x0800索引：seqID -> 新的WfsPathBean
	if record.PathBeanData != nil {
		index0800Key := append(INDEX_0800, make([]byte, 8)...)
		copy(index0800Key[len(INDEX_0800):], seqIDBytes)
		batch.Put(index0800Key, record.PathBeanData)
	}

	// 4. 写入新的指纹索引：新文件名的指纹 -> 内容ID
	if record.ContentID != nil {
		newFingerprint := dm.calculateFingerprint(record.FileName)
		batch.Put(newFingerprint, record.ContentID)
	}

	// 5. 写入文件内容：内容ID -> 文件数据
	if record.ContentData != nil && record.ContentID != nil {
		batch.Put(record.ContentID, record.ContentData)
	}

	return nil
}

// 写入批处理
func (dm *DatabaseMigrator) writeBatch(batch *leveldb.Batch, workerID int) error {
	if dm.config.DryRun {
		return nil
	}

	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	return dm.targetDB.Write(batch, &opt.WriteOptions{Sync: false})
}

// 打印统计信息
func (dm *DatabaseMigrator) printStats() {
	elapsed := time.Since(dm.stats.StartTime)
	total := atomic.LoadInt64(&dm.stats.TotalFiles)
	processed := atomic.LoadInt64(&dm.stats.ProcessedFiles)
	success := atomic.LoadInt64(&dm.stats.SuccessFiles)
	errors := atomic.LoadInt64(&dm.stats.ErrorFiles)
	bytes := atomic.LoadInt64(&dm.stats.BytesProcessed)

	dm.logger.Printf("Progress: %d/%d files, %d success, %d errors, %s processed, elapsed: %v",
		processed, total, success, errors, formatBytes(bytes), elapsed.Truncate(time.Second))
}

// 格式化字节数
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// 执行迁移
func (dm *DatabaseMigrator) Migrate() error {
	dm.logger.Println("=== Starting WFS Database Migration ===")

	// 打开数据库
	if err := dm.openSourceDB(); err != nil {
		return err
	}
	defer dm.closeDatabases()

	if !dm.config.DryRun {
		if err := dm.openTargetDB(); err != nil {
			return err
		}
	} else {
		// 在dry-run模式下，只创建目录结构
		if err := dm.createTargetDirs(); err != nil {
			return err
		}
	}

	if dm.config.DryRun {
		dm.logger.Println("🔍 DRY RUN MODE - No actual changes will be made")
	}

	// 启动工作线程
	for i := 0; i < dm.config.WorkerCount; i++ {
		dm.wg.Add(1)
		go dm.worker(i)
	}

	// 启动统计信息打印协程
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				dm.printStats()
			default:
				if atomic.LoadInt64(&dm.stats.ProcessedFiles) >= atomic.LoadInt64(&dm.stats.TotalFiles) {
					return
				}
				time.Sleep(100 * time.Millisecond)
			}
		}
	}()

	// 扫描源数据库并发送到工作队列
	if err := dm.scanSourceDatabase(); err != nil {
		return err
	}

	// 等待所有工作线程完成
	dm.wg.Wait()

	// 最终同步
	if !dm.config.DryRun {
		if err := dm.targetDB.CompactRange(levelutil.Range{}); err != nil {
			dm.logger.Printf("Warning: Failed to compact target database: %v", err)
		}
	}

	// 打印最终统计信息
	dm.printStats()
	dm.logger.Println("✅ Migration completed successfully!")

	return nil
}

// 主函数
func main() {
	if len(os.Args) < 3 {
		fmt.Println("WFS Database Migration Tool")
		fmt.Println("Usage: insertdbclient <source_db_path> <target_db_path> [options]")
		fmt.Println("")
		fmt.Println("Options:")
		fmt.Println("  -workers <n>     : Number of worker threads (default: CPU count)")
		fmt.Println("  -batch <n>       : Batch size (default: 1000)")
		fmt.Println("  -buffer <n>      : Buffer size (default: 10000)")
		fmt.Println("  -dry-run         : Dry run mode (no actual changes)")
		fmt.Println("  -create-dirs     : Create target directory structure")
		fmt.Println("")
		fmt.Println("Examples:")
		fmt.Println("  insertdbclient source/wfsdb target/wfsdb -workers 8 -create-dirs")
		fmt.Println("  insertdbclient source/wfsdb target/wfsdb -dry-run")
		os.Exit(1)
	}

	config := &MigrationConfig{
		SourceDBPath: os.Args[1],
		TargetDBPath: os.Args[2],
		WorkerCount:  runtime.NumCPU(),
		BatchSize:    1000,
		BufferSize:   10000,
		DryRun:       false,
		CreateDirs:   false,
	}

	// 解析参数
	for i := 3; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-workers":
			if i+1 < len(os.Args) {
				fmt.Sscanf(os.Args[i+1], "%d", &config.WorkerCount)
				i++
			}
		case "-batch":
			if i+1 < len(os.Args) {
				fmt.Sscanf(os.Args[i+1], "%d", &config.BatchSize)
				i++
			}
		case "-buffer":
			if i+1 < len(os.Args) {
				fmt.Sscanf(os.Args[i+1], "%d", &config.BufferSize)
				i++
			}
		case "-dry-run":
			config.DryRun = true
		case "-create-dirs":
			config.CreateDirs = true
		}
	}

	// 验证参数
	if config.WorkerCount <= 0 {
		config.WorkerCount = runtime.NumCPU()
	}
	if config.BatchSize <= 0 {
		config.BatchSize = 1000
	}
	if config.BufferSize <= 0 {
		config.BufferSize = 10000
	}

	// 创建并运行迁移器
	migrator := NewDatabaseMigrator(config)
	if err := migrator.Migrate(); err != nil {
		log.Fatalf("Migration failed: %v", err)
	}
}
