// 分析指纹索引工具
package main

import (
	"crypto/md5"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type FingerprintAnalyzerConfig struct {
	DatabasePath string
	Verbose      bool
}

type FingerprintAnalyzer struct {
	config *FingerprintAnalyzerConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewFingerprintAnalyzer(config *FingerprintAnalyzerConfig) (*FingerprintAnalyzer, error) {
	logger := log.New(os.Stdout, "[FingerprintAnalyzer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &FingerprintAnalyzer{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (fa *FingerprintAnalyzer) Analyze() error {
	fa.logger.Println("=== Fingerprint Analysis ===")

	// 1. 收集所有PATH_PRE文件
	files := fa.collectFiles()

	// 2. 计算每个文件的指纹
	fa.analyzeFingerprints(files)

	// 3. 扫描所有可能的指纹索引
	fa.scanPossibleFingerprints()

	return nil
}

func (fa *FingerprintAnalyzer) collectFiles() map[string]int64 {
	fa.logger.Println("Collecting files from PATH_PRE...")

	files := make(map[string]int64)
	iter := fa.db.NewIterator(nil, nil)
	defer iter.Release()

	PATH_PRE := []byte{0x00, 0x00}
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := fa.bytesToInt64(seqIDBytes)
				files[path] = seqID

				if fa.config.Verbose {
					fa.logger.Printf("File: %s, seqID=%d", path, seqID)
				}
			}
		}
	}

	fa.logger.Printf("Found %d files", len(files))
	return files
}

func (fa *FingerprintAnalyzer) analyzeFingerprints(files map[string]int64) {
	fa.logger.Println("\nAnalyzing fingerprints for each file...")

	for path, seqID := range files {
		// 计算MD5指纹
		md5Hash := md5.Sum([]byte(path))
		md5Hex := hex.EncodeToString(md5Hash[:])

		fa.logger.Printf("File: %s", path)
		fa.logger.Printf("  SeqID: %d", seqID)
		fa.logger.Printf("  MD5: %s", md5Hex)

		// 检查是否存在对应的指纹索引
		if value, err := fa.db.Get(md5Hash[:], nil); err == nil {
			fa.logger.Printf("  ✅ Fingerprint exists, points to: %s", hex.EncodeToString(value))
		} else {
			fa.logger.Printf("  ❌ Fingerprint missing")
		}

		// 尝试其他可能的指纹算法
		fa.tryOtherFingerprints(path, seqID)
		fa.logger.Println()
	}
}

func (fa *FingerprintAnalyzer) tryOtherFingerprints(path string, seqID int64) {
	// 尝试不同的指纹算法

	// 1. 尝试前8字节的MD5
	md5Hash := md5.Sum([]byte(path))
	if value, err := fa.db.Get(md5Hash[:8], nil); err == nil {
		fa.logger.Printf("  ✅ MD5[0:8] exists, points to: %s", hex.EncodeToString(value))
	}

	// 2. 尝试后8字节的MD5
	if value, err := fa.db.Get(md5Hash[8:], nil); err == nil {
		fa.logger.Printf("  ✅ MD5[8:16] exists, points to: %s", hex.EncodeToString(value))
	}

	// 3. 尝试简单哈希
	simpleHash := fa.simpleHash(path)
	if value, err := fa.db.Get(simpleHash, nil); err == nil {
		fa.logger.Printf("  ✅ SimpleHash exists, points to: %s", hex.EncodeToString(value))
	}
}

func (fa *FingerprintAnalyzer) simpleHash(s string) []byte {
	// 简单的哈希算法
	hash := make([]byte, 8)
	for i, b := range []byte(s) {
		hash[i%8] ^= b
	}
	return hash
}

func (fa *FingerprintAnalyzer) scanPossibleFingerprints() {
	fa.logger.Println("Scanning for possible fingerprint entries...")

	iter := fa.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if fa.isKnownIndex(key) {
			continue
		}

		// 检查是否可能是指纹索引
		if fa.isPossibleFingerprint(key, value) {
			count++
			fa.logger.Printf("Possible fingerprint %d:", count)
			fa.logger.Printf("  Key: %s", hex.EncodeToString(key))
			fa.logger.Printf("  Value: %s", hex.EncodeToString(value))

			// 尝试解析value
			if len(value) == 8 {
				seqID := fa.bytesToInt64(value)
				fa.logger.Printf("  Possible SeqID: %d", seqID)
			}
			fa.logger.Println()
		}
	}

	fa.logger.Printf("Found %d possible fingerprint entries", count)
}

func (fa *FingerprintAnalyzer) isKnownIndex(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
		{0x06, 0x00}, // 其他已知前缀
		{0x07, 0x00},
		{0x09, 0x00},
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (fa *FingerprintAnalyzer) isPossibleFingerprint(key, value []byte) bool {
	// 指纹索引的特征：
	// 1. key长度通常是8或16字节
	// 2. value通常是8字节（seqID或文件内容ID）
	// 3. 不是已知的索引前缀

	if len(key) != 8 && len(key) != 16 {
		return false
	}

	if len(value) != 8 {
		return false
	}

	return true
}

func (fa *FingerprintAnalyzer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	
	// 尝试大端序
	result := int64(0)
	for i := 0; i < 8; i++ {
		result = (result << 8) | int64(bs[i])
	}
	return result
}

func (fa *FingerprintAnalyzer) Close() {
	if fa.db != nil {
		fa.db.Close()
	}
}

func main() {
	config := &FingerprintAnalyzerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Fingerprint Analyzer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	analyzer, err := NewFingerprintAnalyzer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer analyzer.Close()

	if err := analyzer.Analyze(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
