// WFS文件内容恢复工具 - 修复指纹索引指向正确的文件内容
package main

import (
	"crypto/md5"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_RECOVERY = []byte{0x00, 0x00}
)

type ContentRecoveryConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type WFSContentRecovery struct {
	config *ContentRecoveryConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewWFSContentRecovery(config *ContentRecoveryConfig) (*WFSContentRecovery, error) {
	logger := log.New(os.Stdout, "[WFSContentRecovery] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &WFSContentRecovery{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (wcr *WFSContentRecovery) Recover() error {
	wcr.logger.Println("=== WFS Content Recovery ===")

	// 1. 扫描所有可能的文件内容
	contentMap := wcr.scanFileContents()

	// 2. 收集需要修复的文件
	files := wcr.collectFiles()

	// 3. 尝试恢复指纹索引
	wcr.recoverFingerprints(files, contentMap)

	return nil
}

func (wcr *WFSContentRecovery) scanFileContents() map[string][]byte {
	wcr.logger.Println("Scanning for file contents...")

	contentMap := make(map[string][]byte)
	iter := wcr.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if wcr.isIndexKey(key) {
			continue
		}

		// 检查是否可能是文件内容
		if wcr.isPossibleFileContent(value) {
			keyStr := string(key)
			contentMap[keyStr] = make([]byte, len(value))
			copy(contentMap[keyStr], value)
			count++

			if wcr.config.Verbose && count <= 10 {
				wcr.logger.Printf("Found possible content: key=%x, size=%d bytes", key, len(value))
			}
		}
	}

	wcr.logger.Printf("Found %d possible file content entries", count)
	return contentMap
}

func (wcr *WFSContentRecovery) isIndexKey(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (wcr *WFSContentRecovery) isPossibleFileContent(value []byte) bool {
	// 简单的启发式检查：
	// 1. 长度大于0
	// 2. 不是很短的数据（可能是索引数据）
	// 3. 不是纯文本的protobuf数据
	
	if len(value) == 0 {
		return false
	}

	// 如果太短，可能是索引数据
	if len(value) < 100 {
		return false
	}

	// 检查是否是图片文件（JPEG, PNG等）
	if wcr.isImageContent(value) {
		return true
	}

	// 其他可能的文件内容
	return len(value) > 1000 // 假设文件内容至少1KB
}

func (wcr *WFSContentRecovery) isImageContent(value []byte) bool {
	if len(value) < 4 {
		return false
	}

	// JPEG文件头
	if value[0] == 0xFF && value[1] == 0xD8 {
		return true
	}

	// PNG文件头
	if len(value) >= 8 && value[0] == 0x89 && value[1] == 0x50 && 
		value[2] == 0x4E && value[3] == 0x47 {
		return true
	}

	// GIF文件头
	if len(value) >= 6 && string(value[0:3]) == "GIF" {
		return true
	}

	return false
}

func (wcr *WFSContentRecovery) collectFiles() map[int64]string {
	wcr.logger.Println("Collecting files from PATH_PRE...")

	files := make(map[int64]string)
	iter := wcr.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_RECOVERY[0] && key[1] == PATH_PRE_RECOVERY[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := wcr.bytesToInt64(seqIDBytes)
				files[seqID] = path

				if wcr.config.Verbose {
					wcr.logger.Printf("File: seqID=%d, path=%s", seqID, path)
				}
			}
		}
	}

	wcr.logger.Printf("Found %d files to recover", len(files))
	return files
}

func (wcr *WFSContentRecovery) recoverFingerprints(files map[int64]string, contentMap map[string][]byte) {
	wcr.logger.Println("\n=== Fingerprint Recovery ===")

	batch := new(leveldb.Batch)
	recoveredCount := 0

	for seqID, path := range files {
		wcr.logger.Printf("Recovering seqID %d: %s", seqID, path)

		// 计算文件路径的指纹
		fingerprint := wcr.calculateFingerprint(path)

		// 检查当前指纹索引
		currentContent, err := wcr.db.Get(fingerprint, nil)
		if err == nil {
			// 检查指向的内容是否存在
			if _, contentErr := wcr.db.Get(currentContent, nil); contentErr == nil {
				wcr.logger.Printf("  ✅ Fingerprint already points to valid content")
				continue
			}
		}

		// 尝试找到合适的文件内容
		var bestContentKey []byte
		var bestContent []byte

		// 策略1: 使用seqID作为内容key
		seqIDBytes := wcr.int64ToBytes(seqID)
		if content, exists := contentMap[string(seqIDBytes)]; exists {
			bestContentKey = seqIDBytes
			bestContent = content
			wcr.logger.Printf("  Found content using seqID key")
		}

		// 策略2: 查找最大的可能文件内容
		if bestContent == nil {
			maxSize := 0
			for keyStr, content := range contentMap {
				if len(content) > maxSize && wcr.isImageContent(content) {
					maxSize = len(content)
					bestContentKey = []byte(keyStr)
					bestContent = content
				}
			}
			if bestContent != nil {
				wcr.logger.Printf("  Found content using largest image heuristic (%d bytes)", len(bestContent))
			}
		}

		if bestContent != nil {
			if wcr.config.DryRun {
				wcr.logger.Printf("  [DRY RUN] Would fix fingerprint: %x -> %x", fingerprint, bestContentKey)
			} else {
				batch.Put(fingerprint, bestContentKey)
				wcr.logger.Printf("  ✅ Fixed fingerprint: %x -> %x", fingerprint, bestContentKey)
			}
			recoveredCount++
		} else {
			wcr.logger.Printf("  ❌ No suitable content found")
		}
	}

	if recoveredCount > 0 && !wcr.config.DryRun {
		if err := wcr.db.Write(batch, nil); err != nil {
			wcr.logger.Printf("Error writing batch: %v", err)
		} else {
			wcr.logger.Printf("✅ Successfully recovered %d fingerprint indexes", recoveredCount)
		}
	} else if wcr.config.DryRun {
		wcr.logger.Printf("[DRY RUN] Would recover %d fingerprint indexes", recoveredCount)
	} else {
		wcr.logger.Printf("No fingerprints needed recovery")
	}
}

func (wcr *WFSContentRecovery) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wcr *WFSContentRecovery) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (wcr *WFSContentRecovery) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func (wcr *WFSContentRecovery) Close() {
	if wcr.db != nil {
		wcr.db.Close()
	}
}

func main() {
	config := &ContentRecoveryConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Content Recovery Tool\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	recovery, err := NewWFSContentRecovery(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer recovery.Close()

	if err := recovery.Recover(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
