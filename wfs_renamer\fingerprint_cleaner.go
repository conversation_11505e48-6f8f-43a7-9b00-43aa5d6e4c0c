// 指纹索引清理工具 - 删除错误的指纹索引，只保留正确的
package main

import (
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

var (
	PATH_PRE_CLEAN = []byte{0x00, 0x00}
)

type FingerprintCleanerConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type FingerprintCleaner struct {
	config   *FingerprintCleanerConfig
	db       *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
}

func NewFingerprintCleaner(config *FingerprintCleanerConfig) (*FingerprintCleaner, error) {
	logger := log.New(os.Stdout, "[FingerprintCleaner] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	// 尝试多次打开数据库
	var db *leveldb.DB
	var err error
	
	for i := 0; i < 5; i++ {
		db, err = leveldb.OpenFile(config.DatabasePath, options)
		if err == nil {
			break
		}
		logger.Printf("Attempt %d failed: %v, retrying in 1 second...", i+1, err)
		time.Sleep(1 * time.Second)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to open database after 5 attempts: %v", err)
	}

	crcTable := crc64.MakeTable(crc64.ISO)

	return &FingerprintCleaner{
		config:   config,
		db:       db,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (fc *FingerprintCleaner) Clean() error {
	fc.logger.Println("=== Fingerprint Index Cleanup ===")

	// 1. 收集所有文件的正确指纹
	correctFingerprints := fc.collectCorrectFingerprints()

	// 2. 收集所有现有的指纹索引
	allFingerprints := fc.collectAllFingerprints()

	// 3. 找出需要删除的错误指纹
	wrongFingerprints := fc.findWrongFingerprints(correctFingerprints, allFingerprints)

	// 4. 删除错误的指纹索引
	return fc.deleteWrongFingerprints(wrongFingerprints)
}

func (fc *FingerprintCleaner) collectCorrectFingerprints() map[string]bool {
	fc.logger.Println("Collecting correct fingerprints...")

	correctFingerprints := make(map[string]bool)
	iter := fc.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_CLEAN[0] && key[1] == PATH_PRE_CLEAN[1] {
			if len(key) > 2 {
				path := string(key[2:])
				cleanPath := fc.cleanFileName(path)
				
				// 计算正确的CRC64指纹
				crc64Value := fc.calculateCRC64(cleanPath)
				crc64Bytes := fc.int64ToBytes(int64(crc64Value))
				crc64Hex := hex.EncodeToString(crc64Bytes)
				
				correctFingerprints[crc64Hex] = true

				if fc.config.Verbose {
					fc.logger.Printf("Correct fingerprint: %s -> CRC64=%d -> %s", cleanPath, crc64Value, crc64Hex)
				}
			}
		}
	}

	fc.logger.Printf("Found %d correct fingerprints", len(correctFingerprints))
	return correctFingerprints
}

func (fc *FingerprintCleaner) collectAllFingerprints() map[string][]byte {
	fc.logger.Println("Collecting all fingerprint indexes...")

	allFingerprints := make(map[string][]byte)
	iter := fc.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if fc.isKnownIndex(key) {
			continue
		}

		// 指纹索引：key长度8字节，value长度8字节
		if len(key) == 8 && len(value) == 8 {
			keyHex := hex.EncodeToString(key)
			valueBytes := make([]byte, len(value))
			copy(valueBytes, value)
			allFingerprints[keyHex] = valueBytes

			if fc.config.Verbose {
				crc64Value := fc.bytesToInt64(key)
				contentID := hex.EncodeToString(value)
				fc.logger.Printf("Found fingerprint: CRC64=%d -> ContentID=%s", crc64Value, contentID)
			}
		}
	}

	fc.logger.Printf("Found %d total fingerprint indexes", len(allFingerprints))
	return allFingerprints
}

func (fc *FingerprintCleaner) findWrongFingerprints(correctFingerprints map[string]bool, allFingerprints map[string][]byte) map[string][]byte {
	fc.logger.Println("Finding wrong fingerprint indexes...")

	wrongFingerprints := make(map[string][]byte)

	for keyHex, value := range allFingerprints {
		if !correctFingerprints[keyHex] {
			wrongFingerprints[keyHex] = value

			if fc.config.Verbose {
				crc64Value := fc.bytesToInt64([]byte(keyHex))
				contentID := hex.EncodeToString(value)
				fc.logger.Printf("Wrong fingerprint: CRC64=%d -> ContentID=%s", crc64Value, contentID)
			}
		}
	}

	fc.logger.Printf("Found %d wrong fingerprint indexes to delete", len(wrongFingerprints))
	return wrongFingerprints
}

func (fc *FingerprintCleaner) deleteWrongFingerprints(wrongFingerprints map[string][]byte) error {
	if len(wrongFingerprints) == 0 {
		fc.logger.Println("✅ No wrong fingerprint indexes to delete")
		return nil
	}

	fc.logger.Printf("Deleting %d wrong fingerprint indexes...", len(wrongFingerprints))

	batch := new(leveldb.Batch)
	deletedCount := 0

	for keyHex := range wrongFingerprints {
		key, err := hex.DecodeString(keyHex)
		if err != nil {
			fc.logger.Printf("Warning: Failed to decode key %s: %v", keyHex, err)
			continue
		}

		batch.Delete(key)
		deletedCount++

		if fc.config.Verbose {
			crc64Value := fc.bytesToInt64(key)
			fc.logger.Printf("Deleting wrong fingerprint: CRC64=%d", crc64Value)
		}
	}

	if deletedCount > 0 {
		if fc.config.DryRun {
			fc.logger.Printf("[DRY RUN] Would delete %d wrong fingerprint indexes", deletedCount)
		} else {
			if err := fc.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			fc.logger.Printf("✅ Deleted %d wrong fingerprint indexes", deletedCount)
		}
	}

	return nil
}

func (fc *FingerprintCleaner) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	return filepath.Base(path)
}

func (fc *FingerprintCleaner) isKnownIndex(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
		{0x06, 0x00}, // 其他已知前缀
		{0x07, 0x00},
		{0x09, 0x00},
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (fc *FingerprintCleaner) calculateCRC64(path string) uint64 {
	return crc64.Checksum([]byte(path), fc.crcTable)
}

func (fc *FingerprintCleaner) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (fc *FingerprintCleaner) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (fc *FingerprintCleaner) Close() {
	if fc.db != nil {
		fc.db.Close()
	}
}

func main() {
	config := &FingerprintCleanerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Fingerprint Index Cleaner\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	cleaner, err := NewFingerprintCleaner(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer cleaner.Close()

	if err := cleaner.Clean(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
