// SEQ检查工具 - 检查数据库中的SEQ值
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

var (
	SEQ_KEY = []byte("SEQ")
	COUNT_KEY = []byte("COUNT")
)

type SeqCheckerConfig struct {
	DatabasePath string
	Verbose      bool
}

type Seq<PERSON><PERSON><PERSON> struct {
	config *SeqCheckerConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewSeqChecker(config *SeqCheckerConfig) (*SeqChecker, error) {
	logger := log.New(os.Stdout, "[SeqChecker] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	// 尝试多次打开数据库
	var db *leveldb.DB
	var err error
	
	for i := 0; i < 5; i++ {
		db, err = leveldb.OpenFile(config.DatabasePath, options)
		if err == nil {
			break
		}
		logger.Printf("Attempt %d failed: %v, retrying in 1 second...", i+1, err)
		time.Sleep(1 * time.Second)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to open database after 5 attempts: %v", err)
	}

	return &SeqChecker{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (sc *SeqChecker) Check() error {
	sc.logger.Println("=== SEQ Value Analysis ===")

	// 检查SEQ值
	seqValue, err := sc.db.Get(SEQ_KEY, nil)
	if err != nil {
		sc.logger.Printf("❌ SEQ key not found: %v", err)
	} else {
		seq := sc.bytesToInt64(seqValue)
		sc.logger.Printf("✅ SEQ value: %d", seq)
	}

	// 检查COUNT值
	countValue, err := sc.db.Get(COUNT_KEY, nil)
	if err != nil {
		sc.logger.Printf("❌ COUNT key not found: %v", err)
	} else {
		count := sc.bytesToInt64(countValue)
		sc.logger.Printf("✅ COUNT value: %d", count)
	}

	// 模拟findLimit逻辑
	sc.simulateFindLimit()

	return nil
}

func (sc *SeqChecker) simulateFindLimit() {
	sc.logger.Println("\n--- Simulating findLimit Logic ---")

	// 获取seq值
	seqValue, err := sc.db.Get(SEQ_KEY, nil)
	if err != nil {
		sc.logger.Printf("Cannot simulate: SEQ not found")
		return
	}
	seq := sc.bytesToInt64(seqValue)

	sc.logger.Printf("Current seq value: %d", seq)
	sc.logger.Printf("Simulating findLimit(start=10, limit=10):")

	PATH_SEQ := []byte{0x01, 0x00}
	count := int64(0)
	limit := int64(10)

	for i := int64(10); i > 0 && count < limit; i-- {
		pathseqkey := append(PATH_SEQ, sc.int64ToBytes(i)...)
		_, err := sc.db.Get(pathseqkey, nil)
		
		if err == nil {
			sc.logger.Printf("  i=%d: PATH_SEQ exists -> would process", i)
			count++
		} else if i > seq {
			sc.logger.Printf("  i=%d: PATH_SEQ missing but i > seq (%d) -> count++", i, seq)
			count++
		} else {
			sc.logger.Printf("  i=%d: PATH_SEQ missing and i <= seq (%d) -> skip", i, seq)
		}
	}

	sc.logger.Printf("Final count: %d", count)
	
	if count == 5 {
		sc.logger.Printf("🎯 This explains why 5 files are counted!")
	}
}

func (sc *SeqChecker) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (sc *SeqChecker) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (sc *SeqChecker) Close() {
	if sc.db != nil {
		sc.db.Close()
	}
}

func main() {
	config := &SeqCheckerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "SEQ Checker\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	checker, err := NewSeqChecker(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer checker.Close()

	if err := checker.Check(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
