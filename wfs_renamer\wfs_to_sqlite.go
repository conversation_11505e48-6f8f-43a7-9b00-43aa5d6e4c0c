// WFS数据库导出到SQLite工具
package main

import (
	"crypto/md5"
	"database/sql"
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00}
	PATH_SEQ = []byte{0x01, 0x00}
)

type WFSRecord struct {
	ID             int64
	OriginalPath   string
	CleanFileName  string
	SeqID          int64
	SeqIDHex       string
	HasPATH_PRE    bool
	HasPATH_SEQ    bool
	PATH_SEQData   string
	Has0x0800      bool
	HasFingerprint bool
	FingerprintHex string
	ContentIDHex   string
	Timestamp      int64
	IsComplete     bool
}

type WFSToSQLite struct {
	wfsDB    *leveldb.DB
	sqliteDB *sql.DB
	logger   *log.Logger
}

func NewWFSToSQLite(wfsPath, sqlitePath string) (*WFSToSQLite, error) {
	logger := log.New(os.Stdout, "[WFSToSQLite] ", log.LstdFlags)

	// 打开WFS数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	wfsDB, err := leveldb.OpenFile(wfsPath, options)
	if err != nil {
		logger.Printf("Failed to open WFS DB normally, attempting recovery...")
		wfsDB, err = leveldb.RecoverFile(wfsPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover WFS database: %v", err)
		}
	}

	// 打开SQLite数据库
	sqliteDB, err := sql.Open("sqlite3", sqlitePath)
	if err != nil {
		wfsDB.Close()
		return nil, fmt.Errorf("failed to open SQLite database: %v", err)
	}

	return &WFSToSQLite{
		wfsDB:    wfsDB,
		sqliteDB: sqliteDB,
		logger:   logger,
	}, nil
}

func (wts *WFSToSQLite) CreateTables() error {
	wts.logger.Println("Creating SQLite tables...")

	createTableSQL := `
	CREATE TABLE IF NOT EXISTS wfs_files (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		original_path TEXT NOT NULL,
		clean_filename TEXT NOT NULL,
		seq_id INTEGER NOT NULL,
		seq_id_hex TEXT NOT NULL,
		has_path_pre BOOLEAN NOT NULL,
		has_path_seq BOOLEAN NOT NULL,
		path_seq_data TEXT,
		has_0x0800 BOOLEAN NOT NULL,
		has_fingerprint BOOLEAN NOT NULL,
		fingerprint_hex TEXT,
		content_id_hex TEXT,
		timestamp INTEGER,
		is_complete BOOLEAN NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_original_path ON wfs_files(original_path);
	CREATE INDEX IF NOT EXISTS idx_clean_filename ON wfs_files(clean_filename);
	CREATE INDEX IF NOT EXISTS idx_seq_id ON wfs_files(seq_id);
	CREATE INDEX IF NOT EXISTS idx_is_complete ON wfs_files(is_complete);
	`

	_, err := wts.sqliteDB.Exec(createTableSQL)
	if err != nil {
		return fmt.Errorf("failed to create tables: %v", err)
	}

	wts.logger.Println("SQLite tables created successfully")
	return nil
}

func (wts *WFSToSQLite) Export() error {
	wts.logger.Println("=== Starting WFS to SQLite Export ===")

	// 清空现有数据
	if _, err := wts.sqliteDB.Exec("DELETE FROM wfs_files"); err != nil {
		return fmt.Errorf("failed to clear existing data: %v", err)
	}

	// 收集所有文件信息
	records, err := wts.collectAllRecords()
	if err != nil {
		return fmt.Errorf("failed to collect records: %v", err)
	}

	wts.logger.Printf("Collected %d records", len(records))

	// 插入到SQLite
	if err := wts.insertRecords(records); err != nil {
		return fmt.Errorf("failed to insert records: %v", err)
	}

	wts.logger.Println("✅ Export completed successfully")
	return nil
}

func (wts *WFSToSQLite) collectAllRecords() ([]WFSRecord, error) {
	wts.logger.Println("Collecting all WFS records...")

	records := make(map[string]*WFSRecord)
	timestampMap := make(map[string]int64)

	// 1. 从0x0800索引获取时间戳
	wts.logger.Println("Phase 1: Scanning 0x0800 index for timestamps...")
	prefix0800 := []byte{0x08, 0x00}
	iter := wts.wfsDB.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		value := iter.Value()
		if path, timestamp, err := wts.parseWfsPathBean(value); err == nil {
			timestampMap[path] = timestamp
		}
	}

	wts.logger.Printf("Found %d timestamp entries", len(timestampMap))

	// 2. 扫描PATH_PRE索引
	wts.logger.Println("Phase 2: Scanning PATH_PRE index...")
	iter = wts.wfsDB.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := wts.bytesToInt64(seqIDBytes)

				record := &WFSRecord{
					OriginalPath:  originalPath,
					CleanFileName: wts.cleanFileName(originalPath),
					SeqID:         seqID,
					SeqIDHex:      hex.EncodeToString(seqIDBytes),
					HasPATH_PRE:   true,
					Timestamp:     timestampMap[originalPath],
				}

				records[originalPath] = record
			}
		}
	}

	wts.logger.Printf("Found %d PATH_PRE entries", len(records))

	// 3. 检查PATH_SEQ索引
	wts.logger.Println("Phase 3: Checking PATH_SEQ index...")
	for path, record := range records {
		seqIDBytes := wts.int64ToBytes(record.SeqID)
		pathSeqKey := append(PATH_SEQ, seqIDBytes...)

		if data, err := wts.wfsDB.Get(pathSeqKey, nil); err == nil {
			record.HasPATH_SEQ = true
			record.PATH_SEQData = hex.EncodeToString(data)

			if seqPath, timestamp, err := wts.parseWfsPathBean(data); err == nil {
				if timestamp != 0 && record.Timestamp == 0 {
					record.Timestamp = timestamp
				}
				// 验证路径一致性
				if seqPath != path {
					wts.logger.Printf("⚠️  PATH_SEQ path mismatch: %s vs %s", path, seqPath)
				}
			}
		}
	}

	// 4. 检查0x0800索引
	wts.logger.Println("Phase 4: Checking 0x0800 index...")
	for path, record := range records {
		if _, exists := timestampMap[path]; exists {
			record.Has0x0800 = true
		}
	}

	// 5. 检查指纹索引
	wts.logger.Println("Phase 5: Checking fingerprint index...")
	for path, record := range records {
		fingerprint := wts.calculateFingerprint(path)
		record.FingerprintHex = hex.EncodeToString(fingerprint)

		if contentID, err := wts.wfsDB.Get(fingerprint, nil); err == nil {
			record.HasFingerprint = true
			record.ContentIDHex = hex.EncodeToString(contentID)
		}
	}

	// 6. 确定完整性
	for _, record := range records {
		record.IsComplete = record.HasPATH_PRE && record.HasPATH_SEQ && record.HasFingerprint
	}

	// 转换为切片
	var result []WFSRecord
	for _, record := range records {
		result = append(result, *record)
	}

	return result, nil
}

func (wts *WFSToSQLite) insertRecords(records []WFSRecord) error {
	wts.logger.Println("Inserting records into SQLite...")

	tx, err := wts.sqliteDB.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	stmt, err := tx.Prepare(`
		INSERT INTO wfs_files (
			original_path, clean_filename, seq_id, seq_id_hex,
			has_path_pre, has_path_seq, path_seq_data, has_0x0800,
			has_fingerprint, fingerprint_hex, content_id_hex,
			timestamp, is_complete
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %v", err)
	}
	defer stmt.Close()

	for _, record := range records {
		_, err := stmt.Exec(
			record.OriginalPath,
			record.CleanFileName,
			record.SeqID,
			record.SeqIDHex,
			record.HasPATH_PRE,
			record.HasPATH_SEQ,
			record.PATH_SEQData,
			record.Has0x0800,
			record.HasFingerprint,
			record.FingerprintHex,
			record.ContentIDHex,
			record.Timestamp,
			record.IsComplete,
		)
		if err != nil {
			return fmt.Errorf("failed to insert record %s: %v", record.OriginalPath, err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	wts.logger.Printf("Successfully inserted %d records", len(records))
	return nil
}

func (wts *WFSToSQLite) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	parts := strings.Split(path, "/")
	return parts[len(parts)-1]
}

// 辅助函数
func (wts *WFSToSQLite) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wts *WFSToSQLite) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (wts *WFSToSQLite) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func (wts *WFSToSQLite) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wts *WFSToSQLite) Close() {
	if wts.wfsDB != nil {
		wts.wfsDB.Close()
	}
	if wts.sqliteDB != nil {
		wts.sqliteDB.Close()
	}
}

func main() {
	var wfsPath, sqlitePath string
	var verbose bool

	flag.StringVar(&wfsPath, "wfs", "", "WFS database path (required)")
	flag.StringVar(&sqlitePath, "sqlite", "", "SQLite output path (optional, default: wfs_export.db)")
	flag.BoolVar(&verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS to SQLite Exporter\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -wfs C:\\wfsdata\\wfsdb\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -wfs C:\\wfsdata\\wfsdb -sqlite wfs_data.db\n", os.Args[0])
	}

	flag.Parse()

	if wfsPath == "" {
		fmt.Fprintf(os.Stderr, "Error: -wfs parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	if sqlitePath == "" {
		sqlitePath = filepath.Join(filepath.Dir(wfsPath), "wfs_export.db")
	}

	exporter, err := NewWFSToSQLite(wfsPath, sqlitePath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer exporter.Close()

	if err := exporter.CreateTables(); err != nil {
		fmt.Fprintf(os.Stderr, "Error creating tables: %v\n", err)
		os.Exit(1)
	}

	if err := exporter.Export(); err != nil {
		fmt.Fprintf(os.Stderr, "Error during export: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Export completed successfully: %s\n", sqlitePath)
}
