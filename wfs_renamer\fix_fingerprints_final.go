// 最终修复指纹索引工具 - 让指纹指向正确的文件内容ID
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type FinalFingerprintFixerConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type FinalFingerprintFixer struct {
	config   *FinalFingerprintFixerConfig
	db       *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
}

func NewFinalFingerprintFixer(config *FinalFingerprintFixerConfig) (*FinalFingerprintFixer, error) {
	logger := log.New(os.Stdout, "[FinalFingerprintFixer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	// 创建CRC64表（使用ISO多项式）
	crcTable := crc64.MakeTable(crc64.ISO)

	return &FinalFingerprintFixer{
		config:   config,
		db:       db,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (fff *FinalFingerprintFixer) Fix() error {
	fff.logger.Println("=== Final Fingerprint Fix - Pointing to Correct File Content ID ===")

	// 1. 收集所有文件和它们的seqID
	files := fff.collectFiles()

	// 2. 查找每个文件对应的文件内容ID
	fileContentIDs := fff.findFileContentIDs(files)

	// 3. 修复指纹索引，让它们指向正确的文件内容ID
	return fff.fixFingerprints(files, fileContentIDs)
}

func (fff *FinalFingerprintFixer) collectFiles() map[string]int64 {
	fff.logger.Println("Collecting files from PATH_PRE...")

	files := make(map[string]int64)
	iter := fff.db.NewIterator(nil, nil)
	defer iter.Release()

	PATH_PRE := []byte{0x00, 0x00}
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := fff.bytesToInt64(seqIDBytes)
				files[path] = seqID

				if fff.config.Verbose {
					fff.logger.Printf("File: %s, seqID=%d", path, seqID)
				}
			}
		}
	}

	fff.logger.Printf("Found %d files", len(files))
	return files
}

func (fff *FinalFingerprintFixer) findFileContentIDs(files map[string]int64) map[string][]byte {
	fff.logger.Println("Finding file content IDs...")

	fileContentIDs := make(map[string][]byte)

	// 策略1：扫描所有可能的文件内容ID条目
	// 在WFS中，文件内容ID通常是随机生成的，我们需要找到指向WfsFileBean的条目
	iter := fff.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if fff.isKnownIndex(key) {
			continue
		}

		// 检查是否可能是WfsFileBean
		if fff.looksLikeWfsFileBean(value) {
			// 这个key可能是文件内容ID
			contentID := make([]byte, len(key))
			copy(contentID, key)

			// 尝试找到对应的文件
			// 由于我们不知道具体的映射关系，我们使用启发式方法
			for path, seqID := range files {
				// 检查是否已经为这个文件找到了内容ID
				if _, exists := fileContentIDs[path]; !exists {
					// 尝试使用seqID作为文件内容ID（WFS的一种可能行为）
					seqIDBytes := fff.int64ToBytes(seqID)
					if wfsFileBean, err := fff.db.Get(seqIDBytes, nil); err == nil {
						if fff.looksLikeWfsFileBean(wfsFileBean) {
							fileContentIDs[path] = seqIDBytes
							if fff.config.Verbose {
								fff.logger.Printf("Found content ID for %s: seqID=%d (using seqID as contentID)", path, seqID)
							}
						}
					}
				}
			}
		}
	}

	// 策略2：对于没有找到文件内容ID的文件，使用seqID作为文件内容ID
	for path, seqID := range files {
		if _, exists := fileContentIDs[path]; !exists {
			seqIDBytes := fff.int64ToBytes(seqID)
			fileContentIDs[path] = seqIDBytes
			if fff.config.Verbose {
				fff.logger.Printf("Using seqID as content ID for %s: seqID=%d", path, seqID)
			}
		}
	}

	fff.logger.Printf("Found content IDs for %d files", len(fileContentIDs))
	return fileContentIDs
}

func (fff *FinalFingerprintFixer) fixFingerprints(files map[string]int64, fileContentIDs map[string][]byte) error {
	fff.logger.Println("Fixing fingerprint indexes to point to correct file content IDs...")

	batch := new(leveldb.Batch)
	fixedCount := 0

	for path, seqID := range files {
		contentID, exists := fileContentIDs[path]
		if !exists {
			fff.logger.Printf("Warning: No content ID found for %s", path)
			continue
		}

		// 计算CRC64指纹
		crc64Value := fff.calculateCRC64(path)
		crc64Bytes := fff.int64ToBytes(int64(crc64Value))

		// 检查当前指纹索引
		if currentValue, err := fff.db.Get(crc64Bytes, nil); err == nil {
			currentSeqID := fff.bytesToInt64(currentValue)
			if currentSeqID == seqID {
				// 当前指纹指向seqID，需要修改为指向文件内容ID
				if string(currentValue) != string(contentID) {
					batch.Put(crc64Bytes, contentID)
					fixedCount++

					if fff.config.Verbose {
						fff.logger.Printf("Fixing fingerprint: %s → CRC64=%d → contentID=%x (was seqID=%d)", 
							path, crc64Value, contentID, currentSeqID)
					}
				} else {
					if fff.config.Verbose {
						fff.logger.Printf("Fingerprint already correct: %s → CRC64=%d → contentID=%x", 
							path, crc64Value, contentID)
					}
				}
			} else {
				fff.logger.Printf("Warning: Fingerprint for %s points to unexpected seqID %d (expected %d)", 
					path, currentSeqID, seqID)
			}
		} else {
			// 指纹不存在，创建新的
			batch.Put(crc64Bytes, contentID)
			fixedCount++

			if fff.config.Verbose {
				fff.logger.Printf("Creating fingerprint: %s → CRC64=%d → contentID=%x", 
					path, crc64Value, contentID)
			}
		}
	}

	if fixedCount > 0 {
		if fff.config.DryRun {
			fff.logger.Printf("[DRY RUN] Would fix %d fingerprint indexes", fixedCount)
		} else {
			if err := fff.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			fff.logger.Printf("✅ Fixed %d fingerprint indexes", fixedCount)
		}
	} else {
		fff.logger.Println("✅ All fingerprint indexes are already correct")
	}

	return nil
}

func (fff *FinalFingerprintFixer) looksLikeWfsFileBean(data []byte) bool {
	// 简单的启发式检查：WfsFileBean通常包含特定的protobuf字段
	// 这里使用长度和内容模式来判断
	if len(data) < 10 || len(data) > 200 {
		return false
	}

	// 检查是否包含protobuf的典型模式
	// WfsFileBean通常以特定的字段标签开始
	return true // 简化实现，实际可以更精确
}

func (fff *FinalFingerprintFixer) calculateCRC64(path string) uint64 {
	return crc64.Checksum([]byte(path), fff.crcTable)
}

func (fff *FinalFingerprintFixer) isKnownIndex(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
		{0x06, 0x00}, // 其他已知前缀
		{0x07, 0x00},
		{0x09, 0x00},
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (fff *FinalFingerprintFixer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (fff *FinalFingerprintFixer) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (fff *FinalFingerprintFixer) Close() {
	if fff.db != nil {
		fff.db.Close()
	}
}

func main() {
	config := &FinalFingerprintFixerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Final Fingerprint Fixer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	fixer, err := NewFinalFingerprintFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
