@echo off
REM WFS LevelDB Reader Build Script - Fixed Version
REM 修复版本构建脚本

echo ========================================
echo WFS LevelDB Reader Builder - Fixed
echo ========================================

REM 检查CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: CMake is not installed or not in PATH
    echo Please install CMake 3.20 or later
    pause
    exit /b 1
)

echo CMake found
cmake --version

REM 检查vcpkg
if not exist "C:\dev\vcpkg\vcpkg.exe" (
    echo Error: vcpkg not found at C:\dev\vcpkg\
    echo Please install vcpkg or update the path in CMakeLists.txt
    pause
    exit /b 1
)

echo vcpkg found

REM 检查必要的包
echo Checking required packages...
if not exist "C:\dev\vcpkg\installed\x64-windows\include\leveldb" (
    echo Installing leveldb...
    C:\dev\vcpkg\vcpkg.exe install leveldb:x64-windows
)

if not exist "C:\dev\vcpkg\installed\x64-windows\include\fmt" (
    echo Installing fmt...
    C:\dev\vcpkg\vcpkg.exe install fmt:x64-windows
)

REM 创建构建目录
if not exist "C:\VisualStudioRollback\CMakeBuild_Temp" (
    mkdir "C:\VisualStudioRollback\CMakeBuild_Temp"
)

set BUILD_DIR=C:\VisualStudioRollback\CMakeBuild_Temp\wfs_leveldb_reader_fixed
if exist "%BUILD_DIR%" (
    echo Cleaning previous build...
    rmdir /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

REM 创建发布目录
if not exist "redist_desk" (
    mkdir "redist_desk"
)

echo.
echo Configuring project...
cd "%BUILD_DIR%"
cmake "%~dp0" -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake -DCMAKE_BUILD_TYPE=Release

if errorlevel 1 (
    echo Error: CMake configuration failed
    echo.
    echo Possible solutions:
    echo 1. Make sure vcpkg packages are installed:
    echo    vcpkg install leveldb:x64-windows
    echo    vcpkg install fmt:x64-windows
    echo 2. Check CMake version (requires 3.20+)
    echo 3. Verify Visual Studio is installed
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Release

if errorlevel 1 (
    echo Error: Build failed
    echo.
    echo Build errors have been fixed:
    echo - Added missing header files (map, functional, cstring)
    echo - Fixed LevelDB cache API usage
    echo - Replaced OpenSSL MD5 with standard library hash
    echo - Removed protobuf dependency
    echo.
    echo If build still fails, check:
    echo 1. Visual Studio 2022 is installed
    echo 2. C++ development tools are installed
    echo 3. vcpkg packages are correctly installed
    pause
    exit /b 1
)

echo.
echo Installing...
cmake --install . --config Release

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Fixed issues:
echo ✓ Added missing header files
echo ✓ Fixed LevelDB API compatibility
echo ✓ Removed OpenSSL dependency
echo ✓ Simplified hash function
echo.
echo Executable location: %~dp0redist_desk\wfs_leveldb_reader.exe
echo.
echo Usage:
echo   wfs_leveldb_reader.exe ^<wfsdata_path^> [options]
echo.
echo Examples:
echo   wfs_leveldb_reader.exe C:\wfsdata
echo   wfs_leveldb_reader.exe C:\wfsdata -o extracted -v
echo.
echo Test with demo:
echo   demo_simple.bat C:\wfsdata
echo.
pause
