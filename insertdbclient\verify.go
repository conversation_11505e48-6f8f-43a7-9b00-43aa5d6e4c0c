// WFS Database Verification Tool
// 验证迁移后的数据库完整性和正确性

package main

import (
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE   = []byte{0x00, 0x00}
	PATH_SEQ   = []byte{0x01, 0x00}
	INDEX_0800 = []byte{0x08, 0x00}
)

// 验证器
type DatabaseVerifier struct {
	db     *leveldb.DB
	logger *log.Logger
}

// 创建验证器
func NewDatabaseVerifier(dbPath string) (*DatabaseVerifier, error) {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}

	return &DatabaseVerifier{
		db:     db,
		logger: log.New(os.Stdout, "[DBVerifier] ", log.LstdFlags),
	}, nil
}

// 关闭验证器
func (dv *DatabaseVerifier) Close() error {
	if dv.db != nil {
		return dv.db.Close()
	}
	return nil
}

// 解析protobuf格式的WfsPathBean
func (dv *DatabaseVerifier) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 计算文件路径的MD5指纹
func (dv *DatabaseVerifier) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

// 检查路径是否包含分隔符
func (dv *DatabaseVerifier) hasPathSeparators(path string) bool {
	return strings.Contains(path, "/") || strings.Contains(path, "\\")
}

// 验证数据库
func (dv *DatabaseVerifier) Verify() error {
	dv.logger.Println("=== Starting Database Verification ===")

	// 验证PATH_PRE索引
	pathPreCount, pathPreProblems := dv.verifyPATH_PRE()

	// 验证PATH_SEQ索引
	pathSeqCount, pathSeqProblems := dv.verifyPATH_SEQ()

	// 验证0x0800索引
	index0800Count, index0800Problems := dv.verifyIndex0800()

	// 验证索引一致性
	consistencyProblems := dv.verifyIndexConsistency()

	// 验证文件内容完整性
	contentProblems := dv.verifyFileContent()

	// 生成验证报告
	dv.generateVerificationReport(pathPreCount, pathPreProblems, pathSeqCount, pathSeqProblems,
		index0800Count, index0800Problems, consistencyProblems, contentProblems)

	return nil
}

// 验证PATH_PRE索引
func (dv *DatabaseVerifier) verifyPATH_PRE() (int, int) {
	dv.logger.Println("\n--- Verifying PATH_PRE Index ---")

	iter := dv.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	count := 0
	problems := 0

	for iter.Next() {
		key := iter.Key()
		_ = iter.Value() // value not needed for this check

		if len(key) <= len(PATH_PRE) {
			continue
		}

		path := string(key[len(PATH_PRE):])
		count++

		if dv.hasPathSeparators(path) {
			problems++
			dv.logger.Printf("❌ PATH_PRE problem: %s (contains path separators)", path)
		} else {
			dv.logger.Printf("✅ PATH_PRE OK: %s", path)
		}
	}

	dv.logger.Printf("PATH_PRE Index: %d entries, %d problems", count, problems)
	return count, problems
}

// 验证PATH_SEQ索引
func (dv *DatabaseVerifier) verifyPATH_SEQ() (int, int) {
	dv.logger.Println("\n--- Verifying PATH_SEQ Index ---")

	iter := dv.db.NewIterator(levelutil.BytesPrefix(PATH_SEQ), nil)
	defer iter.Release()

	count := 0
	problems := 0

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_SEQ) {
			continue
		}

		seqIDBytes := key[len(PATH_SEQ):]
		seqID := int64(binary.BigEndian.Uint64(seqIDBytes))
		count++

		path, timestamp, err := dv.parseWfsPathBean(value)
		if err != nil {
			problems++
			dv.logger.Printf("❌ PATH_SEQ parse error for seqID %d: %v", seqID, err)
			continue
		}

		if dv.hasPathSeparators(path) {
			problems++
			dv.logger.Printf("❌ PATH_SEQ problem: seqID %d, path %s (contains path separators)", seqID, path)
		} else {
			dv.logger.Printf("✅ PATH_SEQ OK: seqID %d, path %s, timestamp %d", seqID, path, timestamp)
		}
	}

	dv.logger.Printf("PATH_SEQ Index: %d entries, %d problems", count, problems)
	return count, problems
}

// 验证0x0800索引
func (dv *DatabaseVerifier) verifyIndex0800() (int, int) {
	dv.logger.Println("\n--- Verifying 0x0800 Index ---")

	iter := dv.db.NewIterator(levelutil.BytesPrefix(INDEX_0800), nil)
	defer iter.Release()

	count := 0
	problems := 0

	for iter.Next() {
		_ = iter.Key() // key not needed for this check
		value := iter.Value()

		count++

		path, timestamp, err := dv.parseWfsPathBean(value)
		if err != nil {
			problems++
			dv.logger.Printf("❌ 0x0800 parse error: %v", err)
			continue
		}

		if dv.hasPathSeparators(path) {
			problems++
			dv.logger.Printf("❌ 0x0800 problem: path %s (contains path separators)", path)
		} else {
			dv.logger.Printf("✅ 0x0800 OK: path %s, timestamp %d", path, timestamp)
		}
	}

	dv.logger.Printf("0x0800 Index: %d entries, %d problems", count, problems)
	return count, problems
}

// 验证索引一致性
func (dv *DatabaseVerifier) verifyIndexConsistency() int {
	dv.logger.Println("\n--- Verifying Index Consistency ---")

	// 获取所有PATH_PRE条目
	pathPreFiles := make(map[int64]string)
	iter := dv.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_PRE) {
			continue
		}

		path := string(key[len(PATH_PRE):])
		seqID := int64(binary.BigEndian.Uint64(value))
		pathPreFiles[seqID] = path
	}

	problems := 0

	// 检查PATH_SEQ一致性
	for seqID, pathPrePath := range pathPreFiles {
		seqIDBytes := make([]byte, 8)
		binary.BigEndian.PutUint64(seqIDBytes, uint64(seqID))
		pathSeqKey := append(PATH_SEQ, seqIDBytes...)

		if pathBeanData, err := dv.db.Get(pathSeqKey, nil); err == nil {
			if pathSeqPath, _, err := dv.parseWfsPathBean(pathBeanData); err == nil {
				if pathPrePath != pathSeqPath {
					problems++
					dv.logger.Printf("❌ Inconsistency: seqID %d, PATH_PRE: %s, PATH_SEQ: %s",
						seqID, pathPrePath, pathSeqPath)
				} else {
					dv.logger.Printf("✅ Consistent: seqID %d, path: %s", seqID, pathPrePath)
				}
			} else {
				problems++
				dv.logger.Printf("❌ PATH_SEQ parse error for seqID %d: %v", seqID, err)
			}
		} else {
			problems++
			dv.logger.Printf("❌ Missing PATH_SEQ for seqID %d", seqID)
		}
	}

	dv.logger.Printf("Index Consistency: %d problems found", problems)
	return problems
}

// 验证文件内容完整性
func (dv *DatabaseVerifier) verifyFileContent() int {
	dv.logger.Println("\n--- Verifying File Content Integrity ---")

	// 获取所有PATH_PRE条目
	pathPreFiles := make(map[int64]string)
	iter := dv.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_PRE) {
			continue
		}

		path := string(key[len(PATH_PRE):])
		seqID := int64(binary.BigEndian.Uint64(value))
		pathPreFiles[seqID] = path
	}

	problems := 0

	// 检查每个文件的内容完整性
	for seqID, fileName := range pathPreFiles {
		// 计算指纹
		fingerprint := dv.calculateFingerprint(fileName)

		// 检查指纹索引
		if contentID, err := dv.db.Get(fingerprint, nil); err == nil {
			// 检查文件内容
			if contentData, err := dv.db.Get(contentID, nil); err == nil {
				dv.logger.Printf("✅ Content OK: seqID %d, file %s, size %d bytes",
					seqID, fileName, len(contentData))
			} else {
				problems++
				dv.logger.Printf("❌ Missing content data: seqID %d, file %s, contentID %x",
					seqID, fileName, contentID)
			}
		} else {
			problems++
			dv.logger.Printf("❌ Missing fingerprint: seqID %d, file %s, fingerprint %x",
				seqID, fileName, fingerprint)
		}
	}

	dv.logger.Printf("File Content Integrity: %d problems found", problems)
	return problems
}

// 生成验证报告
func (dv *DatabaseVerifier) generateVerificationReport(pathPreCount, pathPreProblems,
	pathSeqCount, pathSeqProblems, index0800Count, index0800Problems,
	consistencyProblems, contentProblems int) {

	dv.logger.Println("\n=== Verification Report ===")
	dv.logger.Printf("PATH_PRE Index: %d entries, %d problems", pathPreCount, pathPreProblems)
	dv.logger.Printf("PATH_SEQ Index: %d entries, %d problems", pathSeqCount, pathSeqProblems)
	dv.logger.Printf("0x0800 Index: %d entries, %d problems", index0800Count, index0800Problems)
	dv.logger.Printf("Index Consistency: %d problems", consistencyProblems)
	dv.logger.Printf("File Content Integrity: %d problems", contentProblems)

	totalProblems := pathPreProblems + pathSeqProblems + index0800Problems + consistencyProblems + contentProblems

	if totalProblems == 0 {
		dv.logger.Println("✅ Database verification PASSED - No problems found!")
		dv.logger.Println("✅ All file names are correctly formatted")
		dv.logger.Println("✅ All indexes are consistent")
		dv.logger.Println("✅ All file content is accessible")
		dv.logger.Println("✅ WFS web interface should work correctly")
	} else {
		dv.logger.Printf("❌ Database verification FAILED - %d total problems found", totalProblems)
		dv.logger.Println("💡 Please review the problems above and fix them before using the database")
	}
}

// 主函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("WFS Database Verification Tool")
		fmt.Println("Usage: verify <db_path>")
		fmt.Println("")
		fmt.Println("This tool verifies the integrity and correctness of a WFS database")
		fmt.Println("after migration or repair operations.")
		os.Exit(1)
	}

	dbPath := os.Args[1]

	verifier, err := NewDatabaseVerifier(dbPath)
	if err != nil {
		log.Fatalf("Failed to create verifier: %v", err)
	}
	defer verifier.Close()

	if err := verifier.Verify(); err != nil {
		log.Fatalf("Verification failed: %v", err)
	}
}
