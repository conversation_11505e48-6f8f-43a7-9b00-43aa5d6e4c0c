// 修复指纹索引工具 - 根据新的文件路径重新计算指纹
package main

import (
	"crypto/md5"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type FingerprintFixerConfig struct {
	DatabasePath  string
	ReferencePath string
	DryRun        bool
	Verbose       bool
}

type FingerprintFixer struct {
	config *FingerprintFixerConfig
	db     *leveldb.DB
	refDB  *leveldb.DB
	logger *log.Logger
}

func NewFingerprintFixer(config *FingerprintFixerConfig) (*FingerprintFixer, error) {
	logger := log.New(os.Stdout, "[FingerprintFixer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open database normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	var refDB *leveldb.DB
	if config.ReferencePath != "" {
		refOptions := &opt.Options{
			Filter:                 filter.NewBloomFilter(10),
			OpenFilesCacheCapacity: 1 << 8,
			BlockCacheCapacity:     32 * 1024 * 1024,
			WriteBuffer:            8 * 1024 * 1024,
			ReadOnly:               true,
			ErrorIfMissing:         false,
			Strict:                 opt.NoStrict,
		}

		refDB, err = leveldb.OpenFile(config.ReferencePath, refOptions)
		if err != nil {
			logger.Printf("Failed to open reference database: %v", err)
		} else {
			logger.Printf("Successfully opened reference database")
		}
	}

	return &FingerprintFixer{
		config: config,
		db:     db,
		refDB:  refDB,
		logger: logger,
	}, nil
}

func (ff *FingerprintFixer) Fix() error {
	ff.logger.Println("=== Fixing Fingerprint Indexes ===")

	// 1. 收集当前数据库的文件和指纹映射
	currentFiles := ff.collectCurrentFiles()
	currentFingerprints := ff.collectCurrentFingerprints()

	// 2. 如果有参考数据库，分析参考数据库的指纹算法
	var refFingerprints map[string][]byte
	if ff.refDB != nil {
		refFiles := ff.collectReferenceFiles()
		refFingerprints = ff.collectReferenceFingerprints()
		ff.analyzeReferenceFingerprints(refFiles, refFingerprints)
	}

	// 3. 修复指纹索引
	return ff.fixFingerprints(currentFiles, currentFingerprints, refFingerprints)
}

func (ff *FingerprintFixer) collectCurrentFiles() map[string][]byte {
	ff.logger.Println("Collecting current files...")

	files := make(map[string][]byte)
	iter := ff.db.NewIterator(nil, nil)
	defer iter.Release()

	PATH_PRE := []byte{0x00, 0x00}
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				contentID := make([]byte, len(iter.Value()))
				copy(contentID, iter.Value())
				files[path] = contentID

				if ff.config.Verbose {
					ff.logger.Printf("Current file: %s → %s", path, hex.EncodeToString(contentID))
				}
			}
		}
	}

	ff.logger.Printf("Found %d current files", len(files))
	return files
}

func (ff *FingerprintFixer) collectCurrentFingerprints() map[string][]byte {
	ff.logger.Println("Collecting current fingerprints...")

	fingerprints := make(map[string][]byte)
	iter := ff.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if ff.isKnownIndex(key) {
			continue
		}

		// 检查是否可能是指纹索引
		if len(key) == 8 && len(value) == 8 {
			keyHex := hex.EncodeToString(key)
			contentID := make([]byte, len(value))
			copy(contentID, value)
			fingerprints[keyHex] = contentID
			count++

			if ff.config.Verbose {
				ff.logger.Printf("Current fingerprint: %s → %s", keyHex, hex.EncodeToString(contentID))
			}
		}
	}

	ff.logger.Printf("Found %d current fingerprints", count)
	return fingerprints
}

func (ff *FingerprintFixer) collectReferenceFiles() map[string][]byte {
	if ff.refDB == nil {
		return nil
	}

	ff.logger.Println("Collecting reference files...")

	files := make(map[string][]byte)
	iter := ff.refDB.NewIterator(nil, nil)
	defer iter.Release()

	PATH_PRE := []byte{0x00, 0x00}
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				contentID := make([]byte, len(iter.Value()))
				copy(contentID, iter.Value())
				files[path] = contentID

				if ff.config.Verbose {
					ff.logger.Printf("Reference file: %s → %s", path, hex.EncodeToString(contentID))
				}
			}
		}
	}

	ff.logger.Printf("Found %d reference files", len(files))
	return files
}

func (ff *FingerprintFixer) collectReferenceFingerprints() map[string][]byte {
	if ff.refDB == nil {
		return nil
	}

	ff.logger.Println("Collecting reference fingerprints...")

	fingerprints := make(map[string][]byte)
	iter := ff.refDB.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if ff.isKnownIndex(key) {
			continue
		}

		// 检查是否可能是指纹索引
		if len(key) == 8 && len(value) == 8 {
			keyHex := hex.EncodeToString(key)
			contentID := make([]byte, len(value))
			copy(contentID, value)
			fingerprints[keyHex] = contentID
			count++

			if ff.config.Verbose {
				ff.logger.Printf("Reference fingerprint: %s → %s", keyHex, hex.EncodeToString(contentID))
			}
		}
	}

	ff.logger.Printf("Found %d reference fingerprints", count)
	return fingerprints
}

func (ff *FingerprintFixer) analyzeReferenceFingerprints(refFiles map[string][]byte, refFingerprints map[string][]byte) {
	ff.logger.Println("Analyzing reference fingerprint algorithm...")

	// 尝试找出指纹算法
	for path, contentID := range refFiles {
		contentIDHex := hex.EncodeToString(contentID)

		// 查找指向此contentID的指纹
		for fingerprintHex, fpContentID := range refFingerprints {
			if hex.EncodeToString(fpContentID) == contentIDHex {
				ff.logger.Printf("Reference: %s → fingerprint %s → contentID %s", 
					path, fingerprintHex, contentIDHex)

				// 尝试验证指纹算法
				ff.tryFingerprintAlgorithms(path, fingerprintHex)
				break
			}
		}
	}
}

func (ff *FingerprintFixer) tryFingerprintAlgorithms(path, expectedFingerprint string) {
	// 尝试不同的指纹算法

	// 1. MD5前8字节
	md5Hash := md5.Sum([]byte(path))
	md5Front8 := hex.EncodeToString(md5Hash[:8])
	if md5Front8 == expectedFingerprint {
		ff.logger.Printf("  ✅ Found algorithm: MD5 front 8 bytes")
		return
	}

	// 2. MD5后8字节
	md5Back8 := hex.EncodeToString(md5Hash[8:])
	if md5Back8 == expectedFingerprint {
		ff.logger.Printf("  ✅ Found algorithm: MD5 back 8 bytes")
		return
	}

	// 3. 简单哈希
	simpleHash := ff.calculateSimpleHash(path)
	simpleHashHex := hex.EncodeToString(simpleHash)
	if simpleHashHex == expectedFingerprint {
		ff.logger.Printf("  ✅ Found algorithm: Simple hash")
		return
	}

	ff.logger.Printf("  ❌ Unknown fingerprint algorithm for %s", path)
}

func (ff *FingerprintFixer) calculateSimpleHash(s string) []byte {
	hash := make([]byte, 8)
	for i, b := range []byte(s) {
		hash[i%8] ^= b
	}
	return hash
}

func (ff *FingerprintFixer) fixFingerprints(currentFiles map[string][]byte, currentFingerprints map[string][]byte, refFingerprints map[string][]byte) error {
	ff.logger.Println("Fixing fingerprint indexes...")

	batch := new(leveldb.Batch)
	fixedCount := 0

	// 对于每个当前文件，计算正确的指纹并更新
	for path, contentID := range currentFiles {
		// 计算新的指纹（使用MD5前8字节，这是最常见的算法）
		md5Hash := md5.Sum([]byte(path))
		newFingerprint := md5Hash[:8]
		newFingerprintHex := hex.EncodeToString(newFingerprint)

		// 检查是否已经存在正确的指纹
		if existingContentID, exists := currentFingerprints[newFingerprintHex]; exists {
			if hex.EncodeToString(existingContentID) == hex.EncodeToString(contentID) {
				if ff.config.Verbose {
					ff.logger.Printf("Fingerprint already correct: %s → %s", path, newFingerprintHex)
				}
				continue
			}
		}

		// 删除旧的错误指纹
		for fingerprintHex, fpContentID := range currentFingerprints {
			if hex.EncodeToString(fpContentID) == hex.EncodeToString(contentID) {
				oldFingerprint, _ := hex.DecodeString(fingerprintHex)
				batch.Delete(oldFingerprint)
				if ff.config.Verbose {
					ff.logger.Printf("Deleting old fingerprint: %s", fingerprintHex)
				}
				break
			}
		}

		// 添加新的正确指纹
		batch.Put(newFingerprint, contentID)
		fixedCount++

		if ff.config.Verbose {
			ff.logger.Printf("Fixing fingerprint: %s → %s → %s", 
				path, newFingerprintHex, hex.EncodeToString(contentID))
		}
	}

	if fixedCount > 0 {
		if ff.config.DryRun {
			ff.logger.Printf("[DRY RUN] Would fix %d fingerprint indexes", fixedCount)
		} else {
			if err := ff.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			ff.logger.Printf("✅ Fixed %d fingerprint indexes", fixedCount)
		}
	} else {
		ff.logger.Println("✅ All fingerprint indexes are already correct")
	}

	return nil
}

func (ff *FingerprintFixer) isKnownIndex(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
		{0x06, 0x00}, // 其他已知前缀
		{0x07, 0x00},
		{0x09, 0x00},
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (ff *FingerprintFixer) Close() {
	if ff.db != nil {
		ff.db.Close()
	}
	if ff.refDB != nil {
		ff.refDB.Close()
	}
}

func main() {
	config := &FingerprintFixerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.StringVar(&config.ReferencePath, "ref", "", "Reference database path (optional)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Fingerprint Fixer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -ref C:\\wfsdata_new\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	fixer, err := NewFingerprintFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
