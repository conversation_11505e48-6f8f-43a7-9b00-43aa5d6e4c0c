// Copyright (c) 2023, donnie <<EMAIL>>
// All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.
//
// github.com/donnie4w/wfs
//

package tc

import (
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"runtime"
	"strings"

	goutil "github.com/donnie4w/gofer/util"
	"github.com/donnie4w/wfs/sys"
	"github.com/donnie4w/wfs/util"
)

func tlDebug() {
	defer util.Recover()
	if sys.DEBUGADDR != "" {
		runtime.SetMutexProfileFraction(1)
		runtime.SetBlockProfileRate(1)
		if !strings.Contains(sys.DEBUGADDR, ":") && goutil.MatchString("^[0-9]{4,5}$", sys.DEBUGADDR) {
			sys.DEBUGADDR = fmt.Sprint(":", sys.DEBUGADDR)
		}
		sys.FmtLog("Debug start[", sys.DEBUGADDR, "]")
		if err := http.ListenAndServe(sys.DEBUGADDR, nil); err != nil {
			sys.FmtLog("debug  start failed:" + err.Error())
		}
	}
}
