@echo off
echo ========================================
echo LevelDB Key Fixer Optimized Build
echo ========================================
echo.

echo Checking Go environment...
go version
if %errorlevel% neq 0 (
    echo Error: Go not found
    pause
    exit /b 1
)
echo.

echo Setting optimization flags...
set CGO_ENABLED=1
set GOOS=windows
set GOARCH=amd64
echo.

echo Downloading dependencies...
go mod tidy
echo.

echo Building optimized main program...
go build -trimpath -ldflags="-s -w" -tags="release" -o leveldb_key_fixer.exe leveldb_key_fixer.go
if %errorlevel% neq 0 (
    echo Error: Failed to build main program
    pause
    exit /b 1
)
echo Main program built successfully
echo.

echo Building optimized test program...
go build -trimpath -ldflags="-s -w" -tags="release" -o test_runner.exe test_runner.go
if %errorlevel% neq 0 (
    echo Error: Failed to build test program
    pause
    exit /b 1
)
echo Test program built successfully
echo.

echo ========================================
echo Build Completed Successfully!
echo ========================================
echo.

echo Generated files:
dir /b *.exe
echo.

echo File sizes:
for %%f in (*.exe) do (
    for %%s in (%%f) do echo   %%f: %%~zs bytes
)
echo.

echo Testing main program...
leveldb_key_fixer.exe >nul 2>&1
if %errorlevel% equ 1 (
    echo Main program test: OK
) else (
    echo Main program test: FAILED
)
echo.

echo Optimization features applied:
echo   - Dead code elimination
echo   - Path trimming
echo   - Release build tags
echo   - Symbol stripping
echo.

echo Usage:
echo   leveldb_key_fixer.exe database_path [options]
echo   test_runner.exe [perf count]
echo.

pause
