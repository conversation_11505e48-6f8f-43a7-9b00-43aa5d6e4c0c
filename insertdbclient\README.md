# WFS Database Migration Tool

高并发WFS数据库迁移工具，用于修复文件名路径问题。从原有数据库读取数据，修正文件名后写入新数据库。

## 🎯 功能特点

- ✅ **高并发处理**：支持多线程并发迁移，充分利用CPU资源
- ✅ **完整数据保持**：保持与原数据库相同的布局和属性
- ✅ **路径修正**：自动将带路径的文件名修正为纯文件名
- ✅ **五层索引重建**：完整重建WFS的所有索引结构
- ✅ **数据验证**：内置验证工具确保迁移质量
- ✅ **安全可靠**：支持dry-run模式，先预览再执行

## 📊 WFS存储架构

WFS系统使用五层存储结构：

```
1. PATH_PRE索引: 文件路径 → 序列号ID
2. PATH_SEQ索引: 序列号ID → WfsPathBean (protobuf格式)
3. 0x0800索引: 序列号ID → WfsPathBean (protobuf格式)
4. 指纹索引: fingerprint(文件路径) → 文件内容ID
5. 文件内容: 文件内容ID → 实际文件数据
```

## 🛠️ 编译和安装

### 前置要求
- Go 1.21 或更高版本
- Git

### 编译步骤
```bash
# 进入项目目录
cd insertdbclient

# 初始化Go模块
go mod init insertdbclient

# 下载依赖
go mod tidy

# 编译主程序
go build -o insertdbclient main.go

# 编译验证工具
go build -o verify verify.go
```

## 🚀 使用方法

### 基本语法
```bash
insertdbclient <source_db_path> <target_db_path> [options]
```

### 参数说明
- `source_db_path`: 源数据库路径（原有的wfsdb目录）
- `target_db_path`: 目标数据库路径（新的wfsdb目录）

### 选项参数
- `-workers <n>`: 工作线程数（默认：CPU核心数）
- `-batch <n>`: 批处理大小（默认：1000）
- `-buffer <n>`: 缓冲区大小（默认：10000）
- `-dry-run`: 干运行模式（只预览，不实际修改）
- `-create-dirs`: 创建目标目录结构

### 使用示例

#### 1. 预览迁移（推荐第一步）
```bash
# 预览迁移操作，不实际修改数据
./insertdbclient source/wfsdb target/wfsdb -dry-run
```

#### 2. 执行迁移
```bash
# 创建目标目录结构并执行迁移
./insertdbclient source/wfsdb target/wfsdb -create-dirs -workers 8

# 高性能迁移（适用于大数据量）
./insertdbclient source/wfsdb target/wfsdb -workers 16 -batch 2000 -buffer 20000
```

#### 3. 验证迁移结果
```bash
# 验证目标数据库的完整性
./verify target/wfsdb
```

## 📋 完整迁移流程

### 步骤1：准备工作
```bash
# 1. 停止WFS服务
sudo systemctl stop wfs

# 2. 备份原数据库
cp -r /path/to/original/wfsdata /path/to/backup/wfsdata_backup

# 3. 创建新的数据目录
mkdir -p /path/to/new/wfsdata
```

### 步骤2：执行迁移
```bash
# 1. 预览迁移
./insertdbclient /path/to/original/wfsdata/wfsdb /path/to/new/wfsdata/wfsdb -dry-run

# 2. 执行迁移
./insertdbclient /path/to/original/wfsdata/wfsdb /path/to/new/wfsdata/wfsdb -create-dirs -workers 8

# 3. 验证结果
./verify /path/to/new/wfsdata/wfsdb
```

### 步骤3：部署新数据库
```bash
# 1. 复制其他必要文件
cp -r /path/to/original/wfsdata/logs /path/to/new/wfsdata/
cp -r /path/to/original/wfsdata/wfsfile /path/to/new/wfsdata/

# 2. 更新WFS配置指向新数据库
# 编辑WFS配置文件，修改数据库路径

# 3. 启动WFS服务
sudo systemctl start wfs

# 4. 测试网页功能
# 访问WFS网页，验证文件列表显示正确
```

## 📊 性能优化

### 硬件建议
- **CPU**: 多核心处理器，推荐8核以上
- **内存**: 至少8GB，推荐16GB以上
- **存储**: SSD硬盘，提高I/O性能

### 参数调优
```bash
# 小数据量（< 1万文件）
./insertdbclient source target -workers 4 -batch 500

# 中等数据量（1万-10万文件）
./insertdbclient source target -workers 8 -batch 1000

# 大数据量（> 10万文件）
./insertdbclient source target -workers 16 -batch 2000 -buffer 20000
```

## 🔍 验证工具

### 验证内容
验证工具会检查以下内容：
- ✅ PATH_PRE索引：文件名格式正确性
- ✅ PATH_SEQ索引：protobuf格式和内容正确性
- ✅ 0x0800索引：数据格式和内容正确性
- ✅ 索引一致性：各索引间数据一致性
- ✅ 文件内容完整性：指纹索引和文件内容完整性

### 验证报告示例
```
=== Verification Report ===
PATH_PRE Index: 1000 entries, 0 problems
PATH_SEQ Index: 1000 entries, 0 problems
0x0800 Index: 1000 entries, 0 problems
Index Consistency: 0 problems
File Content Integrity: 0 problems
✅ Database verification PASSED - No problems found!
```

## ⚠️ 注意事项

### 安全提醒
1. **备份数据**：迁移前务必备份原数据库
2. **停止服务**：迁移期间停止WFS服务
3. **磁盘空间**：确保目标位置有足够磁盘空间
4. **权限检查**：确保有读写权限

### 故障排除
1. **内存不足**：减少worker数量和batch大小
2. **磁盘空间不足**：清理磁盘空间或选择其他位置
3. **权限错误**：使用sudo或调整文件权限
4. **数据损坏**：检查源数据库完整性

## 📞 技术支持

### 常见问题
1. **Q**: 迁移过程中断怎么办？
   **A**: 删除目标数据库，重新执行迁移

2. **Q**: 验证失败怎么办？
   **A**: 检查错误信息，重新执行迁移

3. **Q**: 性能太慢怎么办？
   **A**: 增加worker数量，使用SSD硬盘

### 日志分析
迁移工具会输出详细日志：
```
[DBMigrator] Progress: 500/1000 files, 500 success, 0 errors, 2.1 MB processed, elapsed: 30s
```

## 🎯 预期效果

迁移完成后：
- ✅ 网页显示正确的文件名（如 `image.jpg`）
- ✅ 不再显示带路径的文件名（如 `folder/image.jpg`）
- ✅ 文件删除功能正常工作
- ✅ 文件上传功能正常工作
- ✅ 文件下载功能正常工作

---

**工具版本**: v1.0  
**开发时间**: 2025年7月28日  
**适用范围**: WFS系统数据库迁移和修复
