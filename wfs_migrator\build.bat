@echo off
REM WFS High-Performance Migration Tool Build Script
REM 构建WFS高并发迁移工具

echo ========================================
echo WFS High-Performance Migration Tool Builder
echo ========================================

REM 检查Go环境
go version >nul 2>&1
if errorlevel 1 (
    echo Error: Go is not installed or not in PATH
    echo Please install Go 1.21 or later
    pause
    exit /b 1
)

echo Go environment detected
go version

REM 检查stub目录
if not exist "stub" (
    echo Error: stub directory not found
    echo Please copy the Thrift generated stub files to the stub directory
    echo Expected files: stub/wfs.go, stub/wfs-consts.go, etc.
    pause
    exit /b 1
)

echo Thrift stub files found

REM 初始化Go模块
echo.
echo Initializing Go module...
if not exist go.mod (
    go mod init wfs_migrator
)

REM 下载依赖
echo.
echo Downloading dependencies...
go mod tidy

REM 编译主程序
echo.
echo Building migration tool...
go build -trimpath -ldflags="-s -w" -o wfs_migrator.exe main.go
if errorlevel 1 (
    echo Error: Failed to build migration tool
    pause
    exit /b 1
)
echo ✅ wfs_migrator.exe built successfully

REM 显示构建结果
echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Built files:
dir /b *.exe
echo.
echo Usage:
echo   wfs_migrator.exe source_wfs_folder wfs_host:port [options]
echo.
echo Examples:
echo   wfs_migrator.exe C:\wfsdata localhost:9090 -dry-run
echo   wfs_migrator.exe C:\wfsdata localhost:9090 -workers 8 -skip-existing
echo.
echo For detailed usage, see README.md
echo.
pause
