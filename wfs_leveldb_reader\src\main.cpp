// WFS LevelDB Reader - 主程序
// 读取WFS LevelDB数据并提取文件内容到磁盘

#include <iostream>
#include <string>
#include <filesystem>
#include <chrono>
#include <fmt/format.h>
#include <fmt/color.h>

#include "leveldb_reader.hpp"
#include "file_extractor.hpp"
#include "datatype.hpp"

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

#include <iomanip>

using namespace wfs;
namespace fs = std::filesystem;

// 设置控制台UTF-8支持
void setup_console_utf8()
{
#ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
    _setmode(_fileno(stdout), _O_U8TEXT);
    _setmode(_fileno(stderr), _O_U8TEXT);
#endif
}

// 打印帮助信息
void print_help(const std::string &program_name)
{
    fmt::print(fg(fmt::color::cyan), "WFS LevelDB Reader - 读取WFS数据库并提取文件内容\n\n");

    fmt::print("用法: {} <wfsdata_path> [options]\n\n", program_name);

    fmt::print("参数:\n");
    fmt::print("  wfsdata_path    WFS数据目录路径（包含wfsdb子目录）\n\n");

    fmt::print("选项:\n");
    fmt::print("  -o, --output <dir>     输出目录（默认：extracted_files）\n");
    fmt::print("  -v, --verbose          详细输出\n");
    fmt::print("  -t, --test-content     创建测试内容（当文件内容缺失时）\n");
    fmt::print("  -h, --help             显示此帮助信息\n\n");

    fmt::print("示例:\n");
    fmt::print("  {} C:\\wfsdata\n", program_name);
    fmt::print("  {} C:\\wfsdata -o extracted -v\n", program_name);
    fmt::print("  {} /path/to/wfsdata --output /tmp/extracted --verbose\n", program_name);
}

// 解析命令行参数
ReaderConfig parse_arguments(int argc, char *argv[])
{
    ReaderConfig config;

    if (argc < 2)
    {
        print_help(argv[0]);
        std::exit(1);
    }

    config.wfsdata_path = argv[1];
    config.output_directory = "extracted_files";

    for (int i = 2; i < argc; ++i)
    {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help")
        {
            print_help(argv[0]);
            std::exit(0);
        }
        else if (arg == "-v" || arg == "--verbose")
        {
            config.verbose_logging = true;
        }
        else if (arg == "-t" || arg == "--test-content")
        {
            config.create_test_content = true;
        }
        else if ((arg == "-o" || arg == "--output") && i + 1 < argc)
        {
            config.output_directory = argv[++i];
        }
        else
        {
            fmt::print(stderr, fg(fmt::color::red), "未知参数: {}\n", arg);
            std::exit(1);
        }
    }

    return config;
}

// 验证输入路径
bool validate_paths(const ReaderConfig &config)
{
    // 检查wfsdata目录
    fs::path wfsdata_path(config.wfsdata_path);
    if (!fs::exists(wfsdata_path) || !fs::is_directory(wfsdata_path))
    {
        fmt::print(stderr, fg(fmt::color::red), "错误: WFS数据目录不存在: {}\n", config.wfsdata_path);
        return false;
    }

    // 检查wfsdb子目录
    fs::path wfsdb_path = wfsdata_path / "wfsdb";
    if (!fs::exists(wfsdb_path) || !fs::is_directory(wfsdb_path))
    {
        fmt::print(stderr, fg(fmt::color::red), "错误: wfsdb子目录不存在: {}\n", wfsdb_path.string());
        return false;
    }

    fmt::print(fg(fmt::color::green), "✓ 输入路径验证通过\n");
    return true;
}

// 进度回调函数
void progress_callback(size_t current, size_t total)
{
    if (total > 0)
    {
        double percentage = (double)current / total * 100.0;
        fmt::print("\r进度: {}/{} ({:.1f}%)", current, total, percentage);
        std::cout.flush();
    }
}

// 打印统计信息
void print_statistics(const DatabaseStats &stats)
{
    fmt::print("\n");
    fmt::print(fg(fmt::color::yellow), "=== 数据库统计信息 ===\n");
    fmt::print("总条目数: {}\n", stats.total_entries);
    fmt::print("PATH_PRE索引: {} 条目\n", stats.path_pre_entries);
    fmt::print("PATH_SEQ索引: {} 条目\n", stats.path_seq_entries);
    fmt::print("0x0800索引: {} 条目\n", stats.index_0800_entries);
    fmt::print("内容条目: {} 条目\n", stats.content_entries);
    fmt::print("提取文件数: {} 个\n", stats.extracted_files);
    fmt::print("总内容大小: {:.2f} MB\n", (double)stats.total_content_size / (1024 * 1024));
}

// 打印文件列表
void print_file_list(const std::map<int64_t, FileRecord> &records, bool verbose)
{
    fmt::print("\n");
    fmt::print(fg(fmt::color::yellow), "=== 发现的文件 ===\n");

    for (const auto &[seq_id, record] : records)
    {
        if (verbose)
        {
            fmt::print("SeqID: {}, 原始路径: {}, 文件名: {}, 大小: {} bytes\n",
                       seq_id, record.original_path, record.file_name, record.content_size);
        }
        else
        {
            fmt::print("{} -> {} ({} bytes)\n",
                       record.original_path, record.file_name, record.content_size);
        }
    }
}

int main(int argc, char *argv[])
{
    setup_console_utf8();

    try
    {
        // 解析命令行参数
        auto config = parse_arguments(argc, argv);

        // 验证路径
        if (!validate_paths(config))
        {
            return 1;
        }

        fmt::print(fg(fmt::color::cyan), "WFS LevelDB Reader 启动\n");
        fmt::print("输入目录: {}\n", config.wfsdata_path);
        fmt::print("输出目录: {}\n", config.output_directory);

        auto start_time = std::chrono::high_resolution_clock::now();

        // 创建LevelDB读取器
        auto reader = create_leveldb_reader();
        reader->set_progress_callback(progress_callback);

        // 打开数据库
        fs::path db_path = fs::path(config.wfsdata_path) / "wfsdb";
        fmt::print("\n正在打开数据库: {}\n", db_path.string());

        auto result = reader->open_database(db_path.string());
        if (result != ErrorCode::SUCCESS)
        {
            fmt::print(stderr, fg(fmt::color::red), "错误: 无法打开数据库\n");
            return 1;
        }

        fmt::print(fg(fmt::color::green), "✓ 数据库打开成功\n");

        // 扫描所有条目
        fmt::print("\n正在扫描数据库条目...\n");
        result = reader->scan_all_entries();
        if (result != ErrorCode::SUCCESS)
        {
            fmt::print(stderr, fg(fmt::color::red), "错误: 扫描数据库失败\n");
            return 1;
        }

        fmt::print("\n");
        fmt::print(fg(fmt::color::green), "✓ 数据库扫描完成\n");

        // 获取统计信息和文件记录
        const auto &stats = reader->get_stats();
        const auto &records = reader->get_file_records();

        // 打印统计信息
        print_statistics(stats);

        // 打印文件列表
        if (!records.empty())
        {
            print_file_list(records, config.verbose_logging);

            // 提取文件到磁盘
            if (config.extract_files)
            {
                fmt::print("\n正在提取文件到磁盘...\n");

                auto extractor = create_file_extractor();
                result = extractor->set_output_directory(config.output_directory);
                if (result != ErrorCode::SUCCESS)
                {
                    fmt::print(stderr, fg(fmt::color::red), "错误: 无法创建输出目录\n");
                    return 1;
                }

                result = extractor->extract_files(records);
                if (result != ErrorCode::SUCCESS)
                {
                    fmt::print(stderr, fg(fmt::color::red), "错误: 文件提取失败\n");
                    return 1;
                }

                fmt::print(fg(fmt::color::green), "✓ 文件提取完成\n");
                fmt::print("提取文件数: {}\n", extractor->get_extracted_count());
                fmt::print("总提取大小: {:.2f} MB\n",
                           (double)extractor->get_total_extracted_size() / (1024 * 1024));
            }
        }
        else
        {
            fmt::print(fg(fmt::color::yellow), "⚠ 未发现任何文件\n");
        }

        // 计算总耗时
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        fmt::print("\n");
        fmt::print(fg(fmt::color::green), "✓ 处理完成，耗时: {} ms\n", duration.count());

        reader->close_database();
    }
    catch (const std::exception &e)
    {
        fmt::print(stderr, fg(fmt::color::red), "异常: {}\n", e.what());
        return 1;
    }

    return 0;
}
