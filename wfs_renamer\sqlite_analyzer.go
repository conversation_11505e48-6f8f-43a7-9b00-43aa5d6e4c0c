// SQLite分析工具 - 分析扫描结果
package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"os"

	_ "github.com/mattn/go-sqlite3"
)

type SQLiteAnalyzerConfig struct {
	DatabasePath string
	Verbose      bool
}

type SQLiteAnalyzer struct {
	config *SQLiteAnalyzerConfig
	db     *sql.DB
	logger *log.Logger
}

func NewSQLiteAnalyzer(config *SQLiteAnalyzerConfig) (*SQLiteAnalyzer, error) {
	logger := log.New(os.Stdout, "[SQLiteAnalyzer] ", log.LstdFlags)

	db, err := sql.Open("sqlite3", config.DatabasePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open SQLite database: %v", err)
	}

	return &SQLiteAnalyzer{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (sa *SQLiteAnalyzer) Analyze() error {
	sa.logger.Println("=== SQLite Database Analysis ===")

	// 1. 统计各类型条目
	if err := sa.analyzeKeyTypes(); err != nil {
		return err
	}

	// 2. 分析文件stem
	if err := sa.analyzeFileStems(); err != nil {
		return err
	}

	// 3. 分析PATH_PRE条目
	if err := sa.analyzePATH_PRE(); err != nil {
		return err
	}

	// 4. 分析PATH_SEQ条目
	if err := sa.analyzePATH_SEQ(); err != nil {
		return err
	}

	// 5. 分析指纹索引
	if err := sa.analyzeFingerprints(); err != nil {
		return err
	}

	// 6. 分析WfsFileBean
	if err := sa.analyzeWfsFileBeans(); err != nil {
		return err
	}

	return nil
}

func (sa *SQLiteAnalyzer) analyzeKeyTypes() error {
	sa.logger.Println("\n--- Key Types Analysis ---")

	query := `
	SELECT key_type, COUNT(*) as count 
	FROM wfs_keys 
	GROUP BY key_type 
	ORDER BY count DESC
	`

	rows, err := sa.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var keyType string
		var count int
		if err := rows.Scan(&keyType, &count); err != nil {
			return err
		}
		sa.logger.Printf("%-15s: %d entries", keyType, count)
	}

	return nil
}

func (sa *SQLiteAnalyzer) analyzeFileStems() error {
	sa.logger.Println("\n--- File Stems Analysis ---")

	query := `
	SELECT file_stem, COUNT(*) as count, GROUP_CONCAT(key_type) as key_types
	FROM wfs_keys 
	WHERE file_stem IS NOT NULL AND file_stem != ''
	GROUP BY file_stem 
	ORDER BY count DESC
	`

	rows, err := sa.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var fileStem string
		var count int
		var keyTypes string
		if err := rows.Scan(&fileStem, &count, &keyTypes); err != nil {
			return err
		}
		sa.logger.Printf("%-10s: %d entries (%s)", fileStem, count, keyTypes)
	}

	return nil
}

func (sa *SQLiteAnalyzer) analyzePATH_PRE() error {
	sa.logger.Println("\n--- PATH_PRE Analysis ---")

	query := `
	SELECT seq_id, file_stem, description
	FROM wfs_keys 
	WHERE key_type = 'PATH_PRE'
	ORDER BY seq_id
	`

	rows, err := sa.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var seqID int64
		var fileStem sql.NullString
		var description string
		if err := rows.Scan(&seqID, &fileStem, &description); err != nil {
			return err
		}
		stem := ""
		if fileStem.Valid {
			stem = fileStem.String
		}
		sa.logger.Printf("seqID=%d, stem=%s, desc=%s", seqID, stem, description)
	}

	return nil
}

func (sa *SQLiteAnalyzer) analyzePATH_SEQ() error {
	sa.logger.Println("\n--- PATH_SEQ Analysis ---")

	query := `
	SELECT seq_id, file_stem, description
	FROM wfs_keys 
	WHERE key_type = 'PATH_SEQ'
	ORDER BY seq_id
	`

	rows, err := sa.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var seqID int64
		var fileStem sql.NullString
		var description string
		if err := rows.Scan(&seqID, &fileStem, &description); err != nil {
			return err
		}
		stem := ""
		if fileStem.Valid {
			stem = fileStem.String
		}
		sa.logger.Printf("seqID=%d, stem=%s, desc=%s", seqID, stem, description)
	}

	return nil
}

func (sa *SQLiteAnalyzer) analyzeFingerprints() error {
	sa.logger.Println("\n--- Fingerprint Analysis ---")

	query := `
	SELECT key_hex, value_hex, description
	FROM wfs_keys 
	WHERE key_type = 'FINGERPRINT'
	ORDER BY key_hex
	`

	rows, err := sa.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var keyHex, valueHex, description string
		if err := rows.Scan(&keyHex, &valueHex, &description); err != nil {
			return err
		}
		sa.logger.Printf("Key=%s, Value=%s, Desc=%s", keyHex, valueHex, description)
	}

	return nil
}

func (sa *SQLiteAnalyzer) analyzeWfsFileBeans() error {
	sa.logger.Println("\n--- WfsFileBean Analysis ---")

	query := `
	SELECT key_hex, value_length, description
	FROM wfs_keys 
	WHERE key_type = 'WFS_FILE_BEAN'
	ORDER BY key_hex
	`

	rows, err := sa.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var keyHex, description string
		var valueLength int
		if err := rows.Scan(&keyHex, &valueLength, &description); err != nil {
			return err
		}
		sa.logger.Printf("ContentID=%s, Size=%d, Desc=%s", keyHex, valueLength, description)
	}

	return nil
}

func (sa *SQLiteAnalyzer) Close() {
	if sa.db != nil {
		sa.db.Close()
	}
}

func main() {
	config := &SQLiteAnalyzerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "wfs_scan_results.sqlite", "SQLite database path")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "SQLite Analyzer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db wfs_scan_results.sqlite -verbose\n", os.Args[0])
	}

	flag.Parse()

	analyzer, err := NewSQLiteAnalyzer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer analyzer.Close()

	if err := analyzer.Analyze(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
