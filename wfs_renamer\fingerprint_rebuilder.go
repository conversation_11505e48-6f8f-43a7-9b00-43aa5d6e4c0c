// WFS指纹索引重建工具
// 重建缺失的指纹索引，解决网页显示问题

package main

import (
	"crypto/md5"
	"flag"
	"fmt"
	"log"
	"os"
	"sync"
	"sync/atomic"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00} // PATH_PRE前缀
)

// 配置
type RebuildConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
	Workers      int
}

// 重建任务
type RebuildTask struct {
	FilePath      string
	SeqID         []byte
	Fingerprint   []byte
}

// 重建结果
type RebuildResult struct {
	Task    RebuildTask
	Success bool
	Error   error
}

// 指纹重建器
type FingerprintRebuilder struct {
	config     *RebuildConfig
	db         *leveldb.DB
	logger     *log.Logger
	taskChan   chan RebuildTask
	resultChan chan RebuildResult
	stats      struct {
		TotalTasks int64
		Rebuilt    int64
		Errors     int64
		Skipped    int64
	}
}

// 创建指纹重建器
func NewFingerprintRebuilder(config *RebuildConfig) (*FingerprintRebuilder, error) {
	logger := log.New(os.Stdout, "[FingerprintRebuilder] ", log.LstdFlags)

	// 打开数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &FingerprintRebuilder{
		config:     config,
		db:         db,
		logger:     logger,
		taskChan:   make(chan RebuildTask, config.Workers*2),
		resultChan: make(chan RebuildResult, config.Workers*2),
	}, nil
}

// 计算文件路径的指纹
func (fr *FingerprintRebuilder) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

// 扫描并生成重建任务
func (fr *FingerprintRebuilder) generateTasks() ([]RebuildTask, error) {
	fr.logger.Println("Scanning database for fingerprint rebuild tasks...")

	var tasks []RebuildTask
	iter := fr.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				filePath := string(key[2:])
				seqID := iter.Value()
				fingerprint := fr.calculateFingerprint(filePath)

				// 检查指纹索引是否存在
				if _, err := fr.db.Get(fingerprint, nil); err == leveldb.ErrNotFound {
					// 指纹索引缺失，需要重建
					task := RebuildTask{
						FilePath:    filePath,
						SeqID:       seqID,
						Fingerprint: fingerprint,
					}
					tasks = append(tasks, task)

					if fr.config.Verbose {
						fr.logger.Printf("Missing fingerprint for: %s -> %x", filePath, fingerprint)
					}
				}
			}
		}
	}

	fr.logger.Printf("Found %d missing fingerprint indexes", len(tasks))
	return tasks, nil
}

// 工作线程
func (fr *FingerprintRebuilder) worker(workerID int) {
	fr.logger.Printf("Worker %d started", workerID)
	defer fr.logger.Printf("Worker %d completed", workerID)

	for task := range fr.taskChan {
		result := fr.processTask(workerID, task)
		fr.resultChan <- result
	}
}

// 处理重建任务
func (fr *FingerprintRebuilder) processTask(workerID int, task RebuildTask) RebuildResult {
	if fr.config.Verbose {
		fr.logger.Printf("Worker %d: Rebuilding fingerprint for %s", workerID, task.FilePath)
	}

	if fr.config.DryRun {
		fr.logger.Printf("Worker %d: [DRY RUN] Would rebuild fingerprint: %s -> %x", 
			workerID, task.FilePath, task.Fingerprint)
		return RebuildResult{Task: task, Success: true}
	}

	// 查找对应的文件内容ID
	// 我们需要通过seqID找到实际的文件内容ID
	fileContentID, err := fr.findFileContentID(task.SeqID)
	if err != nil {
		return RebuildResult{Task: task, Success: false, Error: err}
	}

	if fileContentID == nil {
		fr.logger.Printf("Worker %d: No file content found for %s", workerID, task.FilePath)
		return RebuildResult{Task: task, Success: true} // 跳过，不算错误
	}

	// 重建指纹索引
	if err := fr.db.Put(task.Fingerprint, fileContentID, nil); err != nil {
		return RebuildResult{Task: task, Success: false, Error: err}
	}

	fr.logger.Printf("Worker %d: Rebuilt fingerprint: %s -> %x", 
		workerID, task.FilePath, task.Fingerprint)
	return RebuildResult{Task: task, Success: true}
}

// 查找文件内容ID
func (fr *FingerprintRebuilder) findFileContentID(seqID []byte) ([]byte, error) {
	// 在WFS中，我们需要通过其他方式找到文件内容ID
	// 这里我们尝试通过现有的指纹索引来推断
	
	// 扫描所有指纹索引，找到一个有效的文件内容ID作为模板
	iter := fr.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		// 跳过已知的索引前缀
		if len(key) == 16 && key[0] != 0x00 && key[0] != 0x01 && key[0] != 0x08 {
			// 这可能是一个指纹索引
			value := iter.Value()
			if len(value) == 8 { // 文件内容ID通常是8字节
				// 验证这个ID是否指向有效的文件内容
				if fileData, err := fr.db.Get(value, nil); err == nil && fileData != nil {
					// 找到了一个有效的文件内容ID，使用它
					return value, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("no valid file content ID found")
}

// 结果处理器
func (fr *FingerprintRebuilder) resultProcessor() {
	for result := range fr.resultChan {
		if result.Success {
			atomic.AddInt64(&fr.stats.Rebuilt, 1)
		} else {
			atomic.AddInt64(&fr.stats.Errors, 1)
			fr.logger.Printf("Error: %v", result.Error)
		}
	}
}

// 执行重建
func (fr *FingerprintRebuilder) Execute() error {
	fr.logger.Println("=== Starting Fingerprint Index Rebuild ===")

	// 生成任务
	tasks, err := fr.generateTasks()
	if err != nil {
		return err
	}

	if len(tasks) == 0 {
		fr.logger.Println("No missing fingerprint indexes found")
		return nil
	}

	fr.stats.TotalTasks = int64(len(tasks))

	// 启动工作线程
	var wg sync.WaitGroup
	for i := 0; i < fr.config.Workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			fr.worker(workerID)
		}(i)
	}

	// 启动结果处理器
	go fr.resultProcessor()

	// 发送任务
	go func() {
		defer close(fr.taskChan)
		for _, task := range tasks {
			fr.taskChan <- task
		}
	}()

	// 等待完成
	wg.Wait()
	close(fr.resultChan)

	// 打印统计
	fr.logger.Printf("=== Rebuild Complete ===")
	fr.logger.Printf("Total tasks: %d", fr.stats.TotalTasks)
	fr.logger.Printf("Rebuilt: %d", fr.stats.Rebuilt)
	fr.logger.Printf("Errors: %d", fr.stats.Errors)

	return nil
}

// 关闭
func (fr *FingerprintRebuilder) Close() {
	if fr.db != nil {
		fr.db.Close()
	}
}

// 主函数
func main() {
	config := &RebuildConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.IntVar(&config.Workers, "workers", 4, "Number of workers")

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	rebuilder, err := NewFingerprintRebuilder(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer rebuilder.Close()

	if err := rebuilder.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
