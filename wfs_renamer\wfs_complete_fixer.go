// WFS完整修复工具 - 重建所有缺失的索引
package main

import (
	"crypto/md5"
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00}
	PATH_SEQ = []byte{0x01, 0x00}
)

type FixerConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
	RenameFiles  bool
}

type FileInfo struct {
	OriginalPath string
	CleanPath    string
	SeqID        []byte
	SeqIDInt     int64
	Timestamp    int64
	NeedsRename  bool
}

type WFSCompleteFixer struct {
	config *FixerConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewWFSCompleteFixer(config *FixerConfig) (*WFSCompleteFixer, error) {
	logger := log.New(os.Stdout, "[WFSCompleteFixer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &WFSCompleteFixer{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (wcf *WFSCompleteFixer) Fix() error {
	wcf.logger.Println("=== WFS Complete Fix ===")

	// 1. 收集所有文件信息
	files, err := wcf.collectFileInfo()
	if err != nil {
		return fmt.Errorf("failed to collect file info: %v", err)
	}

	wcf.logger.Printf("Found %d files to process", len(files))

	// 2. 执行修复
	if wcf.config.DryRun {
		return wcf.previewFix(files)
	} else {
		return wcf.executeFix(files)
	}
}

func (wcf *WFSCompleteFixer) collectFileInfo() ([]FileInfo, error) {
	wcf.logger.Println("Collecting file information...")

	var files []FileInfo
	timestampMap := make(map[string]int64)

	// 从0x0800索引获取时间戳
	prefix0800 := []byte{0x08, 0x00}
	iter := wcf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		value := iter.Value()
		if path, timestamp, err := wcf.parseWfsPathBean(value); err == nil {
			timestampMap[path] = timestamp
		}
	}

	// 扫描PATH_PRE索引
	iter = wcf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])
				seqID := make([]byte, len(iter.Value()))
				copy(seqID, iter.Value())

				cleanPath := wcf.cleanFileName(originalPath)
				needsRename := originalPath != cleanPath

				fileInfo := FileInfo{
					OriginalPath: originalPath,
					CleanPath:    cleanPath,
					SeqID:        seqID,
					SeqIDInt:     wcf.bytesToInt64(seqID),
					Timestamp:    timestampMap[originalPath],
					NeedsRename:  needsRename,
				}

				if fileInfo.Timestamp == 0 {
					fileInfo.Timestamp = time.Now().UnixNano()
				}

				files = append(files, fileInfo)

				if wcf.config.Verbose {
					wcf.logger.Printf("File: %s -> %s, seqID=%d, needsRename=%v",
						originalPath, cleanPath, fileInfo.SeqIDInt, needsRename)
				}
			}
		}
	}

	return files, nil
}

func (wcf *WFSCompleteFixer) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	parts := strings.Split(path, "/")
	return parts[len(parts)-1]
}

func (wcf *WFSCompleteFixer) previewFix(files []FileInfo) error {
	wcf.logger.Println("=== Preview Mode ===")

	renameCount := 0
	pathSeqCount := 0
	fingerprintCount := 0

	for _, file := range files {
		if file.NeedsRename && wcf.config.RenameFiles {
			wcf.logger.Printf("[DRY RUN] Would rename: %s -> %s", file.OriginalPath, file.CleanPath)
			renameCount++
		}

		// 检查PATH_SEQ
		pathSeqKey := append(PATH_SEQ, file.SeqID...)
		if _, err := wcf.db.Get(pathSeqKey, nil); err == leveldb.ErrNotFound {
			targetPath := file.OriginalPath
			if file.NeedsRename && wcf.config.RenameFiles {
				targetPath = file.CleanPath
			}
			wcf.logger.Printf("[DRY RUN] Would create PATH_SEQ: seqID %d -> %s", file.SeqIDInt, targetPath)
			pathSeqCount++
		}

		// 检查指纹索引
		targetPath := file.OriginalPath
		if file.NeedsRename && wcf.config.RenameFiles {
			targetPath = file.CleanPath
		}
		fingerprint := wcf.calculateFingerprint(targetPath)
		if _, err := wcf.db.Get(fingerprint, nil); err == leveldb.ErrNotFound {
			wcf.logger.Printf("[DRY RUN] Would create fingerprint: %s -> %s", targetPath, hex.EncodeToString(fingerprint))
			fingerprintCount++
		}
	}

	wcf.logger.Printf("Summary: %d renames, %d PATH_SEQ, %d fingerprints", renameCount, pathSeqCount, fingerprintCount)
	return nil
}

func (wcf *WFSCompleteFixer) executeFix(files []FileInfo) error {
	wcf.logger.Println("=== Executing Fix ===")

	batch := new(leveldb.Batch)
	operations := 0

	for _, file := range files {
		if file.NeedsRename && wcf.config.RenameFiles {
			// 执行重命名
			if err := wcf.addRenameOperations(batch, file); err != nil {
				wcf.logger.Printf("Error adding rename operations for %s: %v", file.OriginalPath, err)
				continue
			}
			operations++
		}

		// 确保PATH_SEQ存在
		if err := wcf.ensurePATH_SEQ(batch, file); err != nil {
			wcf.logger.Printf("Error ensuring PATH_SEQ for %s: %v", file.OriginalPath, err)
			continue
		}

		// 确保指纹索引存在
		if err := wcf.ensureFingerprint(batch, file); err != nil {
			wcf.logger.Printf("Error ensuring fingerprint for %s: %v", file.OriginalPath, err)
			continue
		}
	}

	if operations > 0 || true { // 总是执行，因为可能需要创建索引
		wcf.logger.Printf("Executing batch operation with %d operations...", operations)
		if err := wcf.db.Write(batch, nil); err != nil {
			return fmt.Errorf("failed to write batch: %v", err)
		}
		wcf.logger.Println("✅ Batch operation completed successfully")
	}

	return nil
}

func (wcf *WFSCompleteFixer) addRenameOperations(batch *leveldb.Batch, file FileInfo) error {
	// 删除旧的PATH_PRE
	oldPathPreKey := append(PATH_PRE, []byte(file.OriginalPath)...)
	batch.Delete(oldPathPreKey)

	// 添加新的PATH_PRE
	newPathPreKey := append(PATH_PRE, []byte(file.CleanPath)...)
	batch.Put(newPathPreKey, file.SeqID)

	// 删除旧的指纹
	oldFingerprint := wcf.calculateFingerprint(file.OriginalPath)
	batch.Delete(oldFingerprint)

	if wcf.config.Verbose {
		wcf.logger.Printf("Added rename operations: %s -> %s", file.OriginalPath, file.CleanPath)
	}

	return nil
}

func (wcf *WFSCompleteFixer) ensurePATH_SEQ(batch *leveldb.Batch, file FileInfo) error {
	pathSeqKey := append(PATH_SEQ, file.SeqID...)

	// 检查是否已存在
	if _, err := wcf.db.Get(pathSeqKey, nil); err == leveldb.ErrNotFound {
		// 不存在，创建新的
		targetPath := file.OriginalPath
		if file.NeedsRename && wcf.config.RenameFiles {
			targetPath = file.CleanPath
		}

		pathBeanData := wcf.encodeWfsPathBean(targetPath, file.Timestamp)
		batch.Put(pathSeqKey, pathBeanData)

		if wcf.config.Verbose {
			wcf.logger.Printf("Creating PATH_SEQ: seqID %d -> %s", file.SeqIDInt, targetPath)
		}
	}

	return nil
}

func (wcf *WFSCompleteFixer) ensureFingerprint(batch *leveldb.Batch, file FileInfo) error {
	targetPath := file.OriginalPath
	if file.NeedsRename && wcf.config.RenameFiles {
		targetPath = file.CleanPath
	}

	fingerprint := wcf.calculateFingerprint(targetPath)

	// 检查是否已存在
	if _, err := wcf.db.Get(fingerprint, nil); err == leveldb.ErrNotFound {
		// 不存在，创建新的
		batch.Put(fingerprint, file.SeqID) // 使用seqID作为内容ID

		if wcf.config.Verbose {
			wcf.logger.Printf("Creating fingerprint: %s -> %s", targetPath, hex.EncodeToString(fingerprint))
		}
	}

	return nil
}

// 辅助函数
func (wcf *WFSCompleteFixer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wcf *WFSCompleteFixer) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func (wcf *WFSCompleteFixer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// Path字段 (field 1, wire type 2)
	if path != "" {
		result = append(result, 0x0A)
		pathBytes := []byte(path)
		result = append(result, byte(len(pathBytes)))
		result = append(result, pathBytes...)
	}

	// Timestamp字段 (field 2, wire type 0)
	if timestamp != 0 {
		result = append(result, 0x10)
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

func (wcf *WFSCompleteFixer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wcf *WFSCompleteFixer) Close() {
	if wcf.db != nil {
		wcf.db.Close()
	}
}

func main() {
	config := &FixerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.BoolVar(&config.RenameFiles, "rename", true, "Rename files to clean names")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Complete Fixer - Fix all WFS index issues\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	fixer, err := NewWFSCompleteFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
