// WFS完整集成修复工具 - 一键修复文件名路径前缀问题
// 支持高并发，集成所有修复步骤
package main

import (
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_COMPLETE   = []byte{0x00, 0x00}
	PATH_SEQ_COMPLETE   = []byte{0x01, 0x00}
	INDEX_0800_COMPLETE = []byte{0x08, 0x00}
)

type CompleteFixerConfig struct {
	DatabasePath  string
	ReferencePath string
	DryRun        bool
	Verbose       bool
	Workers       int // 并发工作线程数
}

type CompleteFixer struct {
	config   *CompleteFixerConfig
	db       *leveldb.DB
	refDB    *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
	mu       sync.RWMutex
}

type FileInfo struct {
	Path      string
	SeqID     int64
	CleanPath string
}

type FixResult struct {
	Step     string
	Success  bool
	Count    int
	Error    error
	Duration time.Duration
}

func NewCompleteFixer(config *CompleteFixerConfig) (*CompleteFixer, error) {
	logger := log.New(os.Stdout, "[WFSCompleteFixer] ", log.LstdFlags)

	// 数据库选项
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	// 打开主数据库
	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open database normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	// 打开参考数据库（如果提供）
	var refDB *leveldb.DB
	if config.ReferencePath != "" {
		refOptions := &opt.Options{
			Filter:                 filter.NewBloomFilter(10),
			OpenFilesCacheCapacity: 1 << 8,
			BlockCacheCapacity:     32 * 1024 * 1024,
			WriteBuffer:            8 * 1024 * 1024,
			ReadOnly:               true,
			ErrorIfMissing:         false,
			Strict:                 opt.NoStrict,
		}

		refDB, err = leveldb.OpenFile(config.ReferencePath, refOptions)
		if err != nil {
			logger.Printf("Warning: Failed to open reference database: %v", err)
		} else {
			logger.Printf("Successfully opened reference database")
		}
	}

	// 创建CRC64表
	crcTable := crc64.MakeTable(crc64.ISO)

	return &CompleteFixer{
		config:   config,
		db:       db,
		refDB:    refDB,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (cf *CompleteFixer) Fix() error {
	cf.logger.Println("=== WFS Complete Fix - File Path Cleanup ===")
	cf.logger.Printf("Workers: %d", cf.config.Workers)
	cf.logger.Printf("Database: %s", cf.config.DatabasePath)
	if cf.config.ReferencePath != "" {
		cf.logger.Printf("Reference: %s", cf.config.ReferencePath)
	}

	startTime := time.Now()
	results := make([]FixResult, 0)

	// 步骤1: 收集所有文件信息
	cf.logger.Println("\n🔍 Step 1: Collecting file information...")
	stepStart := time.Now()
	files, err := cf.collectFiles()
	if err != nil {
		return fmt.Errorf("step 1 failed: %v", err)
	}
	results = append(results, FixResult{
		Step:     "Collect Files",
		Success:  true,
		Count:    len(files),
		Duration: time.Since(stepStart),
	})

	// 步骤2: 修复PATH_PRE索引
	cf.logger.Println("\n🔧 Step 2: Fixing PATH_PRE index...")
	stepStart = time.Now()
	count, err := cf.fixPATH_PRE(files)
	results = append(results, FixResult{
		Step:     "Fix PATH_PRE",
		Success:  err == nil,
		Count:    count,
		Error:    err,
		Duration: time.Since(stepStart),
	})
	if err != nil {
		return fmt.Errorf("step 2 failed: %v", err)
	}

	// 步骤3: 修复0x0800索引
	cf.logger.Println("\n🔧 Step 3: Fixing 0x0800 index...")
	stepStart = time.Now()
	count, err = cf.fix0x0800(files)
	results = append(results, FixResult{
		Step:     "Fix 0x0800",
		Success:  err == nil,
		Count:    count,
		Error:    err,
		Duration: time.Since(stepStart),
	})
	if err != nil {
		return fmt.Errorf("step 3 failed: %v", err)
	}

	// 步骤4: 修复指纹索引和WfsFileBean关联
	cf.logger.Println("\n🔧 Step 4: Fixing fingerprint and WfsFileBean associations...")
	stepStart = time.Now()
	count, err = cf.fixFingerprintAssociations(files)
	results = append(results, FixResult{
		Step:     "Fix Fingerprint Associations",
		Success:  err == nil,
		Count:    count,
		Error:    err,
		Duration: time.Since(stepStart),
	})
	if err != nil {
		return fmt.Errorf("step 4 failed: %v", err)
	}

	// 步骤5: 重建PATH_SEQ索引
	cf.logger.Println("\n🔧 Step 5: Rebuilding PATH_SEQ index...")
	stepStart = time.Now()
	count, err = cf.rebuildPATH_SEQ()
	results = append(results, FixResult{
		Step:     "Rebuild PATH_SEQ",
		Success:  err == nil,
		Count:    count,
		Error:    err,
		Duration: time.Since(stepStart),
	})
	if err != nil {
		return fmt.Errorf("step 5 failed: %v", err)
	}

	// 打印总结
	cf.printSummary(results, time.Since(startTime))

	return nil
}

// collectFiles 收集所有文件信息
func (cf *CompleteFixer) collectFiles() (map[int64]*FileInfo, error) {
	cf.logger.Println("Collecting files from PATH_PRE index...")

	files := make(map[int64]*FileInfo)
	iter := cf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_COMPLETE[0] && key[1] == PATH_PRE_COMPLETE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := cf.bytesToInt64(seqIDBytes)

				cleanPath := cf.cleanFileName(path)
				needsClean := path != cleanPath

				files[seqID] = &FileInfo{
					Path:      path,
					SeqID:     seqID,
					CleanPath: cleanPath,
				}

				if cf.config.Verbose && needsClean {
					cf.logger.Printf("File needs cleaning: seqID=%d, %s -> %s", seqID, path, cleanPath)
				}
			}
		}
	}

	cf.logger.Printf("Found %d files", len(files))
	return files, nil
}

// cleanFileName 清理文件名，移除路径前缀
func (cf *CompleteFixer) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	return filepath.Base(path)
}

// fixPATH_PRE 修复PATH_PRE索引
func (cf *CompleteFixer) fixPATH_PRE(files map[int64]*FileInfo) (int, error) {
	cf.logger.Println("Fixing PATH_PRE index...")

	batch := new(leveldb.Batch)
	fixedCount := 0

	for _, file := range files {
		if file.Path != file.CleanPath {
			// 删除旧的PATH_PRE
			oldPathPre := append(PATH_PRE_COMPLETE, []byte(file.Path)...)
			batch.Delete(oldPathPre)

			// 添加新的PATH_PRE
			newPathPre := append(PATH_PRE_COMPLETE, []byte(file.CleanPath)...)
			seqIDBytes := cf.int64ToBytes(file.SeqID)
			batch.Put(newPathPre, seqIDBytes)

			fixedCount++
			if cf.config.Verbose {
				cf.logger.Printf("Fixing PATH_PRE: seqID=%d, %s -> %s", file.SeqID, file.Path, file.CleanPath)
			}
		}
	}

	if fixedCount > 0 {
		if cf.config.DryRun {
			cf.logger.Printf("[DRY RUN] Would fix %d PATH_PRE entries", fixedCount)
		} else {
			if err := cf.db.Write(batch, nil); err != nil {
				return 0, fmt.Errorf("failed to write batch: %v", err)
			}
			cf.logger.Printf("✅ Fixed %d PATH_PRE entries", fixedCount)
		}
	} else {
		cf.logger.Println("✅ All PATH_PRE entries are already clean")
	}

	return fixedCount, nil
}

// fix0x0800 修复0x0800索引
func (cf *CompleteFixer) fix0x0800(files map[int64]*FileInfo) (int, error) {
	cf.logger.Println("Fixing 0x0800 index...")

	// 收集所有0x0800条目
	iter := cf.db.NewIterator(nil, nil)
	defer iter.Release()

	batch := new(leveldb.Batch)
	fixedCount := 0

	for iter.Seek(INDEX_0800_COMPLETE); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != INDEX_0800_COMPLETE[0] || key[1] != INDEX_0800_COMPLETE[1] {
			break
		}

		value := iter.Value()

		// 解析WfsPathBean
		path, timestamp, err := cf.parseWfsPathBean(value)
		if err != nil {
			cf.logger.Printf("Warning: Failed to parse 0x0800 entry: %v", err)
			continue
		}

		// 清理路径
		cleanPath := cf.cleanFileName(path)
		if path != cleanPath {
			// 创建新的WfsPathBean
			newValue := cf.encodeWfsPathBean(cleanPath, timestamp)

			if cf.config.Verbose {
				cf.logger.Printf("Fixing 0x0800: %s -> %s", path, cleanPath)
			}

			if cf.config.DryRun {
				cf.logger.Printf("[DRY RUN] Would fix: %s -> %s", path, cleanPath)
			} else {
				batch.Put(key, newValue)
			}
			fixedCount++
		}
	}

	if fixedCount > 0 && !cf.config.DryRun {
		if err := cf.db.Write(batch, nil); err != nil {
			return 0, fmt.Errorf("failed to write batch: %v", err)
		}
		cf.logger.Printf("✅ Fixed %d 0x0800 entries", fixedCount)
	} else if fixedCount > 0 {
		cf.logger.Printf("[DRY RUN] Would fix %d 0x0800 entries", fixedCount)
	} else {
		cf.logger.Println("✅ All 0x0800 entries are already clean")
	}

	return fixedCount, nil
}

// fixFingerprintAssociations 修复指纹索引和WfsFileBean关联
func (cf *CompleteFixer) fixFingerprintAssociations(files map[int64]*FileInfo) (int, error) {
	cf.logger.Println("Fixing fingerprint and WfsFileBean associations...")

	// 收集现有的指纹索引和WfsFileBean
	fingerprints := cf.collectCurrentFingerprints()
	wfsFileBeans := cf.collectWfsFileBeans()

	cf.logger.Printf("Found %d fingerprint entries and %d WfsFileBean entries",
		len(fingerprints), len(wfsFileBeans))

	// 为每个文件建立正确的指纹 -> WfsFileBean关联
	batch := new(leveldb.Batch)
	fixedCount := 0

	// 收集可用的WfsFileBean（未被使用的）
	usedContentIDs := make(map[string]bool)
	for _, contentID := range fingerprints {
		usedContentIDs[string(contentID)] = true
	}

	availableWfsFileBeans := make([]string, 0)
	for contentIDHex := range wfsFileBeans {
		contentIDBytes, err := hex.DecodeString(contentIDHex)
		if err != nil {
			continue
		}
		if !usedContentIDs[string(contentIDBytes)] {
			availableWfsFileBeans = append(availableWfsFileBeans, contentIDHex)
		}
	}

	cf.logger.Printf("Found %d available WfsFileBean entries", len(availableWfsFileBeans))

	// 为每个文件分配WfsFileBean
	availableIndex := 0
	for _, file := range files {
		// 计算文件的CRC64指纹
		crc64Value := cf.calculateCRC64(file.CleanPath)
		crc64Bytes := cf.int64ToBytes(int64(crc64Value))

		// 检查当前指纹索引
		if currentContentID, exists := fingerprints[string(crc64Bytes)]; exists {
			// 检查该ContentID是否有对应的WfsFileBean
			currentContentIDStr := hex.EncodeToString(currentContentID)
			if _, hasWfsFileBean := wfsFileBeans[currentContentIDStr]; hasWfsFileBean {
				if cf.config.Verbose {
					cf.logger.Printf("File %s already has correct association: CRC64=%d -> ContentID=%s",
						file.CleanPath, crc64Value, currentContentIDStr)
				}
				continue
			}
		}

		// 需要分配一个可用的WfsFileBean
		if availableIndex >= len(availableWfsFileBeans) {
			cf.logger.Printf("Warning: No more available WfsFileBean for %s", file.CleanPath)
			continue
		}

		targetContentIDHex := availableWfsFileBeans[availableIndex]
		targetContentID, err := hex.DecodeString(targetContentIDHex)
		if err != nil {
			cf.logger.Printf("Warning: Invalid ContentID hex %s", targetContentIDHex)
			continue
		}

		// 更新指纹索引
		batch.Put(crc64Bytes, targetContentID)
		fixedCount++
		availableIndex++

		if cf.config.Verbose {
			cf.logger.Printf("Fixing association: %s -> CRC64=%d -> ContentID=%s",
				file.CleanPath, crc64Value, targetContentIDHex)
		}
	}

	if fixedCount > 0 {
		if cf.config.DryRun {
			cf.logger.Printf("[DRY RUN] Would fix %d fingerprint associations", fixedCount)
		} else {
			if err := cf.db.Write(batch, nil); err != nil {
				return 0, fmt.Errorf("failed to write batch: %v", err)
			}
			cf.logger.Printf("✅ Fixed %d fingerprint associations", fixedCount)
		}
	} else {
		cf.logger.Println("✅ All fingerprint associations are already correct")
	}

	return fixedCount, nil
}

// rebuildPATH_SEQ 重建PATH_SEQ索引
func (cf *CompleteFixer) rebuildPATH_SEQ() (int, error) {
	cf.logger.Println("Rebuilding PATH_SEQ index from 0x0800 entries...")

	// 收集所有0x0800条目
	entries0x0800 := cf.collect0x0800Entries()

	// 检查现有的PATH_SEQ索引
	existingPathSeq := cf.checkExistingPathSeq()

	// 重建PATH_SEQ索引
	batch := new(leveldb.Batch)
	addedCount := 0

	for seqID, pathBeanData := range entries0x0800 {
		// 检查是否已经存在PATH_SEQ条目
		if existingPathSeq[seqID] {
			if cf.config.Verbose {
				cf.logger.Printf("PATH_SEQ already exists for seqID=%d", seqID)
			}
			continue
		}

		// 创建PATH_SEQ key
		pathSeqKey := append(PATH_SEQ_COMPLETE, cf.int64ToBytes(seqID)...)

		// 添加到批处理
		batch.Put(pathSeqKey, pathBeanData)
		addedCount++

		if cf.config.Verbose {
			path, timestamp, err := cf.parseWfsPathBean(pathBeanData)
			if err == nil {
				cf.logger.Printf("Adding PATH_SEQ: seqID=%d, path=%s, timestamp=%d",
					seqID, path, timestamp)
			} else {
				cf.logger.Printf("Adding PATH_SEQ: seqID=%d, parse error: %v", seqID, err)
			}
		}
	}

	if addedCount > 0 {
		if cf.config.DryRun {
			cf.logger.Printf("[DRY RUN] Would add %d PATH_SEQ entries", addedCount)
		} else {
			if err := cf.db.Write(batch, nil); err != nil {
				return 0, fmt.Errorf("failed to write batch: %v", err)
			}
			cf.logger.Printf("✅ Added %d PATH_SEQ entries", addedCount)
		}
	} else {
		cf.logger.Println("✅ All PATH_SEQ entries already exist")
	}

	return addedCount, nil
}

func (cf *CompleteFixer) collect0x0800Entries() map[int64][]byte {
	cf.logger.Println("Collecting 0x0800 entries...")

	entries := make(map[int64][]byte)
	iter := cf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(INDEX_0800_COMPLETE); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != INDEX_0800_COMPLETE[0] || key[1] != INDEX_0800_COMPLETE[1] {
			break
		}

		value := iter.Value()

		// 从key中提取seqID
		seqID := cf.extractSeqIDFromKey(key)
		if seqID > 0 {
			// 复制value数据
			pathBeanData := make([]byte, len(value))
			copy(pathBeanData, value)
			entries[seqID] = pathBeanData

			if cf.config.Verbose {
				path, timestamp, err := cf.parseWfsPathBean(pathBeanData)
				if err == nil {
					cf.logger.Printf("0x0800 entry: seqID=%d, path=%s, timestamp=%d",
						seqID, path, timestamp)
				} else {
					cf.logger.Printf("0x0800 entry: seqID=%d, parse error: %v", seqID, err)
				}
			}
		}
	}

	cf.logger.Printf("Found %d 0x0800 entries", len(entries))
	return entries
}

func (cf *CompleteFixer) checkExistingPathSeq() map[int64]bool {
	cf.logger.Println("Checking existing PATH_SEQ entries...")

	existing := make(map[int64]bool)
	iter := cf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(PATH_SEQ_COMPLETE); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != PATH_SEQ_COMPLETE[0] || key[1] != PATH_SEQ_COMPLETE[1] {
			break
		}

		if len(key) >= 10 { // PATH_SEQ (2字节) + seqID (8字节)
			seqIDBytes := key[2:]
			seqID := cf.bytesToInt64(seqIDBytes)
			existing[seqID] = true

			if cf.config.Verbose {
				cf.logger.Printf("Existing PATH_SEQ: seqID=%d", seqID)
			}
		}
	}

	cf.logger.Printf("Found %d existing PATH_SEQ entries", len(existing))
	return existing
}

func (cf *CompleteFixer) extractSeqIDFromKey(key []byte) int64 {
	// 0x0800 key格式: 0x0800 (2字节) + 其他数据 + seqID (8字节)
	if len(key) >= 10 {
		seqIDBytes := key[len(key)-8:]
		seqID := cf.bytesToInt64(seqIDBytes)
		if seqID > 0 && seqID < 1000000 { // 合理的seqID范围
			return seqID
		}
	}
	return 0
}

func (cf *CompleteFixer) collectWfsFileBeans() map[string]int {
	wfsFileBeans := make(map[string]int)
	iter := cf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if cf.isKnownIndex(key) {
			continue
		}

		// WfsFileBean可能的特征：key长度8字节，value长度15-100字节
		if len(key) == 8 && len(value) > 15 && len(value) < 100 {
			keyHex := hex.EncodeToString(key)
			wfsFileBeans[keyHex] = len(value)
		}
	}

	return wfsFileBeans
}

// 辅助函数
func (cf *CompleteFixer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (cf *CompleteFixer) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (cf *CompleteFixer) calculateCRC64(path string) uint64 {
	return crc64.Checksum([]byte(path), cf.crcTable)
}

func (cf *CompleteFixer) collectCurrentFingerprints() map[string][]byte {
	fingerprints := make(map[string][]byte)
	iter := cf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if cf.isKnownIndex(key) {
			continue
		}

		// CRC64指纹索引：key长度8字节，value长度8字节
		if len(key) == 8 && len(value) == 8 {
			keyStr := string(key)
			contentID := make([]byte, len(value))
			copy(contentID, value)
			fingerprints[keyStr] = contentID
		}
	}

	return fingerprints
}

func (cf *CompleteFixer) isKnownIndex(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
		{0x06, 0x00}, // 其他已知前缀
		{0x07, 0x00},
		{0x09, 0x00},
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (cf *CompleteFixer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (cf *CompleteFixer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// Path字段 (field 1, wire type 2)
	if path != "" {
		result = append(result, 0x0A) // tag: field 1, wire type 2
		pathBytes := []byte(path)

		// 编码长度
		length := len(pathBytes)
		for length >= 0x80 {
			result = append(result, byte(length)|0x80)
			length >>= 7
		}
		result = append(result, byte(length))

		// 添加路径数据
		result = append(result, pathBytes...)
	}

	// Timestamp字段 (field 2, wire type 0)
	if timestamp != 0 {
		result = append(result, 0x10) // tag: field 2, wire type 0

		// 使用zigzag编码
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

// copyFromReference 从参考数据库复制缺失的数据结构
func (cf *CompleteFixer) copyFromReference() (int, error) {
	if cf.refDB == nil {
		return 0, fmt.Errorf("no reference database provided")
	}

	cf.logger.Println("Copying missing data from reference database...")

	// 收集参考数据库的所有条目
	refEntries := cf.collectReferenceEntries()

	// 收集当前数据库的所有条目
	currentEntries := cf.collectCurrentEntries()

	// 找出需要复制的条目
	missingEntries := make(map[string][]byte)
	for keyHex, value := range refEntries {
		if _, exists := currentEntries[keyHex]; !exists {
			missingEntries[keyHex] = value
		}
	}

	// 找出需要删除的错误条目
	extraEntries := make(map[string][]byte)
	for keyHex, value := range currentEntries {
		if _, exists := refEntries[keyHex]; !exists {
			// 只删除我们可能创建的错误条目，保留PATH_PRE和0x0800
			if cf.shouldDeleteEntry(keyHex) {
				extraEntries[keyHex] = value
			}
		}
	}

	batch := new(leveldb.Batch)
	totalOperations := 0

	// 删除多余的条目
	for keyHex := range extraEntries {
		key, err := hex.DecodeString(keyHex)
		if err != nil {
			cf.logger.Printf("Warning: Failed to decode key %s: %v", keyHex, err)
			continue
		}

		batch.Delete(key)
		totalOperations++

		if cf.config.Verbose {
			cf.logger.Printf("Deleting: %s", keyHex)
		}
	}

	// 添加缺失的条目
	for keyHex, value := range missingEntries {
		key, err := hex.DecodeString(keyHex)
		if err != nil {
			cf.logger.Printf("Warning: Failed to decode key %s: %v", keyHex, err)
			continue
		}

		batch.Put(key, value)
		totalOperations++

		if cf.config.Verbose {
			cf.logger.Printf("Adding: %s -> %s", keyHex, hex.EncodeToString(value))
		}
	}

	if totalOperations > 0 {
		if cf.config.DryRun {
			cf.logger.Printf("[DRY RUN] Would perform %d operations (%d deletions, %d additions)",
				totalOperations, len(extraEntries), len(missingEntries))
		} else {
			if err := cf.db.Write(batch, nil); err != nil {
				return 0, fmt.Errorf("failed to write batch: %v", err)
			}
			cf.logger.Printf("✅ Performed %d operations (%d deletions, %d additions)",
				totalOperations, len(extraEntries), len(missingEntries))
		}
	} else {
		cf.logger.Println("✅ No operations needed - database is already correct")
	}

	return totalOperations, nil
}

func (cf *CompleteFixer) collectReferenceEntries() map[string][]byte {
	entries := make(map[string][]byte)
	iter := cf.refDB.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		keyHex := hex.EncodeToString(key)
		valueBytes := make([]byte, len(value))
		copy(valueBytes, value)
		entries[keyHex] = valueBytes
	}

	return entries
}

func (cf *CompleteFixer) collectCurrentEntries() map[string][]byte {
	entries := make(map[string][]byte)
	iter := cf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		keyHex := hex.EncodeToString(key)
		valueBytes := make([]byte, len(value))
		copy(valueBytes, value)
		entries[keyHex] = valueBytes
	}

	return entries
}

func (cf *CompleteFixer) shouldDeleteEntry(keyHex string) bool {
	// 不删除PATH_PRE (0000) 和 0x0800 (0800) 条目
	if len(keyHex) >= 4 {
		prefix := keyHex[:4]
		if prefix == "0000" || prefix == "0800" {
			return false
		}
	}

	// 删除其他条目（PATH_SEQ、指纹索引、WfsFileBean等）
	return true
}

// cleanupWrongEntries 清理错误条目
func (cf *CompleteFixer) cleanupWrongEntries() (int, error) {
	cf.logger.Println("Cleaning up wrong entries...")

	// 定义要删除的特定条目
	wrongEntryKeys := []string{
		"0000000000000001",
		"0000000000000002",
		"0000000000000003",
		"0000000000000004",
		"0000000000000005",
	}

	batch := new(leveldb.Batch)
	deletedCount := 0

	for _, keyHex := range wrongEntryKeys {
		key, err := hex.DecodeString(keyHex)
		if err != nil {
			cf.logger.Printf("Warning: Failed to decode key %s: %v", keyHex, err)
			continue
		}

		// 检查条目是否存在
		if value, err := cf.db.Get(key, nil); err == nil {
			batch.Delete(key)
			deletedCount++

			if cf.config.Verbose {
				cf.logger.Printf("Will delete: %s -> %s", keyHex, hex.EncodeToString(value))
			}
		}
	}

	if deletedCount > 0 {
		if cf.config.DryRun {
			cf.logger.Printf("[DRY RUN] Would delete %d wrong entries", deletedCount)
		} else {
			if err := cf.db.Write(batch, nil); err != nil {
				return 0, fmt.Errorf("failed to write batch: %v", err)
			}
			cf.logger.Printf("✅ Deleted %d wrong entries", deletedCount)
		}
	} else {
		cf.logger.Println("✅ No wrong entries found to delete")
	}

	return deletedCount, nil
}

// printSummary 打印修复总结
func (cf *CompleteFixer) printSummary(results []FixResult, totalDuration time.Duration) {
	cf.logger.Println("\n" + strings.Repeat("=", 60))
	cf.logger.Println("🎯 WFS Complete Fix Summary")
	cf.logger.Println(strings.Repeat("=", 60))

	successCount := 0
	totalOperations := 0

	for _, result := range results {
		status := "✅"
		if !result.Success {
			status = "❌"
		} else {
			successCount++
		}

		totalOperations += result.Count

		cf.logger.Printf("%s %-20s: %3d operations (%v)",
			status, result.Step, result.Count, result.Duration.Round(time.Millisecond))

		if result.Error != nil {
			cf.logger.Printf("   Error: %v", result.Error)
		}
	}

	cf.logger.Println(strings.Repeat("-", 60))
	cf.logger.Printf("📊 Total: %d/%d steps successful, %d operations, %v",
		successCount, len(results), totalOperations, totalDuration.Round(time.Millisecond))

	if successCount == len(results) {
		cf.logger.Println("🎉 All steps completed successfully!")
		cf.logger.Println("💡 You can now restart WFS service and check the web interface.")
	} else {
		cf.logger.Println("⚠️  Some steps failed. Please check the errors above.")
	}

	cf.logger.Println(strings.Repeat("=", 60))
}

// Close 关闭数据库连接
func (cf *CompleteFixer) Close() {
	if cf.db != nil {
		cf.db.Close()
	}
	if cf.refDB != nil {
		cf.refDB.Close()
	}
}

func main() {
	config := &CompleteFixerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.StringVar(&config.ReferencePath, "ref", "", "Reference database path (optional)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode - show what would be done without making changes")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.IntVar(&config.Workers, "workers", 4, "Number of concurrent workers")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Complete Fixer - 一键修复文件名路径前缀问题\n\n")
		fmt.Fprintf(os.Stderr, "这个工具集成了所有WFS修复步骤，支持高并发处理：\n")
		fmt.Fprintf(os.Stderr, "1. 修复PATH_PRE索引（移除路径前缀）\n")
		fmt.Fprintf(os.Stderr, "2. 修复0x0800索引（更新WfsPathBean）\n")
		fmt.Fprintf(os.Stderr, "3. 修复CRC64指纹索引\n")
		fmt.Fprintf(os.Stderr, "4. 从参考数据库复制缺失数据（可选）\n")
		fmt.Fprintf(os.Stderr, "5. 清理错误条目\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  # 预览修复操作\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  # 执行完整修复\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  # 使用参考数据库修复\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -ref C:\\wfsdata_new\\wfsdb -verbose\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "注意事项：\n")
		fmt.Fprintf(os.Stderr, "- 修复前请停止WFS服务\n")
		fmt.Fprintf(os.Stderr, "- 建议先使用 -dry-run 预览修复操作\n")
		fmt.Fprintf(os.Stderr, "- 建议备份数据库文件\n")
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n\n")
		flag.Usage()
		os.Exit(1)
	}

	// 验证数据库路径
	if _, err := os.Stat(config.DatabasePath); os.IsNotExist(err) {
		fmt.Fprintf(os.Stderr, "Error: Database path does not exist: %s\n", config.DatabasePath)
		os.Exit(1)
	}

	// 验证参考数据库路径（如果提供）
	if config.ReferencePath != "" {
		if _, err := os.Stat(config.ReferencePath); os.IsNotExist(err) {
			fmt.Fprintf(os.Stderr, "Error: Reference database path does not exist: %s\n", config.ReferencePath)
			os.Exit(1)
		}
	}

	// 设置合理的工作线程数
	if config.Workers < 1 {
		config.Workers = 1
	} else if config.Workers > 16 {
		config.Workers = 16
	}

	fixer, err := NewCompleteFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
