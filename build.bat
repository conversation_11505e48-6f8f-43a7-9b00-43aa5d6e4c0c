@echo off
REM LevelDB Key Fixer 编译脚本
REM 用于编译WFS系统的LevelDB key路径修复工具

echo ========================================
echo LevelDB Key Fixer 编译脚本
echo ========================================
echo.

chcp 65001

REM 检查Go环境
echo 检查Go环境...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go环境，请先安装Go 1.18或更高版本
    pause
    exit /b 1
)

go version
echo.

REM 初始化Go模块（如果不存在）
if not exist go.mod (
    echo 初始化Go模块...
    go mod init leveldb_key_fixer
    echo.
)

REM 下载依赖
echo 下载依赖包...
go get github.com/syndtr/goleveldb/leveldb
if %errorlevel% neq 0 (
    echo 错误: 依赖包下载失败
    pause
    exit /b 1
)
echo 依赖包下载完成
echo.

REM 编译主程序
echo 编译主程序 leveldb_key_fixer.exe...
go build -ldflags "-s -w" -o leveldb_key_fixer.exe leveldb_key_fixer.go
if %errorlevel% neq 0 (
    echo 错误: 主程序编译失败
    pause
    exit /b 1
)
echo 主程序编译成功: leveldb_key_fixer.exe
echo.

REM 编译测试程序
echo 编译测试程序 key_fixer_test.exe...
go build -ldflags "-s -w" -o key_fixer_test.exe key_fixer_test.go
if %errorlevel% neq 0 (
    echo 错误: 测试程序编译失败
    pause
    exit /b 1
)
echo 测试程序编译成功: key_fixer_test.exe
echo.

REM 显示编译结果
echo ========================================
echo 编译完成！
echo ========================================
echo.
echo 生成的文件:
dir /b *.exe 2>nul
echo.

REM 显示文件大小
echo 文件大小:
for %%f in (*.exe) do (
    for %%s in (%%f) do echo   %%f: %%~zs bytes
)
echo.

echo 使用方法:
echo   1. 修复数据库: leveldb_key_fixer.exe ^<数据库路径^>
echo   2. 运行测试:   key_fixer_test.exe
echo   3. 性能测试:   key_fixer_test.exe perf
echo.
echo 详细使用说明请参考 README_key_fixer.md
echo.

pause
