// PATH_SEQ索引专门检查工具
package main

import (
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00}
	PATH_SEQ = []byte{0x01, 0x00}
)

func main() {
	var dbPath string
	var verbose bool
	
	flag.StringVar(&dbPath, "db", "", "Database path (required)")
	flag.BoolVar(&verbose, "verbose", false, "Verbose output")
	flag.Parse()

	if dbPath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		os.Exit(1)
	}

	logger := log.New(os.Stdout, "[PathSeqChecker] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
	}
	defer db.Close()

	logger.Println("=== PATH_SEQ Index Check ===")

	// 1. 收集PATH_PRE信息
	pathInfo := make(map[string][]byte) // path -> seqID
	
	iter := db.NewIterator(nil, nil)
	defer iter.Release()

	logger.Println("Scanning PATH_PRE index...")
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqID := iter.Value()
				pathInfo[path] = seqID
				
				seqIDInt := bytesToInt64(seqID)
				if verbose {
					logger.Printf("PATH_PRE: %s -> seqID %d (%x)", path, seqIDInt, seqID)
				}
			}
		}
	}

	logger.Printf("Found %d PATH_PRE entries", len(pathInfo))

	// 2. 检查对应的PATH_SEQ
	logger.Println("Checking PATH_SEQ index...")
	
	foundPATH_SEQ := 0
	missingPATH_SEQ := 0
	
	for path, seqID := range pathInfo {
		seqIDInt := bytesToInt64(seqID)
		pathSeqKey := append(PATH_SEQ, int64ToBytes(seqIDInt)...)
		
		if data, err := db.Get(pathSeqKey, nil); err == nil {
			foundPATH_SEQ++
			
			if seqPath, timestamp, err := parseWfsPathBean(data); err == nil {
				if verbose {
					logger.Printf("✅ PATH_SEQ found: seqID %d -> path=%s, timestamp=%d", seqIDInt, seqPath, timestamp)
				}
				if seqPath != path {
					logger.Printf("⚠️  PATH_SEQ path mismatch: expected %s, got %s", path, seqPath)
				}
			} else {
				logger.Printf("❌ PATH_SEQ parse error for seqID %d: %v", seqIDInt, err)
			}
		} else {
			missingPATH_SEQ++
			logger.Printf("❌ PATH_SEQ missing: seqID %d (%s)", seqIDInt, path)
		}
	}

	// 3. 扫描所有PATH_SEQ条目
	logger.Println("Scanning all PATH_SEQ entries...")
	
	iter = db.NewIterator(nil, nil)
	defer iter.Release()
	
	allPATH_SEQ := 0
	for iter.Seek(PATH_SEQ); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != PATH_SEQ[0] || key[1] != PATH_SEQ[1] {
			break
		}
		
		allPATH_SEQ++
		if len(key) >= 10 { // PATH_SEQ + 8字节seqID
			seqIDBytes := key[2:]
			seqID := bytesToInt64(seqIDBytes)
			
			value := iter.Value()
			if path, timestamp, err := parseWfsPathBean(value); err == nil {
				if verbose {
					logger.Printf("PATH_SEQ entry: seqID %d -> path=%s, timestamp=%d", seqID, path, timestamp)
				}
			}
		}
	}

	logger.Printf("Total PATH_SEQ entries found: %d", allPATH_SEQ)

	// 4. 总结
	logger.Println("\n=== Summary ===")
	logger.Printf("PATH_PRE entries: %d", len(pathInfo))
	logger.Printf("PATH_SEQ found: %d", foundPATH_SEQ)
	logger.Printf("PATH_SEQ missing: %d", missingPATH_SEQ)
	logger.Printf("Total PATH_SEQ entries: %d", allPATH_SEQ)
	
	if missingPATH_SEQ > 0 {
		logger.Printf("\n⚠️  %d files have missing PATH_SEQ indexes", missingPATH_SEQ)
	} else {
		logger.Printf("\n✅ All files have PATH_SEQ indexes")
	}
}

// 辅助函数
func bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		return 0
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64
	
	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n
		
		fieldNum := tag >> 3
		wireType := tag & 0x7
		
		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}
	
	return path, timestamp, nil
}
