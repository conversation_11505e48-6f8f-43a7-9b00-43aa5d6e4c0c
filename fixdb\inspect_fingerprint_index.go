// Fingerprint Index Inspector for WFS System
// 检查WFS系统中的文件指纹索引

package main

import (
	"bytes"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE   = []byte{0x00, 0x00}
	PATH_SEQ   = []byte{0x01, 0x00}
	INDEX_0800 = []byte{0x08, 0x00}
)

// 文件哈希类型
const (
	HASH_CRC64  = 0
	HASH_MD5    = 1
	HASH_SHA1   = 2
	HASH_SHA256 = 3
)

// 指纹检查器
type FingerprintInspector struct {
	db     *leveldb.DB
	logger *log.Logger
}

// 创建检查器
func NewFingerprintInspector(dbPath string) (*FingerprintInspector, error) {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     8 * 1024 * 1024,
		WriteBuffer:            4 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}

	return &FingerprintInspector{
		db:     db,
		logger: log.New(os.Stdout, "[FingerprintInspector] ", log.LstdFlags),
	}, nil
}

// 关闭检查器
func (fi *FingerprintInspector) Close() error {
	if fi.db != nil {
		return fi.db.Close()
	}
	return nil
}

// CRC64计算（简化版本）
func crc64(data []byte) uint64 {
	// 这是一个简化的CRC64实现
	// 实际的WFS可能使用不同的CRC64算法
	var crc uint64 = 0xFFFFFFFFFFFFFFFF
	for _, b := range data {
		crc = crc ^ uint64(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xC96C5795D7870F42
			} else {
				crc = crc >> 1
			}
		}
	}
	return crc ^ 0xFFFFFFFFFFFFFFFF
}

// 计算文件路径的指纹
func (fi *FingerprintInspector) calculateFingerprint(path string, hashType int) []byte {
	pathBytes := []byte(path)

	switch hashType {
	case HASH_CRC64:
		crc := crc64(pathBytes)
		result := make([]byte, 8)
		binary.BigEndian.PutUint64(result, crc)
		return result
	case HASH_MD5:
		hash := md5.Sum(pathBytes)
		return hash[:]
	case HASH_SHA1:
		hash := sha1.Sum(pathBytes)
		return hash[:]
	case HASH_SHA256:
		hash := sha256.Sum256(pathBytes)
		return hash[:]
	default:
		crc := crc64(pathBytes)
		result := make([]byte, 8)
		binary.BigEndian.PutUint64(result, crc)
		return result
	}
}

// 检查指纹索引
func (fi *FingerprintInspector) InspectFingerprints() error {
	fi.logger.Println("=== Fingerprint Index Inspection ===")

	// 首先获取所有PATH_SEQ条目
	pathSeqEntries := make(map[int64]string)

	iter := fi.db.NewIterator(levelutil.BytesPrefix(PATH_SEQ), nil)
	defer iter.Release()

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_SEQ) {
			continue
		}

		// 解析seqID
		seqIDBytes := key[len(PATH_SEQ):]
		var seqID int64
		if len(seqIDBytes) >= 8 {
			seqID = int64(binary.BigEndian.Uint64(seqIDBytes))
		} else {
			paddedBytes := make([]byte, 8)
			copy(paddedBytes[8-len(seqIDBytes):], seqIDBytes)
			seqID = int64(binary.BigEndian.Uint64(paddedBytes))
		}

		// 解析WfsPathBean中的路径
		path, _, err := fi.parseWfsPathBean(value)
		if err != nil {
			fi.logger.Printf("Failed to parse PATH_SEQ seqID %d: %v", seqID, err)
			continue
		}

		pathSeqEntries[seqID] = path
		fi.logger.Printf("PATH_SEQ[%d]: %s", seqID, path)
	}

	fi.logger.Printf("Found %d PATH_SEQ entries", len(pathSeqEntries))

	// 检查每个路径的指纹索引
	fi.logger.Println("\n=== Checking Fingerprint Indexes ===")

	hashTypes := []int{HASH_CRC64, HASH_MD5, HASH_SHA1, HASH_SHA256}
	hashNames := []string{"CRC64", "MD5", "SHA1", "SHA256"}

	for seqID, path := range pathSeqEntries {
		fi.logger.Printf("\nChecking fingerprints for seqID %d: %s", seqID, path)

		foundFingerprint := false
		for i, hashType := range hashTypes {
			fingerprint := fi.calculateFingerprint(path, hashType)

			// 检查指纹是否存在
			if value, err := fi.db.Get(fingerprint, nil); err == nil {
				fi.logger.Printf("  ✅ %s fingerprint found: %x -> %x", hashNames[i], fingerprint, value)
				foundFingerprint = true

				// 尝试获取文件内容信息
				if contentData, err := fi.db.Get(value, nil); err == nil {
					fi.logger.Printf("     File content data found: %d bytes", len(contentData))
				} else {
					fi.logger.Printf("     ❌ File content data missing")
				}
			} else {
				fi.logger.Printf("  ❌ %s fingerprint missing: %x", hashNames[i], fingerprint)
			}
		}

		if !foundFingerprint {
			fi.logger.Printf("  ⚠️  NO fingerprints found for path: %s", path)
		}
	}

	// 检查是否有孤立的指纹（指向不存在的路径）
	fi.logger.Println("\n=== Checking for Orphaned Fingerprints ===")
	fi.checkOrphanedFingerprints(pathSeqEntries)

	return nil
}

// 解析protobuf格式的WfsPathBean
func (fi *FingerprintInspector) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 检查孤立的指纹
func (fi *FingerprintInspector) checkOrphanedFingerprints(validPaths map[int64]string) {
	// 创建有效路径的指纹集合
	validFingerprints := make(map[string]bool)
	hashTypes := []int{HASH_CRC64, HASH_MD5, HASH_SHA1, HASH_SHA256}

	for _, path := range validPaths {
		for _, hashType := range hashTypes {
			fingerprint := fi.calculateFingerprint(path, hashType)
			validFingerprints[string(fingerprint)] = true
		}
	}

	// 扫描数据库中可能的指纹条目
	iter := fi.db.NewIterator(nil, nil)
	defer iter.Release()

	orphanCount := 0
	for iter.Next() {
		key := iter.Key()

		// 跳过已知的前缀
		if len(key) >= 2 {
			prefix := key[:2]
			if bytes.Equal(prefix, PATH_PRE) || bytes.Equal(prefix, PATH_SEQ) || bytes.Equal(prefix, INDEX_0800) {
				continue
			}
		}

		// 检查是否是指纹长度
		if len(key) == 8 || len(key) == 16 || len(key) == 20 || len(key) == 32 {
			if !validFingerprints[string(key)] {
				// 检查value是否指向文件内容
				value := iter.Value()
				if len(value) > 0 {
					orphanCount++
					fi.logger.Printf("Orphaned fingerprint[%d]: %x -> %x", orphanCount, key, value)
				}
			}
		}
	}

	fi.logger.Printf("Found %d potentially orphaned fingerprints", orphanCount)
}

// 主函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: inspect_fingerprint_index <db_path>")
		fmt.Println("This tool inspects the fingerprint index in WFS database")
		os.Exit(1)
	}

	dbPath := os.Args[1]

	inspector, err := NewFingerprintInspector(dbPath)
	if err != nil {
		log.Fatalf("Failed to create inspector: %v", err)
	}
	defer inspector.Close()

	if err := inspector.InspectFingerprints(); err != nil {
		log.Fatalf("Inspection failed: %v", err)
	}
}
