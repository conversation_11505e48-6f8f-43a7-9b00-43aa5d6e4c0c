// WFS安全修复工具 - 只修改必要的索引，不删除任何可能的文件内容
package main

import (
	"crypto/md5"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_SAFE = []byte{0x00, 0x00}
	PATH_SEQ_SAFE = []byte{0x01, 0x00}
)

type SafeFixerConfig struct {
	DatabasePath string
	ReferencePath string
	DryRun       bool
	Verbose      bool
}

type WFSSafeFixer struct {
	config *SafeFixerConfig
	db     *leveldb.DB
	refDB  *leveldb.DB
	logger *log.Logger
}

func NewWFSSafeFixer(config *SafeFixerConfig) (*WFSSafeFixer, error) {
	logger := log.New(os.Stdout, "[WFSSafeFixer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open database normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	var refDB *leveldb.DB
	if config.ReferencePath != "" {
		refOptions := &opt.Options{
			Filter:                 filter.NewBloomFilter(10),
			OpenFilesCacheCapacity: 1 << 8,
			BlockCacheCapacity:     32 * 1024 * 1024,
			WriteBuffer:            8 * 1024 * 1024,
			ReadOnly:               true,
			ErrorIfMissing:         false,
			Strict:                 opt.NoStrict,
		}

		refDB, err = leveldb.OpenFile(config.ReferencePath, refOptions)
		if err != nil {
			logger.Printf("Failed to open reference database, will proceed without it: %v", err)
		} else {
			logger.Printf("Successfully opened reference database")
		}
	}

	return &WFSSafeFixer{
		config: config,
		db:     db,
		refDB:  refDB,
		logger: logger,
	}, nil
}

func (wsf *WFSSafeFixer) Fix() error {
	wsf.logger.Println("=== WFS Safe Fix ===")

	// 1. 收集所有文件
	files, err := wsf.collectFiles()
	if err != nil {
		return fmt.Errorf("failed to collect files: %v", err)
	}

	// 2. 修复PATH_PRE索引
	if err := wsf.fixPATH_PRE(files); err != nil {
		return fmt.Errorf("failed to fix PATH_PRE: %v", err)
	}

	// 3. 修复0x0800索引
	if err := wsf.fix0x0800(files); err != nil {
		return fmt.Errorf("failed to fix 0x0800: %v", err)
	}

	// 4. 尝试从参考数据库复制关键索引
	if wsf.refDB != nil {
		if err := wsf.copyFromReference(); err != nil {
			wsf.logger.Printf("Warning: Failed to copy from reference: %v", err)
		}
	}

	return nil
}

func (wsf *WFSSafeFixer) collectFiles() (map[int64]string, error) {
	wsf.logger.Println("Collecting files from PATH_PRE...")

	files := make(map[int64]string)
	iter := wsf.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_SAFE[0] && key[1] == PATH_PRE_SAFE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := wsf.bytesToInt64(seqIDBytes)

				// 检查是否需要清理路径
				cleanPath := wsf.cleanFileName(path)
				needsClean := path != cleanPath

				files[seqID] = path

				if wsf.config.Verbose {
					if needsClean {
						wsf.logger.Printf("File needs cleaning: seqID=%d, %s -> %s", seqID, path, cleanPath)
					} else {
						wsf.logger.Printf("File already clean: seqID=%d, %s", seqID, path)
					}
				}
			}
		}
	}

	wsf.logger.Printf("Found %d files", len(files))
	return files, nil
}

func (wsf *WFSSafeFixer) fixPATH_PRE(files map[int64]string) error {
	wsf.logger.Println("Fixing PATH_PRE index...")

	batch := new(leveldb.Batch)
	fixedCount := 0

	for seqID, path := range files {
		cleanPath := wsf.cleanFileName(path)
		if path != cleanPath {
			// 删除旧的PATH_PRE
			oldPathPre := append(PATH_PRE_SAFE, []byte(path)...)
			batch.Delete(oldPathPre)

			// 添加新的PATH_PRE
			newPathPre := append(PATH_PRE_SAFE, []byte(cleanPath)...)
			seqIDBytes := wsf.int64ToBytes(seqID)
			batch.Put(newPathPre, seqIDBytes)

			fixedCount++
			if wsf.config.Verbose {
				wsf.logger.Printf("Fixing PATH_PRE: seqID=%d, %s -> %s", seqID, path, cleanPath)
			}
		}
	}

	if fixedCount > 0 {
		if wsf.config.DryRun {
			wsf.logger.Printf("[DRY RUN] Would fix %d PATH_PRE entries", fixedCount)
		} else {
			if err := wsf.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			wsf.logger.Printf("✅ Fixed %d PATH_PRE entries", fixedCount)
		}
	} else {
		wsf.logger.Println("✅ All PATH_PRE entries are already clean")
	}

	return nil
}

func (wsf *WFSSafeFixer) fix0x0800(files map[int64]string) error {
	wsf.logger.Println("Fixing 0x0800 index...")

	// 收集所有0x0800条目
	prefix0x0800 := []byte{0x08, 0x00}
	iter := wsf.db.NewIterator(nil, nil)
	defer iter.Release()

	type Entry0x0800 struct {
		Key       []byte
		Path      string
		Timestamp int64
		SeqID     int64
	}

	entries := make(map[int64]*Entry0x0800)

	for iter.Seek(prefix0x0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		value := iter.Value()
		path, timestamp, err := wsf.parseWfsPathBean(value)
		if err != nil {
			wsf.logger.Printf("Warning: Failed to parse 0x0800 entry: %v", err)
			continue
		}

		// 尝试提取seqID
		seqID := wsf.extractSeqIDFromKey(key)
		if seqID > 0 {
			entries[seqID] = &Entry0x0800{
				Key:       key,
				Path:      path,
				Timestamp: timestamp,
				SeqID:     seqID,
			}

			if wsf.config.Verbose {
				wsf.logger.Printf("Found 0x0800: seqID=%d, path=%s, timestamp=%d", 
					seqID, path, timestamp)
			}
		}
	}

	// 修复0x0800条目
	batch := new(leveldb.Batch)
	fixedCount := 0

	for seqID, originalPath := range files {
		cleanPath := wsf.cleanFileName(originalPath)
		
		// 检查是否有对应的0x0800条目
		if entry, exists := entries[seqID]; exists {
			// 检查路径是否需要更新
			if entry.Path != cleanPath {
				// 创建新的WfsPathBean
				newPathBeanData := wsf.encodeWfsPathBean(cleanPath, entry.Timestamp)
				batch.Put(entry.Key, newPathBeanData)

				fixedCount++
				if wsf.config.Verbose {
					wsf.logger.Printf("Fixing 0x0800: seqID=%d, %s -> %s", 
						seqID, entry.Path, cleanPath)
				}
			}
		} else {
			// 没有找到对应的0x0800条目，创建新的
			timestamp := time.Now().UnixNano()
			newKey := wsf.create0x0800Key(seqID)
			newPathBeanData := wsf.encodeWfsPathBean(cleanPath, timestamp)
			batch.Put(newKey, newPathBeanData)

			fixedCount++
			if wsf.config.Verbose {
				wsf.logger.Printf("Creating new 0x0800: seqID=%d, path=%s", seqID, cleanPath)
			}
		}
	}

	if fixedCount > 0 {
		if wsf.config.DryRun {
			wsf.logger.Printf("[DRY RUN] Would fix %d 0x0800 entries", fixedCount)
		} else {
			if err := wsf.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			wsf.logger.Printf("✅ Fixed %d 0x0800 entries", fixedCount)
		}
	} else {
		wsf.logger.Println("✅ All 0x0800 entries are already correct")
	}

	return nil
}

func (wsf *WFSSafeFixer) copyFromReference() error {
	if wsf.refDB == nil {
		return fmt.Errorf("no reference database provided")
	}

	wsf.logger.Println("Copying key entries from reference database...")

	// 收集当前数据库的所有文件
	files, err := wsf.collectFiles()
	if err != nil {
		return fmt.Errorf("failed to collect files: %v", err)
	}

	// 尝试从参考数据库复制PATH_SEQ索引
	batch := new(leveldb.Batch)
	copiedCount := 0

	for seqID, path := range files {
		cleanPath := wsf.cleanFileName(path)
		
		// 检查当前数据库是否已有PATH_SEQ
		pathSeqKey := append(PATH_SEQ_SAFE, wsf.int64ToBytes(seqID)...)
		_, err := wsf.db.Get(pathSeqKey, nil)
		if err == leveldb.ErrNotFound {
			// 尝试从参考数据库获取
			refPathSeqKey := append(PATH_SEQ_SAFE, wsf.int64ToBytes(seqID)...)
			if refValue, err := wsf.refDB.Get(refPathSeqKey, nil); err == nil {
				// 解析并更新路径
				refPath, timestamp, err := wsf.parseWfsPathBean(refValue)
				if err == nil {
					// 创建新的WfsPathBean，使用参考数据库的时间戳但使用清理后的路径
					newPathBeanData := wsf.encodeWfsPathBean(cleanPath, timestamp)
					batch.Put(pathSeqKey, newPathBeanData)

					copiedCount++
					if wsf.config.Verbose {
						wsf.logger.Printf("Copying PATH_SEQ: seqID=%d, refPath=%s -> %s", 
							seqID, refPath, cleanPath)
					}
				}
			}
		}
	}

	if copiedCount > 0 {
		if wsf.config.DryRun {
			wsf.logger.Printf("[DRY RUN] Would copy %d entries from reference", copiedCount)
		} else {
			if err := wsf.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			wsf.logger.Printf("✅ Copied %d entries from reference", copiedCount)
		}
	} else {
		wsf.logger.Println("No entries needed to be copied from reference")
	}

	return nil
}

func (wsf *WFSSafeFixer) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	return filepath.Base(path)
}

func (wsf *WFSSafeFixer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (wsf *WFSSafeFixer) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (wsf *WFSSafeFixer) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func (wsf *WFSSafeFixer) extractSeqIDFromKey(key []byte) int64 {
	// 尝试不同的seqID提取方法
	if len(key) >= 10 { // 0x0800 (2字节) + 其他数据 + seqID (8字节)
		seqIDBytes := key[len(key)-8:]
		seqID := wsf.bytesToInt64(seqIDBytes)
		if seqID > 0 && seqID < 1000000 { // 合理的seqID范围
			return seqID
		}
	}
	return 0
}

func (wsf *WFSSafeFixer) create0x0800Key(seqID int64) []byte {
	// 创建0x0800 key: 0x0800 + 随机数据 + seqID
	key := make([]byte, 17) // 2字节前缀 + 7字节随机数据 + 8字节seqID
	key[0] = 0x08
	key[1] = 0x00
	
	// 填充随机数据（这里简单使用时间戳）
	timestamp := time.Now().UnixNano()
	binary.BigEndian.PutUint64(key[2:10], uint64(timestamp))
	
	// 添加seqID
	seqIDBytes := wsf.int64ToBytes(seqID)
	copy(key[9:], seqIDBytes)
	
	return key
}

func (wsf *WFSSafeFixer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (wsf *WFSSafeFixer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// Path字段 (field 1, wire type 2)
	if path != "" {
		result = append(result, 0x0A)
		pathBytes := []byte(path)
		result = append(result, byte(len(pathBytes)))
		result = append(result, pathBytes...)
	}

	// Timestamp字段 (field 2, wire type 0)
	if timestamp != 0 {
		result = append(result, 0x10)
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

func (wsf *WFSSafeFixer) Close() {
	if wsf.db != nil {
		wsf.db.Close()
	}
	if wsf.refDB != nil {
		wsf.refDB.Close()
	}
}

func main() {
	config := &SafeFixerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.StringVar(&config.ReferencePath, "ref", "", "Reference database path (optional)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Safe Fixer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -ref C:\\wfsdata_new\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	fixer, err := NewWFSSafeFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
