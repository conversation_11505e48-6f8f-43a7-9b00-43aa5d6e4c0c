// Test program for target key existence functionality
// 测试目标key存在性检查功能

package main

import (
	"bytes"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE = []byte{0, 0}
)

// 测试目标key存在性功能
func testTargetKeyExists() error {
	testDBPath := "test_target_exists_" + time.Now().Format("20060102_150405")
	defer os.RemoveAll(testDBPath)

	log.Printf("Creating test database: %s", testDBPath)

	// 创建测试数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     8 * 1024 * 1024,
		WriteBuffer:            4 * 1024 * 1024,
	}

	db, err := leveldb.OpenFile(testDBPath, options)
	if err != nil {
		return fmt.Errorf("failed to create test database: %v", err)
	}

	// 准备测试数据
	batch := new(leveldb.Batch)

	// 添加一些正确格式的key（目标key）
	targetKey1 := append(PATH_PRE, []byte("document.pdf")...)
	targetKey2 := append(PATH_PRE, []byte("image.jpg")...)
	batch.Put(targetKey1, []byte("existing_target_value_1"))
	batch.Put(targetKey2, []byte("existing_target_value_2"))

	// 添加一些错误格式的key（需要修复的key）
	wrongKey1 := append(PATH_PRE, []byte("path/to/file/document.pdf")...)
	wrongKey2 := append(PATH_PRE, []byte("deep/nested/path/image.jpg")...)
	wrongKey3 := append(PATH_PRE, []byte("another/path/newfile.txt")...)
	batch.Put(wrongKey1, []byte("wrong_value_1"))
	batch.Put(wrongKey2, []byte("wrong_value_2"))
	batch.Put(wrongKey3, []byte("wrong_value_3"))

	if err := db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
		return fmt.Errorf("failed to write test data: %v", err)
	}

	log.Printf("Prepared test data:")
	log.Printf("  Target keys (existing): document.pdf, image.jpg")
	log.Printf("  Wrong keys: path/to/file/document.pdf, deep/nested/path/image.jpg, another/path/newfile.txt")

	// 关闭数据库以便修复工具使用
	db.Close()

	// 运行修复工具
	log.Printf("Running key fixer with target existence check...")

	// 模拟修复过程
	db, err = leveldb.OpenFile(testDBPath, options)
	if err != nil {
		return fmt.Errorf("failed to reopen database: %v", err)
	}
	defer db.Close()

	// 统计修复结果
	fixedCount := 0
	deletedOnlyCount := 0
	normalFixCount := 0

	iter := db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	keysToFix := make([][]byte, 0)
	for iter.Next() {
		key := make([]byte, len(iter.Key()))
		copy(key, iter.Key())

		pathBytes := key[len(PATH_PRE):]

		// 检查是否需要修复
		if bytes.Contains(pathBytes, []byte("/")) || bytes.Contains(pathBytes, []byte("\\")) {
			keysToFix = append(keysToFix, key)
		}
	}

	// 执行修复
	for _, oldKey := range keysToFix {
		pathBytes := oldKey[len(PATH_PRE):]
		pathStr := string(pathBytes)

		// 提取文件名
		fileName := pathStr
		if lastSlash := bytes.LastIndexByte(pathBytes, '/'); lastSlash != -1 {
			fileName = string(pathBytes[lastSlash+1:])
		}
		if lastBackslash := bytes.LastIndexByte(pathBytes, '\\'); lastBackslash != -1 {
			fileName = string(pathBytes[lastBackslash+1:])
		}

		newKey := append(PATH_PRE, []byte(fileName)...)

		// 获取原始值
		value, err := db.Get(oldKey, nil)
		if err != nil {
			return fmt.Errorf("failed to get value for key: %v", err)
		}

		// 检查目标key是否已存在
		targetExists, err := db.Has(newKey, nil)
		if err != nil {
			return fmt.Errorf("failed to check target key existence: %v", err)
		}

		batch := new(leveldb.Batch)

		if targetExists {
			// 目标key已存在，只删除原始key
			batch.Delete(oldKey)
			log.Printf("Fixed key (target exists): %s -> %s (deleted old key only)", pathStr, fileName)
			deletedOnlyCount++
		} else {
			// 目标key不存在，执行正常的重命名操作
			batch.Put(newKey, value)
			batch.Delete(oldKey)
			log.Printf("Fixed key: %s -> %s", pathStr, fileName)
			normalFixCount++
		}

		if err := db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
			return fmt.Errorf("failed to write batch: %v", err)
		}

		fixedCount++
	}

	// 验证结果
	log.Printf("\nVerification:")
	log.Printf("Total keys fixed: %d", fixedCount)
	log.Printf("Keys deleted only (target existed): %d", deletedOnlyCount)
	log.Printf("Keys renamed normally: %d", normalFixCount)

	// 检查最终状态
	iter = db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	finalKeys := make([]string, 0)
	for iter.Next() {
		pathBytes := iter.Key()[len(PATH_PRE):]
		pathStr := string(pathBytes)
		finalKeys = append(finalKeys, pathStr)

		// 检查是否还有包含路径分隔符的key
		if bytes.Contains(pathBytes, []byte("/")) || bytes.Contains(pathBytes, []byte("\\")) {
			return fmt.Errorf("found unfixed key with path separator: %s", pathStr)
		}
	}

	log.Printf("\nFinal keys in database:")
	for _, key := range finalKeys {
		log.Printf("  %s", key)
	}

	// 验证预期结果
	expectedKeys := []string{"document.pdf", "image.jpg", "newfile.txt"}
	if len(finalKeys) != len(expectedKeys) {
		return fmt.Errorf("expected %d keys, got %d", len(expectedKeys), len(finalKeys))
	}

	log.Printf("\n✓ Target key existence check functionality verified successfully!")
	log.Printf("✓ Duplicate targets were handled correctly (old keys deleted)")
	log.Printf("✓ New targets were created correctly")
	log.Printf("✓ No path separators remain in keys")

	return nil
}

func main() {
	log.SetFlags(log.LstdFlags)
	log.Println("Testing target key existence functionality...")

	if err := testTargetKeyExists(); err != nil {
		log.Fatalf("Test failed: %v", err)
	}

	log.Println("All tests passed successfully!")
}
