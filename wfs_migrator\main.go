// WFS High-Performance Migration Tool
// 高并发WFS数据迁移工具，通过Thrift协议连接WFS服务
// 从指定存档文件夹读取数据，去除路径后写入新的WFS服务

package main

import (
	"context"
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"wfs_migrator/stub"

	"github.com/apache/thrift/lib/go/thrift"
	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义
var (
	PATH_PRE = []byte{0x00, 0x00} // PATH_PRE前缀
)

// 文件记录结构
type FileRecord struct {
	OriginalPath string
	FileName     string
	FileData     []byte
	Size         int64
}

// 迁移统计信息
type MigrationStats struct {
	TotalFiles     int64
	ProcessedFiles int64
	SuccessFiles   int64
	SkippedFiles   int64
	ErrorFiles     int64
	TotalBytes     int64
	ProcessedBytes int64
	StartTime      time.Time
}

// 迁移配置
type MigrationConfig struct {
	SourcePath   string // 源WFS存档文件夹路径
	WfsHost      string // WFS服务主机
	WfsPort      int    // WFS服务端口
	WorkerCount  int    // 工作线程数
	BufferSize   int    // 缓冲区大小
	BatchSize    int    // 批处理大小
	DryRun       bool   // 干运行模式
	SkipExisting bool   // 跳过已存在的文件
	ConnPoolSize int    // 连接池大小
}

// WFS客户端连接池
type WfsClientPool struct {
	clients chan *WfsClient
	config  *MigrationConfig
	mutex   sync.Mutex
}

// WFS客户端封装
type WfsClient struct {
	transport thrift.TTransport
	client    *stub.WfsIfaceClient
	connected bool
}

// WFS迁移器
type WfsMigrator struct {
	config     *MigrationConfig
	sourceDB   *leveldb.DB
	clientPool *WfsClientPool
	stats      *MigrationStats
	logger     *log.Logger
	workChan   chan *FileRecord
	wg         sync.WaitGroup
}

// 创建WFS客户端
func NewWfsClient(host string, port int) (*WfsClient, error) {
	addr := fmt.Sprintf("%s:%d", host, port)

	// 创建传输层
	var transport thrift.TTransport
	socket := thrift.NewTSocketConf(addr, &thrift.TConfiguration{
		ConnectTimeout: 30 * time.Second,
		SocketTimeout:  30 * time.Second,
	})
	transport = socket

	// 创建协议层
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()
	protocol := protocolFactory.GetProtocol(transport)

	// 创建客户端
	client := stub.NewWfsIfaceClient(thrift.NewTStandardClient(protocol, protocol))

	// 打开连接
	if err := transport.Open(); err != nil {
		return nil, fmt.Errorf("failed to open transport: %v", err)
	}

	return &WfsClient{
		transport: transport,
		client:    client,
		connected: true,
	}, nil
}

// 关闭WFS客户端
func (wc *WfsClient) Close() error {
	if wc.connected && wc.transport != nil {
		wc.connected = false
		return wc.transport.Close()
	}
	return nil
}

// 检查连接状态
func (wc *WfsClient) IsConnected() bool {
	if !wc.connected {
		return false
	}

	// 通过Ping检查连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := wc.client.Ping(ctx)
	return err == nil
}

// 创建WFS客户端连接池
func NewWfsClientPool(config *MigrationConfig) *WfsClientPool {
	return &WfsClientPool{
		clients: make(chan *WfsClient, config.ConnPoolSize),
		config:  config,
	}
}

// 获取客户端连接
func (pool *WfsClientPool) GetClient() (*WfsClient, error) {
	select {
	case client := <-pool.clients:
		if client.IsConnected() {
			return client, nil
		}
		// 连接已断开，创建新连接
		client.Close()
	default:
		// 池中没有可用连接，创建新连接
	}

	return NewWfsClient(pool.config.WfsHost, pool.config.WfsPort)
}

// 归还客户端连接
func (pool *WfsClientPool) ReturnClient(client *WfsClient) {
	if client == nil || !client.IsConnected() {
		if client != nil {
			client.Close()
		}
		return
	}

	select {
	case pool.clients <- client:
		// 成功归还到池中
	default:
		// 池已满，关闭连接
		client.Close()
	}
}

// 关闭连接池
func (pool *WfsClientPool) Close() {
	close(pool.clients)
	for client := range pool.clients {
		client.Close()
	}
}

// 创建WFS迁移器
func NewWfsMigrator(config *MigrationConfig) *WfsMigrator {
	return &WfsMigrator{
		config:     config,
		clientPool: NewWfsClientPool(config),
		stats:      &MigrationStats{StartTime: time.Now()},
		logger:     log.New(os.Stdout, "[WfsMigrator] ", log.LstdFlags),
		workChan:   make(chan *FileRecord, config.BufferSize),
	}
}

// 打开源数据库
func (wm *WfsMigrator) openSourceDB() error {
	dbPath := filepath.Join(wm.config.SourcePath, "wfsdb")

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     256 * 1024 * 1024, // 256MB
		WriteBuffer:            64 * 1024 * 1024,  // 64MB
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	var err error
	wm.sourceDB, err = leveldb.OpenFile(dbPath, options)
	if err != nil {
		wm.logger.Printf("Failed to open source DB normally, attempting recovery...")
		wm.sourceDB, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return fmt.Errorf("failed to open/recover source database: %v", err)
		}
		wm.logger.Printf("Source database recovered successfully")
	}

	wm.logger.Printf("Successfully opened source database: %s", dbPath)
	return nil
}

// 关闭资源
func (wm *WfsMigrator) cleanup() {
	if wm.sourceDB != nil {
		wm.sourceDB.Close()
	}
	if wm.clientPool != nil {
		wm.clientPool.Close()
	}
}

// 计算文件路径的MD5指纹
func (wm *WfsMigrator) calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

// 提取文件名（去除路径）
func (wm *WfsMigrator) extractFileName(path string) string {
	// 处理Windows和Unix路径分隔符
	path = strings.ReplaceAll(path, "\\", "/")
	return filepath.Base(path)
}

// 解析protobuf格式的WfsPathBean
func (wm *WfsMigrator) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		if i >= len(data) {
			break
		}

		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 扫描源数据库并收集文件信息
func (wm *WfsMigrator) scanSourceDatabase() error {
	wm.logger.Println("=== Scanning Source Database ===")

	// 扫描PATH_PRE索引获取文件路径
	pathPreFiles := make(map[int64]string)
	iter := wm.sourceDB.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		if len(key) <= len(PATH_PRE) {
			continue
		}

		originalPath := string(key[len(PATH_PRE):])
		var seqID int64
		if len(value) >= 8 {
			seqID = int64(binary.BigEndian.Uint64(value))
		} else {
			paddedValue := make([]byte, 8)
			copy(paddedValue[8-len(value):], value)
			seqID = int64(binary.BigEndian.Uint64(paddedValue))
		}

		pathPreFiles[seqID] = originalPath
		atomic.AddInt64(&wm.stats.TotalFiles, 1)
	}

	wm.logger.Printf("Found %d files in PATH_PRE index", len(pathPreFiles))

	// 为每个文件获取内容并发送到工作队列
	for _, originalPath := range pathPreFiles {
		fileName := wm.extractFileName(originalPath)

		// 获取文件内容
		fileData, err := wm.getFileContent(originalPath)
		if err != nil {
			wm.logger.Printf("Warning: Failed to get content for %s: %v", originalPath, err)
			continue
		}

		record := &FileRecord{
			OriginalPath: originalPath,
			FileName:     fileName,
			FileData:     fileData,
			Size:         int64(len(fileData)),
		}

		atomic.AddInt64(&wm.stats.TotalBytes, record.Size)
		wm.workChan <- record
	}

	close(wm.workChan)
	return iter.Error()
}

// 获取文件内容
func (wm *WfsMigrator) getFileContent(originalPath string) ([]byte, error) {
	// 计算原始路径的指纹
	fingerprint := wm.calculateFingerprint(originalPath)

	// 获取内容ID
	contentID, err := wm.sourceDB.Get(fingerprint, nil)
	if err != nil {
		return nil, fmt.Errorf("fingerprint not found: %v", err)
	}

	// 获取实际文件内容
	contentData, err := wm.sourceDB.Get(contentID, nil)
	if err != nil {
		return nil, fmt.Errorf("content data not found: %v", err)
	}

	return contentData, nil
}

// 工作线程处理函数
func (wm *WfsMigrator) worker(workerID int) {
	defer wm.wg.Done()

	wm.logger.Printf("Worker %d started", workerID)

	for record := range wm.workChan {
		if err := wm.processRecord(record, workerID); err != nil {
			wm.logger.Printf("Worker %d: Error processing %s: %v", workerID, record.OriginalPath, err)
			atomic.AddInt64(&wm.stats.ErrorFiles, 1)
		} else {
			atomic.AddInt64(&wm.stats.SuccessFiles, 1)
		}

		atomic.AddInt64(&wm.stats.ProcessedFiles, 1)
		atomic.AddInt64(&wm.stats.ProcessedBytes, record.Size)
	}

	wm.logger.Printf("Worker %d completed", workerID)
}

// 处理单个文件记录
func (wm *WfsMigrator) processRecord(record *FileRecord, workerID int) error {
	if wm.config.DryRun {
		wm.logger.Printf("DRY RUN Worker %d: Would migrate %s -> %s (%d bytes)",
			workerID, record.OriginalPath, record.FileName, record.Size)
		return nil
	}

	// 获取WFS客户端连接
	client, err := wm.clientPool.GetClient()
	if err != nil {
		return fmt.Errorf("failed to get WFS client: %v", err)
	}
	defer wm.clientPool.ReturnClient(client)

	// 检查文件是否已存在（如果启用跳过选项）
	if wm.config.SkipExisting {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		existResp, err := client.client.Exist(ctx, record.FileName)
		if err == nil && existResp != nil && existResp.Exists {
			wm.logger.Printf("Worker %d: Skipping existing file %s", workerID, record.FileName)
			atomic.AddInt64(&wm.stats.SkippedFiles, 1)
			return nil
		}
	}

	// 创建WfsFile结构
	wfsFile := &stub.WfsFile{
		Data:     record.FileData,
		Name:     record.FileName,
		Compress: nil, // 不压缩
	}

	// 调用Append接口上传文件
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	ack, err := client.client.Append(ctx, wfsFile)
	if err != nil {
		return fmt.Errorf("failed to append file: %v", err)
	}

	if ack == nil || !ack.Ok {
		errorMsg := "unknown error"
		if ack != nil && ack.Error != nil && ack.Error.Info != nil {
			errorMsg = *ack.Error.Info
		}
		return fmt.Errorf("append failed: %s", errorMsg)
	}

	wm.logger.Printf("Worker %d: Successfully migrated %s -> %s (%d bytes)",
		workerID, record.OriginalPath, record.FileName, record.Size)

	return nil
}

// 打印统计信息
func (wm *WfsMigrator) printStats() {
	elapsed := time.Since(wm.stats.StartTime)
	total := atomic.LoadInt64(&wm.stats.TotalFiles)
	processed := atomic.LoadInt64(&wm.stats.ProcessedFiles)
	success := atomic.LoadInt64(&wm.stats.SuccessFiles)
	skipped := atomic.LoadInt64(&wm.stats.SkippedFiles)
	errors := atomic.LoadInt64(&wm.stats.ErrorFiles)
	totalBytes := atomic.LoadInt64(&wm.stats.TotalBytes)
	processedBytes := atomic.LoadInt64(&wm.stats.ProcessedBytes)

	var speed float64
	if elapsed.Seconds() > 0 {
		speed = float64(processedBytes) / elapsed.Seconds()
	}

	wm.logger.Printf("Progress: %d/%d files, %d success, %d skipped, %d errors, %s/%s, %.1f MB/s, elapsed: %v",
		processed, total, success, skipped, errors,
		formatBytes(processedBytes), formatBytes(totalBytes),
		speed/(1024*1024), elapsed.Truncate(time.Second))
}

// 格式化字节数
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// 执行迁移
func (wm *WfsMigrator) Migrate() error {
	wm.logger.Println("=== Starting WFS High-Performance Migration ===")

	// 打开源数据库
	if err := wm.openSourceDB(); err != nil {
		return err
	}
	defer wm.cleanup()

	if wm.config.DryRun {
		wm.logger.Println("🔍 DRY RUN MODE - No actual changes will be made")
	}

	// 启动工作线程
	for i := 0; i < wm.config.WorkerCount; i++ {
		wm.wg.Add(1)
		go wm.worker(i)
	}

	// 启动统计信息打印协程
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				wm.printStats()
			default:
				if atomic.LoadInt64(&wm.stats.ProcessedFiles) >= atomic.LoadInt64(&wm.stats.TotalFiles) {
					return
				}
				time.Sleep(100 * time.Millisecond)
			}
		}
	}()

	// 扫描源数据库并发送到工作队列
	if err := wm.scanSourceDatabase(); err != nil {
		return err
	}

	// 等待所有工作线程完成
	wm.wg.Wait()

	// 打印最终统计信息
	wm.printStats()
	wm.logger.Println("✅ Migration completed successfully!")

	return nil
}

// 主函数
func main() {
	if len(os.Args) < 3 {
		fmt.Println("WFS High-Performance Migration Tool")
		fmt.Println("Usage: wfs_migrator <source_wfs_folder> <wfs_host:port> [options]")
		fmt.Println("")
		fmt.Println("Arguments:")
		fmt.Println("  source_wfs_folder : Source WFS archive folder (contains logs, wfsdb, wfsfile)")
		fmt.Println("  wfs_host:port     : Target WFS service address (e.g., localhost:9090)")
		fmt.Println("")
		fmt.Println("Options:")
		fmt.Println("  -workers <n>      : Number of worker threads (default: CPU count)")
		fmt.Println("  -buffer <n>       : Buffer size (default: 10000)")
		fmt.Println("  -batch <n>        : Batch size (default: 100)")
		fmt.Println("  -connections <n>  : Connection pool size (default: 10)")
		fmt.Println("  -dry-run          : Dry run mode (no actual changes)")
		fmt.Println("  -skip-existing    : Skip files that already exist")
		fmt.Println("")
		fmt.Println("Examples:")
		fmt.Println("  wfs_migrator /path/to/wfsdata localhost:9090 -workers 8 -skip-existing")
		fmt.Println("  wfs_migrator C:\\wfsdata *************:9090 -dry-run")
		os.Exit(1)
	}

	sourcePath := os.Args[1]
	hostPort := os.Args[2]

	// 解析主机和端口
	parts := strings.Split(hostPort, ":")
	if len(parts) != 2 {
		log.Fatalf("Invalid host:port format: %s", hostPort)
	}

	host := parts[0]
	port, err := strconv.Atoi(parts[1])
	if err != nil {
		log.Fatalf("Invalid port number: %s", parts[1])
	}

	config := &MigrationConfig{
		SourcePath:   sourcePath,
		WfsHost:      host,
		WfsPort:      port,
		WorkerCount:  runtime.NumCPU(),
		BufferSize:   10000,
		BatchSize:    100,
		DryRun:       false,
		SkipExisting: false,
		ConnPoolSize: 10,
	}

	// 解析参数
	for i := 3; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-workers":
			if i+1 < len(os.Args) {
				if n, err := strconv.Atoi(os.Args[i+1]); err == nil {
					config.WorkerCount = n
				}
				i++
			}
		case "-buffer":
			if i+1 < len(os.Args) {
				if n, err := strconv.Atoi(os.Args[i+1]); err == nil {
					config.BufferSize = n
				}
				i++
			}
		case "-batch":
			if i+1 < len(os.Args) {
				if n, err := strconv.Atoi(os.Args[i+1]); err == nil {
					config.BatchSize = n
				}
				i++
			}
		case "-connections":
			if i+1 < len(os.Args) {
				if n, err := strconv.Atoi(os.Args[i+1]); err == nil {
					config.ConnPoolSize = n
				}
				i++
			}
		case "-dry-run":
			config.DryRun = true
		case "-skip-existing":
			config.SkipExisting = true
		}
	}

	// 验证参数
	if config.WorkerCount <= 0 {
		config.WorkerCount = runtime.NumCPU()
	}
	if config.BufferSize <= 0 {
		config.BufferSize = 10000
	}
	if config.BatchSize <= 0 {
		config.BatchSize = 100
	}
	if config.ConnPoolSize <= 0 {
		config.ConnPoolSize = 10
	}

	// 验证源路径
	if _, err := os.Stat(sourcePath); os.IsNotExist(err) {
		log.Fatalf("Source path does not exist: %s", sourcePath)
	}

	dbPath := filepath.Join(sourcePath, "wfsdb")
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		log.Fatalf("Source wfsdb does not exist: %s", dbPath)
	}

	// 创建并运行迁移器
	migrator := NewWfsMigrator(config)
	if err := migrator.Migrate(); err != nil {
		log.Fatalf("Migration failed: %v", err)
	}
}
