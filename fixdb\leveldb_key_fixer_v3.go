// LevelDB Key Path Fixer for WFS System v3
// 修复WFS系统中LevelDB数据库的key路径问题
// 同时修复PATH_PRE和PATH_SEQ索引，使用protobuf格式

package main

import (
	"bytes"
	"context"
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 常量定义，与WFS系统保持一致
var (
	PATH_PRE = []byte{0, 0} // WFS系统中的PATH_PRE前缀
	PATH_SEQ = []byte{1, 0} // WFS系统中的PATH_SEQ前缀
)

// 简化的WfsPathBean结构（用于protobuf解析）
type WfsPathBean struct {
	Path       *string
	Timestramp *int64
}

// 统计信息
type Stats struct {
	TotalKeys     int64 // 总key数量
	ProcessedKeys int64 // 已处理key数量
	FixedKeys     int64 // 已修复key数量
	ErrorKeys     int64 // 错误key数量
	StartTime     time.Time
}

// 修复配置
type FixerConfig struct {
	DBPath      string // LevelDB数据库路径
	WorkerCount int    // 并发工作线程数
	BatchSize   int    // 批处理大小
	DryRun      bool   // 是否为试运行模式
}

// 修复器
type KeyFixer struct {
	config *FixerConfig
	db     *leveldb.DB
	stats  *Stats
	logger *log.Logger
}

// 创建新的修复器
func NewKeyFixer(config *FixerConfig) *KeyFixer {
	return &KeyFixer{
		config: config,
		stats: &Stats{
			StartTime: time.Now(),
		},
		logger: log.New(os.Stdout, "[KeyFixerV3] ", log.LstdFlags),
	}
}

// 打开LevelDB数据库
func (kf *KeyFixer) openDB() error {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     64 * 1024 * 1024, // 64MB
		WriteBuffer:            16 * 1024 * 1024, // 16MB
		ReadOnly:               kf.config.DryRun,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict, // 降低严格性以避免锁定问题
	}

	var err error
	kf.db, err = leveldb.OpenFile(kf.config.DBPath, options)
	if err != nil {
		// 如果打开失败，尝试恢复数据库
		kf.logger.Printf("Failed to open database normally, attempting recovery...")
		kf.db, err = leveldb.RecoverFile(kf.config.DBPath, options)
		if err != nil {
			return fmt.Errorf("failed to open/recover database: %v", err)
		}
		kf.logger.Printf("Database recovered successfully")
	}

	kf.logger.Printf("Successfully opened database: %s", kf.config.DBPath)
	return nil
}

// 关闭数据库
func (kf *KeyFixer) closeDB() error {
	if kf.db != nil {
		return kf.db.Close()
	}
	return nil
}

// 检查key是否需要修复
func (kf *KeyFixer) needsFix(key []byte) (bool, string) {
	// 检查是否是PATH_PRE前缀的key
	if len(key) <= len(PATH_PRE) || !bytes.HasPrefix(key, PATH_PRE) {
		return false, ""
	}

	// 提取路径部分
	pathBytes := key[len(PATH_PRE):]
	pathStr := string(pathBytes)

	// 检查是否包含路径分隔符（表示是全路径）
	if strings.Contains(pathStr, "/") || strings.Contains(pathStr, "\\") {
		// 提取文件名（最后一个路径分隔符后的部分）
		fileName := filepath.Base(pathStr)
		return true, fileName
	}

	return false, ""
}

// 简化的protobuf解析（只解析我们需要的字段）
func (kf *KeyFixer) parseWfsPathBean(data []byte) (string, int64, error) {
	// 这是一个简化的protobuf解析器，只解析Path和Timestramp字段
	// protobuf格式：tag(varint) + length(varint) + data

	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 简化的protobuf编码
func (kf *KeyFixer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var buf []byte

	// 编码Path字段 (field 1, wire type 2)
	pathBytes := []byte(path)
	tag1 := (1 << 3) | 2 // field 1, wire type 2 (length-delimited)
	buf = append(buf, byte(tag1))

	// 编码长度
	lengthBuf := make([]byte, binary.MaxVarintLen64)
	n := binary.PutUvarint(lengthBuf, uint64(len(pathBytes)))
	buf = append(buf, lengthBuf[:n]...)

	// 编码路径数据
	buf = append(buf, pathBytes...)

	// 编码Timestramp字段 (field 2, wire type 0)
	tag2 := (2 << 3) | 0 // field 2, wire type 0 (varint)
	buf = append(buf, byte(tag2))

	// 编码时间戳
	timestampBuf := make([]byte, binary.MaxVarintLen64)
	n = binary.PutVarint(timestampBuf, timestamp)
	buf = append(buf, timestampBuf[:n]...)

	return buf
}

// 修复单个key（同时修复PATH_PRE和PATH_SEQ）
func (kf *KeyFixer) fixKey(oldKey []byte, newFileName string) error {
	// 构造新的PATH_PRE key
	newKey := append(PATH_PRE, []byte(newFileName)...)

	// 获取原始值（序列号ID）
	seqIDBytes, err := kf.db.Get(oldKey, nil)
	if err != nil {
		return fmt.Errorf("failed to get sequence ID for key: %v", err)
	}

	// 构造PATH_SEQ key
	pathSeqKey := append(PATH_SEQ, seqIDBytes...)

	// 获取WfsPathBean数据
	pathBeanData, err := kf.db.Get(pathSeqKey, nil)
	if err != nil {
		return fmt.Errorf("failed to get path bean data: %v", err)
	}

	if kf.config.DryRun {
		// 尝试解析WfsPathBean
		oldPath, _, err := kf.parseWfsPathBean(pathBeanData)
		if err != nil {
			kf.logger.Printf("DRY RUN: Would fix key: %s -> %s (failed to parse PathBean: %v)",
				string(oldKey[len(PATH_PRE):]), newFileName, err)
		} else {
			// 检查目标key是否已存在
			targetExists, _ := kf.db.Has(newKey, nil)
			if targetExists {
				kf.logger.Printf("DRY RUN: Would fix key: %s -> %s (target exists, would delete old key only, PathBean: %s)",
					string(oldKey[len(PATH_PRE):]), newFileName, oldPath)
			} else {
				kf.logger.Printf("DRY RUN: Would fix key: %s -> %s (would update both PATH_PRE and PATH_SEQ, PathBean: %s -> %s)",
					string(oldKey[len(PATH_PRE):]), newFileName, oldPath, newFileName)
			}
		}
		return nil
	}

	// 解析现有的WfsPathBean
	oldPath, timestamp, err := kf.parseWfsPathBean(pathBeanData)
	if err != nil {
		return fmt.Errorf("failed to parse WfsPathBean: %v", err)
	}

	// 创建批处理操作
	batch := new(leveldb.Batch)

	// 检查目标key是否已存在
	targetExists, err := kf.db.Has(newKey, nil)
	if err != nil {
		return fmt.Errorf("failed to check target key existence: %v", err)
	}

	if targetExists {
		// 目标key已存在，只删除原始PATH_PRE key
		batch.Delete(oldKey)
		kf.logger.Printf("Fixed key (target exists): %s -> %s (deleted old PATH_PRE key only, PathBean: %s)",
			string(oldKey[len(PATH_PRE):]), newFileName, oldPath)
	} else {
		// 目标key不存在，执行完整的修复操作

		// 1. 更新PATH_PRE索引
		batch.Put(newKey, seqIDBytes)
		batch.Delete(oldKey)

		// 2. 更新PATH_SEQ索引中的WfsPathBean
		newPathBeanData := kf.encodeWfsPathBean(newFileName, timestamp)
		batch.Put(pathSeqKey, newPathBeanData)

		kf.logger.Printf("Fixed key: %s -> %s (updated both PATH_PRE and PATH_SEQ, PathBean: %s -> %s)",
			string(oldKey[len(PATH_PRE):]), newFileName, oldPath, newFileName)
	}

	// 执行批处理
	if err := kf.db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
		return fmt.Errorf("failed to write batch: %v", err)
	}

	return nil
}

// 工作线程处理函数
func (kf *KeyFixer) worker(ctx context.Context, keysChan <-chan []byte, wg *sync.WaitGroup) {
	defer wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case key, ok := <-keysChan:
			if !ok {
				return
			}

			atomic.AddInt64(&kf.stats.ProcessedKeys, 1)

			if needsFix, newFileName := kf.needsFix(key); needsFix {
				if err := kf.fixKey(key, newFileName); err != nil {
					kf.logger.Printf("Error fixing key %s: %v", string(key), err)
					atomic.AddInt64(&kf.stats.ErrorKeys, 1)
				} else {
					atomic.AddInt64(&kf.stats.FixedKeys, 1)
				}
			}
		}
	}
}

// 打印统计信息
func (kf *KeyFixer) printStats() {
	elapsed := time.Since(kf.stats.StartTime)
	processed := atomic.LoadInt64(&kf.stats.ProcessedKeys)
	fixed := atomic.LoadInt64(&kf.stats.FixedKeys)
	errors := atomic.LoadInt64(&kf.stats.ErrorKeys)
	total := atomic.LoadInt64(&kf.stats.TotalKeys)

	kf.logger.Printf("Progress: %d/%d processed, %d fixed, %d errors, elapsed: %v",
		processed, total, fixed, errors, elapsed.Truncate(time.Second))
}

// 启动修复过程
func (kf *KeyFixer) Fix() error {
	kf.logger.Println("Starting LevelDB key path fix process (v3 - with protobuf PATH_SEQ support)...")

	// 打开数据库
	if err := kf.openDB(); err != nil {
		return err
	}
	defer kf.closeDB()

	kf.logger.Println("Backup disabled for v3 (protobuf version)")

	// 创建上下文和取消函数
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建工作线程
	keysChan := make(chan []byte, kf.config.BatchSize)
	var wg sync.WaitGroup

	for i := 0; i < kf.config.WorkerCount; i++ {
		wg.Add(1)
		go kf.worker(ctx, keysChan, &wg)
	}

	// 启动统计信息打印协程
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				kf.printStats()
			}
		}
	}()

	// 遍历所有PATH_PRE前缀的key
	iter := kf.db.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	kf.logger.Println("Scanning database for keys to fix...")

	for iter.Next() {
		key := make([]byte, len(iter.Key()))
		copy(key, iter.Key())

		atomic.AddInt64(&kf.stats.TotalKeys, 1)
		keysChan <- key
	}

	// 关闭通道并等待工作线程完成
	close(keysChan)
	wg.Wait()

	// 检查迭代器错误
	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	// 打印最终统计信息
	kf.printStats()
	kf.logger.Println("Fix process completed successfully!")

	return nil
}

// 主函数
func main() {
	// 解析命令行参数
	if len(os.Args) < 2 {
		fmt.Println("Usage: leveldb_key_fixer_v3 <db_path> [options]")
		fmt.Println("Options:")
		fmt.Println("  -workers <num>    : Number of worker threads (default: CPU count)")
		fmt.Println("  -batch <size>     : Batch size (default: 1000)")
		fmt.Println("  -dry-run          : Dry run mode (no actual changes)")
		os.Exit(1)
	}

	config := &FixerConfig{
		DBPath:      os.Args[1],
		WorkerCount: runtime.NumCPU(),
		BatchSize:   1000,
		DryRun:      false,
	}

	// 解析其他参数
	for i := 2; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-workers":
			if i+1 < len(os.Args) {
				fmt.Sscanf(os.Args[i+1], "%d", &config.WorkerCount)
				i++
			}
		case "-batch":
			if i+1 < len(os.Args) {
				fmt.Sscanf(os.Args[i+1], "%d", &config.BatchSize)
				i++
			}
		case "-dry-run":
			config.DryRun = true
		}
	}

	// 创建并运行修复器
	fixer := NewKeyFixer(config)
	if err := fixer.Fix(); err != nil {
		log.Fatalf("Fix failed: %v", err)
	}
}
