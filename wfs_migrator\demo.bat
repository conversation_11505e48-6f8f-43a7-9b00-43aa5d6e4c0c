@echo off
REM WFS Migration Tool Demo Script
REM 演示WFS迁移工具的完整功能

echo ========================================
echo WFS High-Performance Migration Tool Demo
echo ========================================

REM 设置变量
set SOURCE_WFS_DATA=..\wfsdata
set TARGET_WFS_HOST=localhost:9090
set WORKERS=4

echo.
echo Step 1: Checking source data...
if not exist "%SOURCE_WFS_DATA%" (
    echo Error: Source WFS data not found: %SOURCE_WFS_DATA%
    pause
    exit /b 1
)

if not exist "%SOURCE_WFS_DATA%\wfsdb" (
    echo Error: Source wfsdb not found: %SOURCE_WFS_DATA%\wfsdb
    pause
    exit /b 1
)

echo ✅ Source data found: %SOURCE_WFS_DATA%

echo.
echo Step 2: Preview migration (dry-run)...
echo Command: wfs_migrator.exe "%SOURCE_WFS_DATA%" "%TARGET_WFS_HOST%" -dry-run -workers %WORKERS%
echo.
wfs_migrator.exe "%SOURCE_WFS_DATA%" "%TARGET_WFS_HOST%" -dry-run -workers %WORKERS%

echo.
echo ========================================
echo Dry-run completed!
echo ========================================
echo.
echo The above shows what files would be migrated:
echo - Files with paths will be converted to file names only
echo - Example: "folder\file.jpg" becomes "file.jpg"
echo.
echo To perform actual migration:
echo 1. Start target WFS service on %TARGET_WFS_HOST%
echo 2. Run: wfs_migrator.exe "%SOURCE_WFS_DATA%" "%TARGET_WFS_HOST%" -workers %WORKERS% -skip-existing
echo.
echo Additional options:
echo   -workers ^<n^>      : Number of worker threads
echo   -skip-existing    : Skip files that already exist
echo   -connections ^<n^>  : Connection pool size
echo.
pause
