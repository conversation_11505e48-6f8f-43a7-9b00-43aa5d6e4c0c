// WFS路径清理工具 - 严格按照Thrift Rename接口逻辑
// 简化版本：检测路径分隔符，重命名或删除冲突文件

package main

import (
	"bytes"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"sync/atomic"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00} // PATH_PRE前缀
	PATH_SEQ = []byte{0x01, 0x00} // PATH_SEQ前缀
)

// 配置
type CleanerConfig struct {
	DatabasePath string
	Workers      int
	DryRun       bool
	Verbose      bool
}

// 清理任务
type CleanTask struct {
	OriginalPath string
	TargetPath   string
	Action       string // "rename" or "delete"
	SeqID        []byte
}

// 清理结果
type CleanResult struct {
	Task    CleanTask
	Success bool
	Error   error
}

// 路径清理器
type PathCleaner struct {
	config     *CleanerConfig
	db         *leveldb.DB
	logger     *log.Logger
	taskChan   chan CleanTask
	resultChan chan CleanResult
	stats      struct {
		TotalTasks int64
		Renamed    int64
		Deleted    int64
		Errors     int64
	}
}

// 创建路径清理器
func NewPathCleaner(config *CleanerConfig) (*PathCleaner, error) {
	logger := log.New(os.Stdout, "[PathCleaner] ", log.LstdFlags)

	// 打开数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &PathCleaner{
		config:     config,
		db:         db,
		logger:     logger,
		taskChan:   make(chan CleanTask, config.Workers*2),
		resultChan: make(chan CleanResult, config.Workers*2),
	}, nil
}

// 检查路径是否包含分隔符
func (pc *PathCleaner) hasPathSeparator(path string) bool {
	return strings.Contains(path, "\\") || strings.Contains(path, "/")
}

// 提取文件名
func (pc *PathCleaner) extractFileName(path string) string {
	path = strings.ReplaceAll(path, "\\", "/")
	parts := strings.Split(path, "/")
	return parts[len(parts)-1]
}

// 生成清理任务
func (pc *PathCleaner) generateTasks() ([]CleanTask, error) {
	pc.logger.Println("Scanning database for path cleanup tasks...")

	var tasks []CleanTask
	existingFiles := make(map[string]bool)

	// 第一遍：收集所有纯文件名
	iter := pc.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				if !pc.hasPathSeparator(path) {
					existingFiles[path] = true
				}
			}
		}
	}

	// 第二遍：生成清理任务
	iter = pc.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				originalPath := string(key[2:])

				// 只处理包含路径分隔符的文件
				if pc.hasPathSeparator(originalPath) {
					fileName := pc.extractFileName(originalPath)
					seqID := iter.Value()

					var task CleanTask
					task.OriginalPath = originalPath
					task.SeqID = seqID

					// 检查目标文件是否已存在
					if existingFiles[fileName] {
						// 目标已存在，删除源文件
						task.Action = "delete"
						task.TargetPath = ""
					} else {
						// 正常重命名
						task.Action = "rename"
						task.TargetPath = fileName
						existingFiles[fileName] = true
					}

					tasks = append(tasks, task)
				}
			}
		}
	}

	pc.logger.Printf("Generated %d cleanup tasks", len(tasks))
	return tasks, nil
}

// 工作线程
func (pc *PathCleaner) worker(workerID int) {
	pc.logger.Printf("Worker %d started", workerID)
	defer pc.logger.Printf("Worker %d completed", workerID)

	for task := range pc.taskChan {
		result := pc.processTask(workerID, task)
		pc.resultChan <- result
	}
}

// 处理任务
func (pc *PathCleaner) processTask(workerID int, task CleanTask) CleanResult {
	if pc.config.Verbose {
		pc.logger.Printf("Worker %d: Processing %s (%s)", workerID, task.OriginalPath, task.Action)
	}

	if task.Action == "delete" {
		return pc.deleteFile(workerID, task)
	} else {
		return pc.renameFile(workerID, task)
	}
}

// 删除文件
func (pc *PathCleaner) deleteFile(workerID int, task CleanTask) CleanResult {
	if pc.config.DryRun {
		pc.logger.Printf("Worker %d: [DRY RUN] Would delete %s (conflict)", workerID, task.OriginalPath)
		return CleanResult{Task: task, Success: true}
	}

	// 删除PATH_PRE条目
	oldKey := append(PATH_PRE, []byte(task.OriginalPath)...)
	if err := pc.db.Delete(oldKey, nil); err != nil {
		return CleanResult{Task: task, Success: false, Error: err}
	}

	pc.logger.Printf("Worker %d: Deleted %s (conflict resolution)", workerID, task.OriginalPath)
	return CleanResult{Task: task, Success: true}
}

// 重命名文件
func (pc *PathCleaner) renameFile(workerID int, task CleanTask) CleanResult {
	if pc.config.DryRun {
		pc.logger.Printf("Worker %d: [DRY RUN] Would rename %s -> %s",
			workerID, task.OriginalPath, task.TargetPath)
		return CleanResult{Task: task, Success: true}
	}

	// 更新PATH_PRE索引
	oldKey := append(PATH_PRE, []byte(task.OriginalPath)...)
	newKey := append(PATH_PRE, []byte(task.TargetPath)...)

	batch := new(leveldb.Batch)
	batch.Put(newKey, task.SeqID)
	batch.Delete(oldKey)

	if err := pc.db.Write(batch, nil); err != nil {
		return CleanResult{Task: task, Success: false, Error: err}
	}

	// 更新0x0800索引（WFS网页显示用）
	if err := pc.update0800Index(task); err != nil {
		pc.logger.Printf("Worker %d: Warning: Failed to update 0x0800 index: %v", workerID, err)
	}

	pc.logger.Printf("Worker %d: Renamed %s -> %s", workerID, task.OriginalPath, task.TargetPath)
	return CleanResult{Task: task, Success: true}
}

// 更新0x0800索引
func (pc *PathCleaner) update0800Index(task CleanTask) error {
	prefix0800 := []byte{0x08, 0x00}

	iter := pc.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if !bytes.HasPrefix(key, prefix0800) {
			break
		}

		if bytes.HasSuffix(key, task.SeqID) {
			value := iter.Value()

			// 解析并更新WfsPathBean
			oldPath, timestamp, err := pc.parseWfsPathBean(value)
			if err != nil {
				continue
			}

			if oldPath == task.OriginalPath {
				newData := pc.encodeWfsPathBean(task.TargetPath, timestamp)
				return pc.db.Put(key, newData, nil)
			}
		}
	}

	return nil
}

// 简化的protobuf解析
func (pc *PathCleaner) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 编码WfsPathBean
func (pc *PathCleaner) encodeWfsPathBean(path string, timestamp int64) []byte {
	var result []byte

	// Path字段
	if path != "" {
		result = append(result, 0x0A)
		pathBytes := []byte(path)
		result = append(result, byte(len(pathBytes)))
		result = append(result, pathBytes...)
	}

	// Timestamp字段
	if timestamp != 0 {
		result = append(result, 0x10)
		zigzag := uint64((timestamp << 1) ^ (timestamp >> 63))
		for zigzag >= 0x80 {
			result = append(result, byte(zigzag)|0x80)
			zigzag >>= 7
		}
		result = append(result, byte(zigzag))
	}

	return result
}

// 结果处理器
func (pc *PathCleaner) resultProcessor() {
	for result := range pc.resultChan {
		if result.Success {
			if result.Task.Action == "delete" {
				atomic.AddInt64(&pc.stats.Deleted, 1)
			} else {
				atomic.AddInt64(&pc.stats.Renamed, 1)
			}
		} else {
			atomic.AddInt64(&pc.stats.Errors, 1)
			pc.logger.Printf("Error: %v", result.Error)
		}
	}
}

// 执行清理
func (pc *PathCleaner) Execute() error {
	pc.logger.Println("=== WFS Path Cleanup Started ===")

	// 生成任务
	tasks, err := pc.generateTasks()
	if err != nil {
		return err
	}

	if len(tasks) == 0 {
		pc.logger.Println("No files with path separators found")
		return nil
	}

	pc.stats.TotalTasks = int64(len(tasks))

	// 启动工作线程
	var wg sync.WaitGroup
	for i := 0; i < pc.config.Workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			pc.worker(workerID)
		}(i)
	}

	// 启动结果处理器
	go pc.resultProcessor()

	// 发送任务
	go func() {
		defer close(pc.taskChan)
		for _, task := range tasks {
			pc.taskChan <- task
		}
	}()

	// 等待完成
	wg.Wait()
	close(pc.resultChan)

	// 打印统计
	pc.logger.Printf("=== Cleanup Complete ===")
	pc.logger.Printf("Total tasks: %d", pc.stats.TotalTasks)
	pc.logger.Printf("Renamed: %d", pc.stats.Renamed)
	pc.logger.Printf("Deleted: %d", pc.stats.Deleted)
	pc.logger.Printf("Errors: %d", pc.stats.Errors)

	return nil
}

// 关闭
func (pc *PathCleaner) Close() {
	if pc.db != nil {
		pc.db.Close()
	}
}

// 主函数
func main() {
	config := &CleanerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.IntVar(&config.Workers, "workers", 8, "Number of workers")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	cleaner, err := NewPathCleaner(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer cleaner.Close()

	if err := cleaner.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
