// Create Test Files for WFS Migration Demo
// 为WFS迁移演示创建测试文件

package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/apache/thrift/lib/go/thrift"
	"wfs_migrator/stub"
)

func main() {
	// 连接到目标WFS服务
	socket := thrift.NewTSocketConf("localhost:9090", &thrift.TConfiguration{
		ConnectTimeout: 10 * time.Second,
		SocketTimeout:  10 * time.Second,
	})
	
	transport := socket
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()
	protocol := protocolFactory.GetProtocol(transport)
	client := stub.NewWfsIfaceClient(thrift.NewTStandardClient(protocol, protocol))
	
	if err := transport.Open(); err != nil {
		log.Fatalf("Failed to connect to WFS service: %v", err)
	}
	defer transport.Close()
	
	fmt.Println("Connected to WFS service, creating test files...")
	
	// 创建测试文件
	testFiles := []struct {
		name    string
		content string
	}{
		{"demo1.txt", "This is demo file 1 content"},
		{"demo2.txt", "This is demo file 2 content"},
		{"test.jpg", "Fake JPEG content for testing"},
	}
	
	for _, file := range testFiles {
		wfsFile := &stub.WfsFile{
			Data: []byte(file.content),
			Name: file.name,
		}
		
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		ack, err := client.Append(ctx, wfsFile)
		cancel()
		
		if err != nil {
			log.Printf("Failed to upload %s: %v", file.name, err)
			continue
		}
		
		if ack == nil || !ack.Ok {
			log.Printf("Upload failed for %s", file.name)
			continue
		}
		
		fmt.Printf("✅ Created test file: %s (%d bytes)\n", file.name, len(file.content))
	}
	
	fmt.Println("Test files created successfully!")
}
