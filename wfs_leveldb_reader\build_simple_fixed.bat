@echo off
chcp 65001 >nul

echo WFS LevelDB Reader - Fixed Build
echo =================================

echo Checking CMake...
cmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: CMake not found
    pause
    exit /b 1
)

echo CMake OK

echo Checking vcpkg...
if not exist "C:\dev\vcpkg\vcpkg.exe" (
    echo Error: vcpkg not found
    pause
    exit /b 1
)

echo vcpkg OK

set BUILD_DIR=C:\VisualStudioRollback\CMakeBuild_Temp\wfs_leveldb_reader_fixed

if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

if not exist "redist_desk" mkdir "redist_desk"

echo.
echo Configuring...
cd "%BUILD_DIR%"
cmake "%~dp0" -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake -DCMAKE_BUILD_TYPE=Release

if errorlevel 1 (
    echo Configuration failed
    pause
    exit /b 1
)

echo.
echo Building...
cmake --build . --config Release

if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo Installing...
cmake --install . --config Release

echo.
echo Build completed successfully!
echo Executable: redist_desk\wfs_leveldb_reader.exe
echo.
pause
