// WFS数据库比较工具 - 比较两个数据库的差异
package main

import (
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"
	"sort"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE_CMP = []byte{0x00, 0x00}
	PATH_SEQ_CMP = []byte{0x01, 0x00}
)

type ComparatorConfig struct {
	Database1Path string
	Database2Path string
	Verbose       bool
	ShowContent   bool
}

type DatabaseEntry struct {
	Key   string
	Value string
	Size  int
}

type WFSDBComparator struct {
	config *ComparatorConfig
	db1    *leveldb.DB
	db2    *leveldb.DB
	logger *log.Logger
}

func NewWFSDBComparator(config *ComparatorConfig) (*WFSDBComparator, error) {
	logger := log.New(os.Stdo<PERSON>, "[WFSDBComparator] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db1, err := leveldb.OpenFile(config.Database1Path, options)
	if err != nil {
		logger.Printf("Failed to open database1 normally, attempting recovery...")
		db1, err = leveldb.RecoverFile(config.Database1Path, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database1: %v", err)
		}
	}

	db2, err := leveldb.OpenFile(config.Database2Path, options)
	if err != nil {
		logger.Printf("Failed to open database2 normally, attempting recovery...")
		db2, err = leveldb.RecoverFile(config.Database2Path, options)
		if err != nil {
			db1.Close()
			return nil, fmt.Errorf("failed to open/recover database2: %v", err)
		}
	}

	return &WFSDBComparator{
		config: config,
		db1:    db1,
		db2:    db2,
		logger: logger,
	}, nil
}

func (wdc *WFSDBComparator) Compare() error {
	wdc.logger.Println("=== WFS Database Comparison ===")
	wdc.logger.Printf("Database 1: %s", wdc.config.Database1Path)
	wdc.logger.Printf("Database 2: %s", wdc.config.Database2Path)

	// 1. 收集两个数据库的所有条目
	entries1 := wdc.collectAllEntries(wdc.db1, "DB1")
	entries2 := wdc.collectAllEntries(wdc.db2, "DB2")

	// 2. 比较条目数量
	wdc.compareEntryCounts(entries1, entries2)

	// 3. 比较PATH_PRE索引
	wdc.comparePATH_PRE(entries1, entries2)

	// 4. 比较PATH_SEQ索引
	wdc.comparePATH_SEQ(entries1, entries2)

	// 5. 比较0x0800索引
	wdc.compare0x0800(entries1, entries2)

	// 6. 比较指纹索引
	wdc.compareFingerprints(entries1, entries2)

	// 7. 比较其他条目
	wdc.compareOtherEntries(entries1, entries2)

	return nil
}

func (wdc *WFSDBComparator) collectAllEntries(db *leveldb.DB, dbName string) map[string]*DatabaseEntry {
	wdc.logger.Printf("Collecting all entries from %s...", dbName)

	entries := make(map[string]*DatabaseEntry)
	iter := db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		keyHex := hex.EncodeToString(key)
		valueHex := hex.EncodeToString(value)

		entries[keyHex] = &DatabaseEntry{
			Key:   keyHex,
			Value: valueHex,
			Size:  len(value),
		}

		count++
		if wdc.config.Verbose && count <= 10 {
			wdc.logger.Printf("%s Entry %d: key=%s, valueSize=%d", dbName, count, keyHex, len(value))
		}
	}

	wdc.logger.Printf("%s has %d entries", dbName, count)
	return entries
}

func (wdc *WFSDBComparator) compareEntryCounts(entries1, entries2 map[string]*DatabaseEntry) {
	wdc.logger.Printf("\n=== Entry Count Comparison ===")
	wdc.logger.Printf("DB1 entries: %d", len(entries1))
	wdc.logger.Printf("DB2 entries: %d", len(entries2))

	if len(entries1) == len(entries2) {
		wdc.logger.Printf("✅ Entry counts match")
	} else {
		wdc.logger.Printf("❌ Entry counts differ by %d", abs(len(entries1)-len(entries2)))
	}
}

func (wdc *WFSDBComparator) comparePATH_PRE(entries1, entries2 map[string]*DatabaseEntry) {
	wdc.logger.Printf("\n=== PATH_PRE Index Comparison ===")

	pathPre1 := wdc.filterByPrefix(entries1, "0000")
	pathPre2 := wdc.filterByPrefix(entries2, "0000")

	wdc.logger.Printf("DB1 PATH_PRE entries: %d", len(pathPre1))
	wdc.logger.Printf("DB2 PATH_PRE entries: %d", len(pathPre2))

	// 比较具体条目
	wdc.compareSpecificEntries("PATH_PRE", pathPre1, pathPre2)
}

func (wdc *WFSDBComparator) comparePATH_SEQ(entries1, entries2 map[string]*DatabaseEntry) {
	wdc.logger.Printf("\n=== PATH_SEQ Index Comparison ===")

	pathSeq1 := wdc.filterByPrefix(entries1, "0100")
	pathSeq2 := wdc.filterByPrefix(entries2, "0100")

	wdc.logger.Printf("DB1 PATH_SEQ entries: %d", len(pathSeq1))
	wdc.logger.Printf("DB2 PATH_SEQ entries: %d", len(pathSeq2))

	// 比较具体条目
	wdc.compareSpecificEntries("PATH_SEQ", pathSeq1, pathSeq2)
}

func (wdc *WFSDBComparator) compare0x0800(entries1, entries2 map[string]*DatabaseEntry) {
	wdc.logger.Printf("\n=== 0x0800 Index Comparison ===")

	index0800_1 := wdc.filterByPrefix(entries1, "0800")
	index0800_2 := wdc.filterByPrefix(entries2, "0800")

	wdc.logger.Printf("DB1 0x0800 entries: %d", len(index0800_1))
	wdc.logger.Printf("DB2 0x0800 entries: %d", len(index0800_2))

	// 比较具体条目
	wdc.compareSpecificEntries("0x0800", index0800_1, index0800_2)
}

func (wdc *WFSDBComparator) compareFingerprints(entries1, entries2 map[string]*DatabaseEntry) {
	wdc.logger.Printf("\n=== Fingerprint Index Comparison ===")

	// 指纹索引通常是16字节的MD5哈希
	fingerprints1 := wdc.filterByKeyLength(entries1, 32) // 16字节 = 32个hex字符
	fingerprints2 := wdc.filterByKeyLength(entries2, 32)

	wdc.logger.Printf("DB1 potential fingerprint entries: %d", len(fingerprints1))
	wdc.logger.Printf("DB2 potential fingerprint entries: %d", len(fingerprints2))

	// 比较具体条目
	wdc.compareSpecificEntries("Fingerprint", fingerprints1, fingerprints2)
}

func (wdc *WFSDBComparator) compareOtherEntries(entries1, entries2 map[string]*DatabaseEntry) {
	wdc.logger.Printf("\n=== Other Entries Comparison ===")

	// 找出只在DB1中存在的条目
	onlyInDB1 := make(map[string]*DatabaseEntry)
	for key, entry := range entries1 {
		if _, exists := entries2[key]; !exists {
			onlyInDB1[key] = entry
		}
	}

	// 找出只在DB2中存在的条目
	onlyInDB2 := make(map[string]*DatabaseEntry)
	for key, entry := range entries2 {
		if _, exists := entries1[key]; !exists {
			onlyInDB2[key] = entry
		}
	}

	wdc.logger.Printf("Entries only in DB1: %d", len(onlyInDB1))
	wdc.logger.Printf("Entries only in DB2: %d", len(onlyInDB2))

	if wdc.config.Verbose {
		wdc.logger.Printf("\nEntries only in DB1:")
		wdc.printEntries(onlyInDB1, 10)

		wdc.logger.Printf("\nEntries only in DB2:")
		wdc.printEntries(onlyInDB2, 10)
	}
}

func (wdc *WFSDBComparator) compareSpecificEntries(indexName string, entries1, entries2 map[string]*DatabaseEntry) {
	// 找出差异
	different := make(map[string][2]*DatabaseEntry)
	onlyInDB1 := make(map[string]*DatabaseEntry)
	onlyInDB2 := make(map[string]*DatabaseEntry)

	for key, entry1 := range entries1 {
		if entry2, exists := entries2[key]; exists {
			if entry1.Value != entry2.Value {
				different[key] = [2]*DatabaseEntry{entry1, entry2}
			}
		} else {
			onlyInDB1[key] = entry1
		}
	}

	for key, entry2 := range entries2 {
		if _, exists := entries1[key]; !exists {
			onlyInDB2[key] = entry2
		}
	}

	wdc.logger.Printf("%s - Different values: %d", indexName, len(different))
	wdc.logger.Printf("%s - Only in DB1: %d", indexName, len(onlyInDB1))
	wdc.logger.Printf("%s - Only in DB2: %d", indexName, len(onlyInDB2))

	if wdc.config.Verbose && len(different) > 0 {
		wdc.logger.Printf("\n%s differences:", indexName)
		count := 0
		for key, entries := range different {
			if count >= 5 {
				break
			}
			wdc.logger.Printf("  Key: %s", key)
			wdc.logger.Printf("    DB1: %s", entries[0].Value)
			wdc.logger.Printf("    DB2: %s", entries[1].Value)
			count++
		}
	}
}

func (wdc *WFSDBComparator) filterByPrefix(entries map[string]*DatabaseEntry, prefix string) map[string]*DatabaseEntry {
	filtered := make(map[string]*DatabaseEntry)
	for key, entry := range entries {
		if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
			filtered[key] = entry
		}
	}
	return filtered
}

func (wdc *WFSDBComparator) filterByKeyLength(entries map[string]*DatabaseEntry, length int) map[string]*DatabaseEntry {
	filtered := make(map[string]*DatabaseEntry)
	for key, entry := range entries {
		if len(key) == length {
			filtered[key] = entry
		}
	}
	return filtered
}

func (wdc *WFSDBComparator) printEntries(entries map[string]*DatabaseEntry, limit int) {
	keys := make([]string, 0, len(entries))
	for key := range entries {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	count := 0
	for _, key := range keys {
		if count >= limit {
			wdc.logger.Printf("  ... and %d more", len(keys)-limit)
			break
		}
		entry := entries[key]
		wdc.logger.Printf("  %s (size: %d)", key, entry.Size)
		if wdc.config.ShowContent && entry.Size < 100 {
			wdc.logger.Printf("    Value: %s", entry.Value)
		}
		count++
	}
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func (wdc *WFSDBComparator) Close() {
	if wdc.db1 != nil {
		wdc.db1.Close()
	}
	if wdc.db2 != nil {
		wdc.db2.Close()
	}
}

func main() {
	config := &ComparatorConfig{}

	flag.StringVar(&config.Database1Path, "db1", "", "First database path (required)")
	flag.StringVar(&config.Database2Path, "db2", "", "Second database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.BoolVar(&config.ShowContent, "show-content", false, "Show entry content")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Database Comparator\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db1 C:\\wfsdata\\wfsdb -db2 C:\\wfsdata_new\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.Database1Path == "" || config.Database2Path == "" {
		fmt.Fprintf(os.Stderr, "Error: Both -db1 and -db2 parameters are required\n")
		flag.Usage()
		os.Exit(1)
	}

	comparator, err := NewWFSDBComparator(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer comparator.Close()

	if err := comparator.Compare(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
