// WFS内容分析工具 - 分析指纹索引和WfsFileBean
package main

import (
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

var (
	PATH_PRE_CONTENT = []byte{0x00, 0x00}
	PATH_SEQ_CONTENT = []byte{0x01, 0x00}
)

type ContentAnalyzerConfig struct {
	DatabasePath string
	Verbose      bool
}

type ContentAnalyzer struct {
	config   *ContentAnalyzerConfig
	db       *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
}

func NewContentAnalyzer(config *ContentAnalyzerConfig) (*ContentAnalyzer, error) {
	logger := log.New(os.Stdout, "[ContentAnalyzer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	// 尝试多次打开数据库
	var db *leveldb.DB
	var err error
	
	for i := 0; i < 5; i++ {
		db, err = leveldb.OpenFile(config.DatabasePath, options)
		if err == nil {
			break
		}
		logger.Printf("Attempt %d failed: %v, retrying in 1 second...", i+1, err)
		time.Sleep(1 * time.Second)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to open database after 5 attempts: %v", err)
	}

	crcTable := crc64.MakeTable(crc64.ISO)

	return &ContentAnalyzer{
		config:   config,
		db:       db,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (ca *ContentAnalyzer) Analyze() error {
	ca.logger.Println("=== WFS Content Analysis ===")

	// 1. 收集所有文件
	files := ca.collectFiles()

	// 2. 分析指纹索引
	fingerprints := ca.analyzeFingerprints()

	// 3. 分析WfsFileBean
	wfsFileBeans := ca.analyzeWfsFileBeans()

	// 4. 模拟WFS的getData流程
	ca.simulateGetDataFlow(files, fingerprints, wfsFileBeans)

	return nil
}

func (ca *ContentAnalyzer) collectFiles() map[int64]string {
	ca.logger.Println("\n--- Collecting Files ---")

	files := make(map[int64]string)
	iter := ca.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_CONTENT[0] && key[1] == PATH_PRE_CONTENT[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := ca.bytesToInt64(seqIDBytes)
				files[seqID] = path

				if ca.config.Verbose {
					ca.logger.Printf("File: seqID=%d, path=%s", seqID, path)
				}
			}
		}
	}

	ca.logger.Printf("Found %d files", len(files))
	return files
}

func (ca *ContentAnalyzer) analyzeFingerprints() map[string]string {
	ca.logger.Println("\n--- Analyzing Fingerprint Index ---")

	fingerprints := make(map[string]string)
	iter := ca.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if ca.isKnownIndex(key) {
			continue
		}

		// CRC64指纹索引：key长度8字节，value长度8字节
		if len(key) == 8 && len(value) == 8 {
			keyHex := hex.EncodeToString(key)
			valueHex := hex.EncodeToString(value)
			fingerprints[keyHex] = valueHex
			count++

			if ca.config.Verbose {
				crc64Value := ca.bytesToInt64(key)
				contentID := ca.bytesToInt64(value)
				ca.logger.Printf("Fingerprint: CRC64=%d -> ContentID=%d", crc64Value, contentID)
			}
		}
	}

	ca.logger.Printf("Found %d fingerprint entries", count)
	return fingerprints
}

func (ca *ContentAnalyzer) analyzeWfsFileBeans() map[string]int {
	ca.logger.Println("\n--- Analyzing WfsFileBean ---")

	wfsFileBeans := make(map[string]int)
	iter := ca.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if ca.isKnownIndex(key) {
			continue
		}

		// WfsFileBean可能的特征：key长度8字节，value长度15-100字节
		if len(key) == 8 && len(value) > 15 && len(value) < 100 {
			keyHex := hex.EncodeToString(key)
			wfsFileBeans[keyHex] = len(value)
			count++

			if ca.config.Verbose {
				contentID := ca.bytesToInt64(key)
				ca.logger.Printf("WfsFileBean: ContentID=%d, size=%d bytes", contentID, len(value))
			}
		}
	}

	ca.logger.Printf("Found %d potential WfsFileBean entries", count)
	return wfsFileBeans
}

func (ca *ContentAnalyzer) simulateGetDataFlow(files map[int64]string, fingerprints map[string]string, wfsFileBeans map[string]int) {
	ca.logger.Println("\n--- Simulating WFS getData Flow ---")

	successCount := 0
	failureCount := 0

	for seqID, path := range files {
		ca.logger.Printf("\nSimulating getData('%s') for seqID=%d:", path, seqID)

		// 步骤1：计算指纹
		crc64Value := ca.calculateCRC64(path)
		crc64Bytes := ca.int64ToBytes(int64(crc64Value))
		crc64Hex := hex.EncodeToString(crc64Bytes)

		ca.logger.Printf("  Step 1: fingerprint('%s') = CRC64=%d", path, crc64Value)

		// 步骤2：查找指纹索引
		if contentIDHex, exists := fingerprints[crc64Hex]; exists {
			ca.logger.Printf("  Step 2: Found fingerprint index: CRC64=%d -> ContentID=%s", crc64Value, contentIDHex)

			// 步骤3：查找WfsFileBean
			if size, exists := wfsFileBeans[contentIDHex]; exists {
				ca.logger.Printf("  Step 3: Found WfsFileBean: ContentID=%s, size=%d bytes", contentIDHex, size)
				ca.logger.Printf("  ✅ Result: getData would return file content")
				successCount++
			} else {
				ca.logger.Printf("  ❌ Step 3: WfsFileBean not found for ContentID=%s", contentIDHex)
				ca.logger.Printf("  ❌ Result: getData would return nil -> delData triggered!")
				failureCount++
			}
		} else {
			ca.logger.Printf("  ❌ Step 2: Fingerprint index not found for CRC64=%d", crc64Value)
			ca.logger.Printf("  ❌ Result: getData would return nil -> delData triggered!")
			failureCount++
		}
	}

	ca.logger.Printf("\n=== Summary ===")
	ca.logger.Printf("Files that would display successfully: %d", successCount)
	ca.logger.Printf("Files that would be deleted by delData: %d", failureCount)

	if successCount == 1 && failureCount == 4 {
		ca.logger.Printf("🎯 This explains why only 1 file displays on the web!")
	}
}

func (ca *ContentAnalyzer) isKnownIndex(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
		{0x06, 0x00}, // 其他已知前缀
		{0x07, 0x00},
		{0x09, 0x00},
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (ca *ContentAnalyzer) calculateCRC64(path string) uint64 {
	return crc64.Checksum([]byte(path), ca.crcTable)
}

func (ca *ContentAnalyzer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (ca *ContentAnalyzer) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (ca *ContentAnalyzer) Close() {
	if ca.db != nil {
		ca.db.Close()
	}
}

func main() {
	config := &ContentAnalyzerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Content Analyzer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	analyzer, err := NewContentAnalyzer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer analyzer.Close()

	if err := analyzer.Analyze(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
