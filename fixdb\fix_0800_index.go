// Fix 0x0800 Index for WFS System
// 修复WFS系统中0x0800索引的路径问题

package main

import (
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

var (
	INDEX_0800 = []byte{0x08, 0x00} // 发现的第三个索引前缀
)

// 0x0800索引修复器
type Index0800Fixer struct {
	db     *leveldb.DB
	logger *log.Logger
	dryRun bool
}

// 创建修复器
func NewIndex0800Fixer(dbPath string, dryRun bool) (*Index0800Fixer, error) {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 10,
		BlockCacheCapacity:     64 * 1024 * 1024, // 64MB
		WriteBuffer:            16 * 1024 * 1024, // 16MB
		ReadOnly:               dryRun,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}

	return &Index0800Fixer{
		db:     db,
		logger: log.New(os.Stdout, "[Index0800Fixer] ", log.LstdFlags),
		dryRun: dryRun,
	}, nil
}

// 关闭修复器
func (f *Index0800Fixer) Close() error {
	if f.db != nil {
		return f.db.Close()
	}
	return nil
}

// 解析protobuf格式的WfsPathBean
func (f *Index0800Fixer) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		// 读取tag
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 { // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestramp字段
			if wireType == 0 { // varint
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0: // varint
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2: // length-delimited
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

// 编码protobuf格式的WfsPathBean
func (f *Index0800Fixer) encodeWfsPathBean(path string, timestamp int64) []byte {
	var buf []byte

	// 编码Path字段 (field 1, wire type 2)
	pathBytes := []byte(path)
	tag1 := (1 << 3) | 2 // field 1, wire type 2 (length-delimited)
	buf = append(buf, byte(tag1))

	// 编码长度
	lengthBuf := make([]byte, binary.MaxVarintLen64)
	n := binary.PutUvarint(lengthBuf, uint64(len(pathBytes)))
	buf = append(buf, lengthBuf[:n]...)

	// 编码路径数据
	buf = append(buf, pathBytes...)

	// 编码Timestramp字段 (field 2, wire type 0)
	tag2 := (2 << 3) | 0 // field 2, wire type 0 (varint)
	buf = append(buf, byte(tag2))

	// 编码时间戳
	timestampBuf := make([]byte, binary.MaxVarintLen64)
	n = binary.PutVarint(timestampBuf, timestamp)
	buf = append(buf, timestampBuf[:n]...)

	return buf
}

// 检查路径是否需要修复
func (f *Index0800Fixer) needsFix(path string) (bool, string) {
	// 检查是否包含路径分隔符
	if strings.Contains(path, "/") || strings.Contains(path, "\\") {
		// 提取文件名
		fileName := filepath.Base(path)
		return true, fileName
	}
	return false, ""
}

// 修复0x0800索引
func (f *Index0800Fixer) Fix() error {
	f.logger.Println("Starting 0x0800 index fix...")

	// 遍历所有0x0800前缀的条目
	iter := f.db.NewIterator(levelutil.BytesPrefix(INDEX_0800), nil)
	defer iter.Release()

	fixedCount := 0
	errorCount := 0
	batch := new(leveldb.Batch)

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 解析WfsPathBean
		path, timestamp, err := f.parseWfsPathBean(value)
		if err != nil {
			f.logger.Printf("Error parsing WfsPathBean for key %x: %v", key, err)
			errorCount++
			continue
		}

		// 检查是否需要修复
		needsFix, newFileName := f.needsFix(path)
		if !needsFix {
			f.logger.Printf("Path already correct: %s", path)
			continue
		}

		if f.dryRun {
			f.logger.Printf("DRY RUN: Would fix path: %s -> %s (key: %x)", path, newFileName, key)
			fixedCount++
			continue
		}

		// 创建新的WfsPathBean数据
		newPathBeanData := f.encodeWfsPathBean(newFileName, timestamp)

		// 添加到批处理
		batch.Put(key, newPathBeanData)
		fixedCount++

		f.logger.Printf("Fixing 0x0800 entry: %s -> %s (key: %x)", path, newFileName, key)

		// 每100个条目执行一次批处理
		if fixedCount%100 == 0 {
			if err := f.db.Write(batch, &opt.WriteOptions{Sync: false}); err != nil {
				f.logger.Printf("Error writing batch: %v", err)
				errorCount++
			}
			batch.Reset()
		}
	}

	// 执行剩余的批处理
	if !f.dryRun && fixedCount > 0 {
		if err := f.db.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
			f.logger.Printf("Error writing final batch: %v", err)
			errorCount++
		}
	}

	// 检查迭代器错误
	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	f.logger.Printf("0x0800 index fix completed: %d entries fixed, %d errors", fixedCount, errorCount)

	if f.dryRun {
		f.logger.Println("DRY RUN completed - no actual changes made")
	}

	return nil
}

// 验证修复结果
func (f *Index0800Fixer) Verify() error {
	f.logger.Println("Verifying 0x0800 index fix results...")

	// 遍历所有0x0800前缀的条目
	iter := f.db.NewIterator(levelutil.BytesPrefix(INDEX_0800), nil)
	defer iter.Release()

	verifiedCount := 0
	problemCount := 0

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 解析WfsPathBean
		path, timestamp, err := f.parseWfsPathBean(value)
		if err != nil {
			f.logger.Printf("PARSE ERROR: key %x, error: %v", key, err)
			problemCount++
			continue
		}

		// 检查路径是否包含分隔符
		if strings.Contains(path, "/") || strings.Contains(path, "\\") {
			f.logger.Printf("PROBLEM: key %x still contains path separators: %s", key, path)
			problemCount++
		} else {
			f.logger.Printf("VERIFIED: key %x, path: %s, timestamp: %d", key, path, timestamp)
			verifiedCount++
		}
	}

	f.logger.Printf("Verification completed: %d verified, %d problems", verifiedCount, problemCount)

	if problemCount > 0 {
		return fmt.Errorf("verification failed: %d entries still have problems", problemCount)
	}

	return nil
}

// 主函数
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: fix_0800_index <db_path> [options]")
		fmt.Println("Options:")
		fmt.Println("  -dry-run    : Dry run mode (no actual changes)")
		fmt.Println("  -verify     : Verify fix results")
		fmt.Println("")
		fmt.Println("This tool fixes the 0x0800 index that contains full file paths")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	dryRun := false
	verifyOnly := false

	// 解析参数
	for i := 2; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "-dry-run":
			dryRun = true
		case "-verify":
			verifyOnly = true
		}
	}

	// 创建修复器
	fixer, err := NewIndex0800Fixer(dbPath, dryRun || verifyOnly)
	if err != nil {
		log.Fatalf("Failed to create fixer: %v", err)
	}
	defer fixer.Close()

	if verifyOnly {
		// 只验证
		if err := fixer.Verify(); err != nil {
			log.Fatalf("Verification failed: %v", err)
		}
		log.Println("Verification completed successfully!")
	} else {
		// 修复
		if err := fixer.Fix(); err != nil {
			log.Fatalf("Fix failed: %v", err)
		}

		if !dryRun {
			// 修复后验证
			if err := fixer.Verify(); err != nil {
				log.Fatalf("Post-fix verification failed: %v", err)
			}
		}

		log.Println("0x0800 index fix completed successfully!")
	}
}
