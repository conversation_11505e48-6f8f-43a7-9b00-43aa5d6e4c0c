// 清理空路径条目工具
package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type EmptyPathCleanerConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type EmptyPathCleaner struct {
	config *EmptyPathCleanerConfig
	db     *leveldb.DB
	logger *log.Logger
}

func NewEmptyPathCleaner(config *EmptyPathCleanerConfig) (*EmptyPathCleaner, error) {
	logger := log.New(os.Stdout, "[EmptyPathCleaner] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &EmptyPathCleaner{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (epc *EmptyPathCleaner) Clean() error {
	epc.logger.Println("=== Cleaning Empty Path Entries ===")

	// 收集所有空路径的PATH_PRE条目
	emptyPathKeys := epc.collectEmptyPathEntries()

	// 删除这些条目
	return epc.deleteEmptyPathEntries(emptyPathKeys)
}

func (epc *EmptyPathCleaner) collectEmptyPathEntries() [][]byte {
	epc.logger.Println("Collecting empty path entries...")

	var emptyPathKeys [][]byte
	iter := epc.db.NewIterator(nil, nil)
	defer iter.Release()

	PATH_PRE := []byte{0x00, 0x00}
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) == 2 {
				// 这是一个空路径条目（只有前缀，没有路径）
				keyBytes := make([]byte, len(key))
				copy(keyBytes, key)
				emptyPathKeys = append(emptyPathKeys, keyBytes)

				if epc.config.Verbose {
					epc.logger.Printf("Found empty path entry: key=%x", key)
				}
			}
		}
	}

	epc.logger.Printf("Found %d empty path entries", len(emptyPathKeys))
	return emptyPathKeys
}

func (epc *EmptyPathCleaner) deleteEmptyPathEntries(emptyPathKeys [][]byte) error {
	if len(emptyPathKeys) == 0 {
		epc.logger.Println("✅ No empty path entries to clean")
		return nil
	}

	epc.logger.Printf("Deleting %d empty path entries...", len(emptyPathKeys))

	if epc.config.DryRun {
		epc.logger.Printf("[DRY RUN] Would delete %d empty path entries", len(emptyPathKeys))
		for i, key := range emptyPathKeys {
			epc.logger.Printf("[DRY RUN] Would delete entry %d: key=%x", i+1, key)
		}
		return nil
	}

	batch := new(leveldb.Batch)
	for _, key := range emptyPathKeys {
		batch.Delete(key)
		if epc.config.Verbose {
			epc.logger.Printf("Deleting empty path entry: key=%x", key)
		}
	}

	if err := epc.db.Write(batch, nil); err != nil {
		return fmt.Errorf("failed to write batch: %v", err)
	}

	epc.logger.Printf("✅ Deleted %d empty path entries", len(emptyPathKeys))
	return nil
}

func (epc *EmptyPathCleaner) Close() {
	if epc.db != nil {
		epc.db.Close()
	}
}

func main() {
	config := &EmptyPathCleanerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Empty Path Cleaner\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	cleaner, err := NewEmptyPathCleaner(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer cleaner.Close()

	if err := cleaner.Clean(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
