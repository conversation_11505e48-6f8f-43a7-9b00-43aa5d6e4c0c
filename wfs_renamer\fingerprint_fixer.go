// 指纹索引修复工具 - 为清理后的文件名创建指纹索引
package main

import (
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"hash/crc64"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

var (
	PATH_PRE_FP = []byte{0x00, 0x00}
)

type FingerprintFixerConfig struct {
	DatabasePath string
	DryRun       bool
	Verbose      bool
}

type FingerprintFixer struct {
	config   *FingerprintFixerConfig
	db       *leveldb.DB
	logger   *log.Logger
	crcTable *crc64.Table
}

func NewFingerprintFixer(config *FingerprintFixerConfig) (*FingerprintFixer, error) {
	logger := log.New(os.Stdout, "[FingerprintFixer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               config.DryRun,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	// 尝试多次打开数据库
	var db *leveldb.DB
	var err error
	
	for i := 0; i < 5; i++ {
		db, err = leveldb.OpenFile(config.DatabasePath, options)
		if err == nil {
			break
		}
		logger.Printf("Attempt %d failed: %v, retrying in 1 second...", i+1, err)
		time.Sleep(1 * time.Second)
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to open database after 5 attempts: %v", err)
	}

	crcTable := crc64.MakeTable(crc64.ISO)

	return &FingerprintFixer{
		config:   config,
		db:       db,
		logger:   logger,
		crcTable: crcTable,
	}, nil
}

func (ff *FingerprintFixer) Fix() error {
	ff.logger.Println("=== Fingerprint Index Fix ===")

	// 1. 收集所有文件
	files := ff.collectFiles()

	// 2. 收集可用的WfsFileBean
	wfsFileBeans := ff.collectWfsFileBeans()

	// 3. 创建指纹索引
	return ff.createFingerprintIndexes(files, wfsFileBeans)
}

func (ff *FingerprintFixer) collectFiles() map[int64]string {
	ff.logger.Println("Collecting files from PATH_PRE...")

	files := make(map[int64]string)
	iter := ff.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE_FP[0] && key[1] == PATH_PRE_FP[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqIDBytes := iter.Value()
				seqID := ff.bytesToInt64(seqIDBytes)

				// 清理文件名
				cleanPath := ff.cleanFileName(path)
				files[seqID] = cleanPath

				if ff.config.Verbose {
					ff.logger.Printf("File: seqID=%d, cleanPath=%s", seqID, cleanPath)
				}
			}
		}
	}

	ff.logger.Printf("Found %d files", len(files))
	return files
}

func (ff *FingerprintFixer) collectWfsFileBeans() []string {
	ff.logger.Println("Collecting WfsFileBean entries...")

	wfsFileBeans := make([]string, 0)
	iter := ff.db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 跳过已知的索引前缀
		if ff.isKnownIndex(key) {
			continue
		}

		// WfsFileBean特征：key长度8字节，value长度15-100字节
		if len(key) == 8 && len(value) > 15 && len(value) < 100 {
			keyHex := hex.EncodeToString(key)
			wfsFileBeans = append(wfsFileBeans, keyHex)

			if ff.config.Verbose {
				ff.logger.Printf("WfsFileBean: ContentID=%s, size=%d bytes", keyHex, len(value))
			}
		}
	}

	ff.logger.Printf("Found %d WfsFileBean entries", len(wfsFileBeans))
	return wfsFileBeans
}

func (ff *FingerprintFixer) createFingerprintIndexes(files map[int64]string, wfsFileBeans []string) error {
	ff.logger.Println("Creating fingerprint indexes...")

	if len(wfsFileBeans) < len(files) {
		return fmt.Errorf("not enough WfsFileBean entries: need %d, have %d", len(files), len(wfsFileBeans))
	}

	batch := new(leveldb.Batch)
	createdCount := 0

	// 为每个文件分配一个WfsFileBean
	beanIndex := 0
	for seqID, cleanPath := range files {
		if beanIndex >= len(wfsFileBeans) {
			ff.logger.Printf("Warning: No more WfsFileBean for seqID=%d", seqID)
			break
		}

		// 计算清理后文件名的CRC64指纹
		crc64Value := ff.calculateCRC64(cleanPath)
		crc64Bytes := ff.int64ToBytes(int64(crc64Value))

		// 获取ContentID
		contentIDHex := wfsFileBeans[beanIndex]
		contentID, err := hex.DecodeString(contentIDHex)
		if err != nil {
			ff.logger.Printf("Warning: Invalid ContentID hex %s", contentIDHex)
			continue
		}

		// 创建指纹索引
		batch.Put(crc64Bytes, contentID)
		createdCount++
		beanIndex++

		if ff.config.Verbose {
			ff.logger.Printf("Creating fingerprint: %s -> CRC64=%d -> ContentID=%s", 
				cleanPath, crc64Value, contentIDHex)
		}
	}

	if createdCount > 0 {
		if ff.config.DryRun {
			ff.logger.Printf("[DRY RUN] Would create %d fingerprint indexes", createdCount)
		} else {
			if err := ff.db.Write(batch, nil); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			ff.logger.Printf("✅ Created %d fingerprint indexes", createdCount)
		}
	} else {
		ff.logger.Println("❌ No fingerprint indexes created")
	}

	return nil
}

func (ff *FingerprintFixer) cleanFileName(path string) string {
	// 移除路径分隔符，只保留文件名
	path = strings.ReplaceAll(path, "\\", "/")
	return filepath.Base(path)
}

func (ff *FingerprintFixer) isKnownIndex(key []byte) bool {
	if len(key) < 2 {
		return false
	}

	// 检查已知的索引前缀
	prefixes := [][]byte{
		{0x00, 0x00}, // PATH_PRE
		{0x01, 0x00}, // PATH_SEQ
		{0x08, 0x00}, // 0x0800
		{0x06, 0x00}, // 其他已知前缀
		{0x07, 0x00},
		{0x09, 0x00},
	}

	for _, prefix := range prefixes {
		if len(key) >= len(prefix) {
			match := true
			for i, b := range prefix {
				if key[i] != b {
					match = false
					break
				}
			}
			if match {
				return true
			}
		}
	}

	return false
}

func (ff *FingerprintFixer) calculateCRC64(path string) uint64 {
	return crc64.Checksum([]byte(path), ff.crcTable)
}

func (ff *FingerprintFixer) bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		bs = padded
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func (ff *FingerprintFixer) int64ToBytes(i int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(i))
	return bs
}

func (ff *FingerprintFixer) Close() {
	if ff.db != nil {
		ff.db.Close()
	}
}

func main() {
	config := &FingerprintFixerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")
	flag.BoolVar(&config.DryRun, "dry-run", false, "Preview mode")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Fingerprint Index Fixer\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -dry-run -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	fixer, err := NewFingerprintFixer(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer fixer.Close()

	if err := fixer.Fix(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
