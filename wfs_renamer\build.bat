@echo off
REM WFS文件重命名工具构建脚本

echo ========================================
echo WFS File Renamer Builder
echo ========================================

REM 检查Go环境
go version >nul 2>&1
if errorlevel 1 (
    echo Error: Go is not installed or not in PATH
    echo Please install Go 1.22 or later
    pause
    exit /b 1
)

echo Go environment found
go version

REM 检查依赖的stub文件
if not exist "..\wfs_migrator\stub\wfs.go" (
    echo Error: WFS stub files not found
    echo Please ensure wfs_migrator project exists with generated stub files
    pause
    exit /b 1
)

echo WFS stub files found

REM 初始化模块（如果需要）
if not exist "go.sum" (
    echo Initializing Go module...
    go mod tidy
)

echo.
echo Building WFS Renamer...
go build -o wfs_renamer.exe main.go

if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable: wfs_renamer.exe
echo.
echo Usage Examples:
echo   wfs_renamer.exe -rules rename_rules_example.txt -dry-run
echo   wfs_renamer.exe -rules my_rules.txt -host localhost -port 5122
echo   wfs_renamer.exe -rules rules.txt -workers 8 -verbose
echo.
echo Rule File Format:
echo   old_path -^> new_path
echo   old_path,new_path
echo   # Comments start with #
echo.
pause
