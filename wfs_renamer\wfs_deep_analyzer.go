// WFS深度分析工具 - 找出网页显示问题的真正原因
package main

import (
	"crypto/md5"
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

// 索引前缀常量
var (
	PATH_PRE = []byte{0x00, 0x00}
	PATH_SEQ = []byte{0x01, 0x00}
)

type FileRecord struct {
	Path           string
	SeqID          []byte
	SeqIDInt       int64
	HasPATH_PRE    bool
	HasPATH_SEQ    bool
	PATH_SEQData   []byte
	Has0x0800      bool
	HasFingerprint bool
	FingerprintKey []byte
	ContentID      []byte
}

func main() {
	var dbPath string
	var verbose bool
	
	flag.StringVar(&dbPath, "db", "", "Database path (required)")
	flag.BoolVar(&verbose, "verbose", false, "Verbose output")
	flag.Parse()

	if dbPath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		os.Exit(1)
	}

	logger := log.New(os.Stdout, "[WFSDeepAnalyzer] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
	}
	defer db.Close()

	logger.Println("=== WFS Deep Analysis ===")

	// 1. 收集所有文件记录
	files := make(map[string]*FileRecord)
	
	// 扫描PATH_PRE
	iter := db.NewIterator(nil, nil)
	defer iter.Release()

	logger.Println("Phase 1: Scanning PATH_PRE index...")
	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) >= 2 && key[0] == PATH_PRE[0] && key[1] == PATH_PRE[1] {
			if len(key) > 2 {
				path := string(key[2:])
				seqID := make([]byte, len(iter.Value()))
				copy(seqID, iter.Value())
				
				files[path] = &FileRecord{
					Path:        path,
					SeqID:       seqID,
					SeqIDInt:    bytesToInt64(seqID),
					HasPATH_PRE: true,
				}
				
				if verbose {
					logger.Printf("PATH_PRE: %s -> seqID %d (%s)", path, bytesToInt64(seqID), hex.EncodeToString(seqID))
				}
			}
		}
	}
	logger.Printf("Found %d PATH_PRE entries", len(files))

	// 2. 检查PATH_SEQ
	logger.Println("Phase 2: Checking PATH_SEQ index...")
	for path, record := range files {
		pathSeqKey := append(PATH_SEQ, record.SeqID...)
		
		if data, err := db.Get(pathSeqKey, nil); err == nil {
			record.HasPATH_SEQ = true
			record.PATH_SEQData = data
			
			if seqPath, timestamp, err := parseWfsPathBean(data); err == nil {
				if verbose {
					logger.Printf("PATH_SEQ: seqID %d -> path=%s, timestamp=%d", record.SeqIDInt, seqPath, timestamp)
				}
				if seqPath != path {
					logger.Printf("⚠️  PATH_SEQ path mismatch: expected %s, got %s", path, seqPath)
				}
			} else {
				logger.Printf("❌ PATH_SEQ parse error for %s: %v", path, err)
			}
		} else {
			if verbose {
				logger.Printf("❌ PATH_SEQ missing for %s (seqID %d)", path, record.SeqIDInt)
			}
		}
	}

	// 3. 检查0x0800索引
	logger.Println("Phase 3: Checking 0x0800 index...")
	prefix0800 := []byte{0x08, 0x00}
	iter = db.NewIterator(nil, nil)
	defer iter.Release()

	for iter.Seek(prefix0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}
		
		value := iter.Value()
		if path, timestamp, err := parseWfsPathBean(value); err == nil {
			if record, exists := files[path]; exists {
				record.Has0x0800 = true
				if verbose {
					logger.Printf("0x0800: key %s -> path=%s, timestamp=%d", hex.EncodeToString(key), path, timestamp)
				}
			} else {
				logger.Printf("⚠️  0x0800 orphan: %s (no PATH_PRE)", path)
			}
		}
	}

	// 4. 检查指纹索引
	logger.Println("Phase 4: Checking fingerprint index...")
	for path, record := range files {
		fingerprint := calculateFingerprint(path)
		record.FingerprintKey = fingerprint
		
		if contentID, err := db.Get(fingerprint, nil); err == nil {
			record.HasFingerprint = true
			record.ContentID = contentID
			
			if verbose {
				logger.Printf("Fingerprint: %s -> %s (contentID %s)", path, hex.EncodeToString(fingerprint), hex.EncodeToString(contentID))
			}
		} else {
			if verbose {
				logger.Printf("❌ Fingerprint missing for %s", path)
			}
		}
	}

	// 5. 分析WFS网页显示逻辑
	logger.Println("\nPhase 5: Analyzing WFS web display logic...")
	
	// 模拟WFS网页的文件列表获取逻辑
	logger.Println("Simulating WFS web file listing...")
	
	// 方法1：通过PATH_SEQ扫描（WFS可能的实现）
	logger.Println("Method 1: Scanning PATH_SEQ index...")
	iter = db.NewIterator(nil, nil)
	defer iter.Release()
	
	webDisplayFiles := make(map[string]bool)
	for iter.Seek(PATH_SEQ); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != PATH_SEQ[0] || key[1] != PATH_SEQ[1] {
			break
		}
		
		value := iter.Value()
		if path, timestamp, err := parseWfsPathBean(value); err == nil {
			webDisplayFiles[path] = true
			logger.Printf("Web would display: %s (timestamp=%d)", path, timestamp)
		}
	}
	
	// 方法2：通过PATH_PRE扫描
	logger.Println("Method 2: Scanning PATH_PRE index...")
	for path := range files {
		logger.Printf("PATH_PRE contains: %s", path)
	}

	// 6. 生成详细报告
	logger.Println("\n=== Detailed Analysis Report ===")
	
	completeFiles := 0
	for path, record := range files {
		status := "✅"
		issues := []string{}
		
		if !record.HasPATH_SEQ {
			status = "❌"
			issues = append(issues, "No PATH_SEQ")
		}
		if !record.HasFingerprint {
			status = "❌"
			issues = append(issues, "No Fingerprint")
		}
		if !record.Has0x0800 {
			issues = append(issues, "No 0x0800")
		}
		
		if len(issues) == 0 {
			completeFiles++
		}
		
		webDisplay := "No"
		if webDisplayFiles[path] {
			webDisplay = "Yes"
		}
		
		issueStr := ""
		if len(issues) > 0 {
			issueStr = " [" + strings.Join(issues, ", ") + "]"
		}
		
		logger.Printf("%s %s -> seqID %d, WebDisplay: %s%s", status, path, record.SeqIDInt, webDisplay, issueStr)
	}

	// 7. 总结
	logger.Println("\n=== Summary ===")
	logger.Printf("Total files: %d", len(files))
	logger.Printf("Complete files: %d", completeFiles)
	logger.Printf("Files that would display on web: %d", len(webDisplayFiles))
	logger.Printf("Incomplete files: %d", len(files)-completeFiles)
	
	if len(webDisplayFiles) != len(files) {
		logger.Printf("\n⚠️  %d files missing from web display", len(files)-len(webDisplayFiles))
		logger.Println("This explains why the web interface doesn't show all files!")
		
		// 找出缺失的文件
		for path := range files {
			if !webDisplayFiles[path] {
				logger.Printf("Missing from web: %s", path)
			}
		}
	} else {
		logger.Println("\n✅ All files should display on web interface")
	}
}

// 辅助函数
func bytesToInt64(bs []byte) int64 {
	if len(bs) < 8 {
		// 处理短字节数组
		padded := make([]byte, 8)
		copy(padded[8-len(bs):], bs)
		return int64(binary.BigEndian.Uint64(padded))
	}
	return int64(binary.BigEndian.Uint64(bs))
}

func calculateFingerprint(path string) []byte {
	hash := md5.Sum([]byte(path))
	return hash[:]
}

func parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64
	
	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n
		
		fieldNum := tag >> 3
		wireType := tag & 0x7
		
		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}
	
	return path, timestamp, nil
}
