@echo off
REM LevelDB Key Fixer Build Script
REM Build script for WFS LevelDB key path fixer

echo ========================================
echo LevelDB Key Fixer Build Script
echo ========================================
echo.

REM Check Go environment
echo Checking Go environment...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go not found, please install Go 1.18 or higher
    pause
    exit /b 1
)

go version
echo.

REM Download dependencies
echo Downloading dependencies...
go mod tidy
if %errorlevel% neq 0 (
    echo Error: Failed to download dependencies
    pause
    exit /b 1
)
echo Dependencies downloaded successfully
echo.

REM Build main program
echo Building main program leveldb_key_fixer.exe...
go build -ldflags "-s -w" -o leveldb_key_fixer.exe leveldb_key_fixer.go
if %errorlevel% neq 0 (
    echo Error: Failed to build main program
    pause
    exit /b 1
)
echo Main program built successfully: leveldb_key_fixer.exe
echo.

REM Build test program
echo Building test program test_runner.exe...
go build -ldflags "-s -w" -o test_runner.exe test_runner.go
if %errorlevel% neq 0 (
    echo Error: Failed to build test program
    pause
    exit /b 1
)
echo Test program built successfully: test_runner.exe
echo.

REM Show build results
echo ========================================
echo Build completed!
echo ========================================
echo.
echo Generated files:
dir /b *.exe 2>nul
echo.

REM Show file sizes
echo File sizes:
for %%f in (*.exe) do (
    for %%s in (%%f) do echo   %%f: %%~zs bytes
)
echo.

echo Usage:
echo   1. Fix database: leveldb_key_fixer.exe ^<database_path^>
echo   2. Run tests:    test_runner.exe
echo   3. Performance:  test_runner.exe perf
echo.
echo For detailed usage, see README.md
echo.

pause
