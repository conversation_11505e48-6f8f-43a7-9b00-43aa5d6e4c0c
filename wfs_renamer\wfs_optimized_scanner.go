// WFS优化扫描工具 - 处理超大型数据库（1亿+记录）
package main

import (
	"database/sql"
	"encoding/binary"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	_ "github.com/mattn/go-sqlite3"
	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type OptimizedScannerConfig struct {
	DatabasePath string
	OutputPath   string
	Verbose      bool
	BatchSize    int // 批量插入大小
	ProgressStep int // 进度显示间隔
	Workers      int // 并发工作线程数
}

type OptimizedScanner struct {
	config     *OptimizedScannerConfig
	db         *leveldb.DB
	sqliteDB   *sql.DB
	logger     *log.Logger
	mu         sync.Mutex
	totalCount int64
	processed  int64
	startTime  time.Time
}

type FilenameEntry struct {
	Filename string
}

func NewOptimizedScanner(config *OptimizedScannerConfig) (*OptimizedScanner, error) {
	logger := log.New(os.Stdout, "[OptimizedScanner] ", log.LstdFlags)

	// 打开LevelDB（只读模式）
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     64 * 1024 * 1024, // 增加缓存
		WriteBuffer:            16 * 1024 * 1024, // 增加写缓冲
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	var db *leveldb.DB
	var err error

	for i := 0; i < 5; i++ {
		db, err = leveldb.OpenFile(config.DatabasePath, options)
		if err == nil {
			break
		}
		logger.Printf("Attempt %d failed: %v, retrying in 2 seconds...", i+1, err)
		time.Sleep(2 * time.Second)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to open LevelDB after 5 attempts: %v", err)
	}

	// 打开SQLite数据库（优化设置）
	sqliteDB, err := sql.Open("sqlite3", config.OutputPath+"?cache=shared&mode=rwc&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=10000")
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to open SQLite database: %v", err)
	}

	// 设置SQLite性能优化参数
	sqliteDB.SetMaxOpenConns(1)
	sqliteDB.SetMaxIdleConns(1)

	return &OptimizedScanner{
		config:    config,
		db:        db,
		sqliteDB:  sqliteDB,
		logger:    logger,
		startTime: time.Now(),
	}, nil
}

func (os *OptimizedScanner) Scan() error {
	os.logger.Println("=== WFS Optimized Large Database Scan ===")
	os.logger.Printf("Batch size: %d", os.config.BatchSize)
	os.logger.Printf("Progress step: %d", os.config.ProgressStep)
	os.logger.Printf("Workers: %d", os.config.Workers)

	// 创建SQLite表
	if err := os.createTable(); err != nil {
		return fmt.Errorf("failed to create table: %v", err)
	}

	// 预估总记录数
	if err := os.estimateTotal(); err != nil {
		os.logger.Printf("Warning: Failed to estimate total records: %v", err)
	}

	// 扫描并提取文件名
	return os.scanFilenames()
}

func (os *OptimizedScanner) createTable() error {
	os.logger.Println("Creating optimized SQLite table...")

	createTableSQL := `
	PRAGMA journal_mode = WAL;
	PRAGMA synchronous = NORMAL;
	PRAGMA cache_size = 10000;
	PRAGMA temp_store = memory;
	
	CREATE TABLE IF NOT EXISTS filenames (
		filename TEXT PRIMARY KEY
	) WITHOUT ROWID;
	
	CREATE INDEX IF NOT EXISTS idx_filename ON filenames(filename);
	`

	_, err := os.sqliteDB.Exec(createTableSQL)
	return err
}

func (os *OptimizedScanner) estimateTotal() error {
	os.logger.Println("Estimating total records...")

	iter := os.db.NewIterator(nil, nil)
	defer iter.Release()

	count := int64(0)
	sampleSize := int64(100000) // 采样10万条记录

	for iter.First(); iter.Valid() && count < sampleSize; iter.Next() {
		count++
	}

	if count > 0 {
		// 简单估算（不够精确，但给用户一个概念）
		os.totalCount = count * 1000 // 假设采样比例
		os.logger.Printf("Estimated total records: ~%d (based on sampling)", os.totalCount)
	}

	return nil
}

func (os *OptimizedScanner) scanFilenames() error {
	os.logger.Println("Starting optimized filename scanning...")

	// 准备批量插入
	batch := make([]string, 0, os.config.BatchSize)
	filenameSet := make(map[string]bool, os.config.BatchSize)

	// 准备插入语句
	insertSQL := `INSERT OR IGNORE INTO filenames (filename) VALUES (?)`
	stmt, err := os.sqliteDB.Prepare(insertSQL)
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement: %v", err)
	}
	defer stmt.Close()

	// 扫描所有条目
	iter := os.db.NewIterator(nil, nil)
	defer iter.Release()

	scannedCount := int64(0)
	uniqueFilenames := int64(0)
	lastProgressTime := time.Now()

	for iter.First(); iter.Valid(); iter.Next() {
		key := iter.Key()
		value := iter.Value()

		// 从不同类型的索引中提取文件名
		filenames := os.extractFilenames(key, value)

		for _, filename := range filenames {
			if filename != "" && !filenameSet[filename] {
				filenameSet[filename] = true
				batch = append(batch, filename)
				uniqueFilenames++

				// 批量插入
				if len(batch) >= os.config.BatchSize {
					if err := os.insertBatch(stmt, batch); err != nil {
						return err
					}
					batch = batch[:0] // 清空batch但保留容量

					// 清理内存映射以控制内存使用
					if len(filenameSet) > os.config.BatchSize*2 {
						filenameSet = make(map[string]bool, os.config.BatchSize)
					}

					// 强制垃圾回收
					if uniqueFilenames%int64(os.config.BatchSize*10) == 0 {
						runtime.GC()
					}
				}
			}
		}

		scannedCount++
		os.processed = scannedCount

		// 显示进度
		if scannedCount%int64(os.config.ProgressStep) == 0 {
			os.showProgress(scannedCount, uniqueFilenames, lastProgressTime)
			lastProgressTime = time.Now()
		}
	}

	// 插入剩余的batch
	if len(batch) > 0 {
		if err := os.insertBatch(stmt, batch); err != nil {
			return err
		}
	}

	// 最终统计
	os.showFinalStats(scannedCount, uniqueFilenames)

	return nil
}

func (os *OptimizedScanner) insertBatch(stmt *sql.Stmt, batch []string) error {
	// 开始事务
	tx, err := os.sqliteDB.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 批量插入
	for _, filename := range batch {
		_, err := tx.Stmt(stmt).Exec(filename)
		if err != nil {
			return fmt.Errorf("failed to insert filename: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	if os.config.Verbose {
		os.logger.Printf("Inserted batch of %d filenames", len(batch))
	}

	return nil
}

func (os *OptimizedScanner) showProgress(scanned, unique int64, lastTime time.Time) {
	elapsed := time.Since(os.startTime)
	rate := float64(scanned) / elapsed.Seconds()

	var progress string
	if os.totalCount > 0 {
		percentage := float64(scanned) / float64(os.totalCount) * 100
		progress = fmt.Sprintf("%.1f%%", percentage)
	} else {
		progress = "N/A"
	}

	// 获取内存使用情况
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	memMB := float64(m.Alloc) / 1024 / 1024

	os.logger.Printf("Progress: %s | Scanned: %d | Unique: %d | Rate: %.0f/s | Memory: %.1fMB | Elapsed: %v",
		progress, scanned, unique, rate, memMB, elapsed.Round(time.Second))
}

func (os *OptimizedScanner) showFinalStats(scanned, unique int64) {
	elapsed := time.Since(os.startTime)
	rate := float64(scanned) / elapsed.Seconds()

	os.logger.Println("\n=== Final Statistics ===")
	os.logger.Printf("Total scanned: %d records", scanned)
	os.logger.Printf("Unique filenames: %d", unique)
	os.logger.Printf("Average rate: %.0f records/second", rate)
	os.logger.Printf("Total time: %v", elapsed.Round(time.Second))
	os.logger.Printf("Output file: %s", os.config.OutputPath)
}

func (os *OptimizedScanner) extractFilenames(key, value []byte) []string {
	var filenames []string

	// 检查PATH_PRE索引 (0x0000)
	if len(key) >= 2 && key[0] == 0x00 && key[1] == 0x00 {
		if len(key) > 2 {
			path := string(key[2:])
			filename := os.extractFilename(path)
			if filename != "" {
				filenames = append(filenames, filename)
			}
		}
	}

	// 检查PATH_SEQ索引 (0x0100) - 从WfsPathBean中提取
	if len(key) >= 2 && key[0] == 0x01 && key[1] == 0x00 {
		if path, _, err := os.parseWfsPathBean(value); err == nil && path != "" {
			filename := os.extractFilename(path)
			if filename != "" {
				filenames = append(filenames, filename)
			}
		}
	}

	// 检查0x0800索引 - 从WfsPathBean中提取
	if len(key) >= 2 && key[0] == 0x08 && key[1] == 0x00 {
		if path, _, err := os.parseWfsPathBean(value); err == nil && path != "" {
			filename := os.extractFilename(path)
			if filename != "" {
				filenames = append(filenames, filename)
			}
		}
	}

	return filenames
}

func (os *OptimizedScanner) extractFilename(path string) string {
	if path == "" {
		return ""
	}

	// 标准化路径分隔符
	path = strings.ReplaceAll(path, "\\", "/")

	// 获取文件名（包含扩展名）
	filename := filepath.Base(path)

	// 过滤掉明显不是文件的条目
	if filename == "." || filename == ".." || filename == "/" || filename == "\\" {
		return ""
	}

	// 过滤掉没有扩展名的条目（可能是目录）
	if !strings.Contains(filename, ".") {
		return ""
	}

	return filename
}

func (os *OptimizedScanner) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (os *OptimizedScanner) Close() {
	if os.db != nil {
		os.db.Close()
	}
	if os.sqliteDB != nil {
		// 优化SQLite关闭
		os.sqliteDB.Exec("PRAGMA optimize")
		os.sqliteDB.Close()
	}
}

func main() {
	config := &OptimizedScannerConfig{}

	flag.StringVar(&config.DatabasePath, "db", "", "LevelDB database path (required)")
	flag.StringVar(&config.OutputPath, "output", "wfs_filenames.db3", "SQLite output file path (.db3)")
	flag.BoolVar(&config.Verbose, "verbose", false, "Verbose output")
	flag.IntVar(&config.BatchSize, "batch", 10000, "Batch size for database inserts")
	flag.IntVar(&config.ProgressStep, "progress", 100000, "Progress display interval")
	flag.IntVar(&config.Workers, "workers", 1, "Number of worker threads (currently unused)")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "WFS Optimized Large Database Scanner\n\n")
		fmt.Fprintf(os.Stderr, "专为处理超大型数据库（1亿+记录）优化的扫描工具\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -output filenames.db3 -batch 50000 -progress 500000\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb -verbose\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "\nOptimization Tips:\n")
		fmt.Fprintf(os.Stderr, "  - 增加batch size可以提高插入性能，但会占用更多内存\n")
		fmt.Fprintf(os.Stderr, "  - 减少progress间隔可以更频繁地看到进度，但会影响性能\n")
		fmt.Fprintf(os.Stderr, "  - 建议在SSD上运行以获得最佳性能\n")
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	// 确保输出文件有.db3扩展名
	if !strings.HasSuffix(config.OutputPath, ".db3") {
		config.OutputPath = strings.TrimSuffix(config.OutputPath, filepath.Ext(config.OutputPath)) + ".db3"
	}

	// 验证参数
	if config.BatchSize <= 0 {
		config.BatchSize = 10000
	}
	if config.ProgressStep <= 0 {
		config.ProgressStep = 100000
	}
	if config.Workers <= 0 {
		config.Workers = 1
	}

	scanner, err := NewOptimizedScanner(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer scanner.Close()

	if err := scanner.Scan(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ 大型数据库扫描完成，结果保存到: %s\n", config.OutputPath)
}
