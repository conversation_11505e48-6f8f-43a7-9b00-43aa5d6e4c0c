// Test program for LevelDB Key Path Fixer
// 测试LevelDB key路径修复工具的正确性和安全性

package main

import (
	"bytes"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
	levelutil "github.com/syndtr/goleveldb/leveldb/util"
)

// 测试数据结构
type TestCase struct {
	OriginalKey string
	ExpectedKey string
	Value       []byte
	ShouldFix   bool
}

// 测试配置
type TestConfig struct {
	TestDBPath   string
	BackupDBPath string
}

// 测试器
type KeyFixerTester struct {
	config   *TestConfig
	testDB   *leveldb.DB
	backupDB *leveldb.DB
	logger   *log.Logger
}

// 创建测试器
func NewKeyFixerTester() *KeyFixerTester {
	return &KeyFixerTester{
		config: &TestConfig{
			TestDBPath:   "test_db_" + time.Now().Format("20060102_150405"),
			BackupDBPath: "backup_db_" + time.Now().Format("20060102_150405"),
		},
		logger: log.New(os.Stdout, "[KeyFixerTest] ", log.LstdFlags),
	}
}

// 创建测试数据库
func (t *KeyFixerTester) createTestDB() error {
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     8 * 1024 * 1024,
		WriteBuffer:            4 * 1024 * 1024,
	}

	var err error
	t.testDB, err = leveldb.OpenFile(t.config.TestDBPath, options)
	if err != nil {
		return fmt.Errorf("failed to create test database: %v", err)
	}

	t.logger.Printf("Created test database: %s", t.config.TestDBPath)
	return nil
}

// 准备测试数据
func (t *KeyFixerTester) prepareTestData() error {
	testCases := []TestCase{
		// 需要修复的情况
		{
			OriginalKey: "3162/4063/7242/31624063724253!2u989!2e1!3u1010.nc",
			ExpectedKey: "31624063724253!2u989!2e1!3u1010.nc",
			Value:       []byte("test_value_1"),
			ShouldFix:   true,
		},
		{
			OriginalKey: "path/to/file/document.pdf",
			ExpectedKey: "document.pdf",
			Value:       []byte("test_value_2"),
			ShouldFix:   true,
		},
		{
			OriginalKey: "deep/nested/path/image.jpg",
			ExpectedKey: "image.jpg",
			Value:       []byte("test_value_3"),
			ShouldFix:   true,
		},
		{
			OriginalKey: "windows\\path\\file.txt",
			ExpectedKey: "file.txt",
			Value:       []byte("test_value_4"),
			ShouldFix:   true,
		},
		// 不需要修复的情况
		{
			OriginalKey: "simple_file.txt",
			ExpectedKey: "simple_file.txt",
			Value:       []byte("test_value_5"),
			ShouldFix:   false,
		},
		{
			OriginalKey: "another_file.dat",
			ExpectedKey: "another_file.dat",
			Value:       []byte("test_value_6"),
			ShouldFix:   false,
		},
	}

	batch := new(leveldb.Batch)
	for _, tc := range testCases {
		key := append(PATH_PRE, []byte(tc.OriginalKey)...)
		batch.Put(key, tc.Value)
	}

	// 添加一些非PATH_PRE的key（不应该被修复）
	batch.Put([]byte("normal_key_1"), []byte("normal_value_1"))
	batch.Put([]byte("normal_key_2"), []byte("normal_value_2"))

	if err := t.testDB.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
		return fmt.Errorf("failed to write test data: %v", err)
	}

	t.logger.Printf("Prepared test data with %d test cases", len(testCases))
	return nil
}

// 验证修复结果
func (t *KeyFixerTester) verifyResults() error {
	t.logger.Println("Verifying fix results...")

	expectedResults := map[string]string{
		"31624063724253!2u989!2e1!3u1010.nc": "test_value_1",
		"document.pdf":                        "test_value_2",
		"image.jpg":                          "test_value_3",
		"file.txt":                           "test_value_4",
		"simple_file.txt":                    "test_value_5",
		"another_file.dat":                   "test_value_6",
	}

	// 检查修复后的key是否正确
	for expectedKey, expectedValue := range expectedResults {
		key := append(PATH_PRE, []byte(expectedKey)...)
		value, err := t.testDB.Get(key, nil)
		if err != nil {
			return fmt.Errorf("expected key not found: %s, error: %v", expectedKey, err)
		}
		if string(value) != expectedValue {
			return fmt.Errorf("value mismatch for key %s: expected %s, got %s", 
				expectedKey, expectedValue, string(value))
		}
		t.logger.Printf("✓ Verified key: %s", expectedKey)
	}

	// 检查是否还有错误的key存在
	iter := t.testDB.NewIterator(levelutil.BytesPrefix(PATH_PRE), nil)
	defer iter.Release()

	for iter.Next() {
		pathBytes := iter.Key()[len(PATH_PRE):]
		pathStr := string(pathBytes)
		
		// 检查是否还有包含路径分隔符的key
		if bytes.Contains(pathBytes, []byte("/")) || bytes.Contains(pathBytes, []byte("\\")) {
			return fmt.Errorf("found unfixed key with path separator: %s", pathStr)
		}
	}

	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error during verification: %v", err)
	}

	// 检查非PATH_PRE的key是否未被影响
	normalKeys := []string{"normal_key_1", "normal_key_2"}
	for _, key := range normalKeys {
		if _, err := t.testDB.Get([]byte(key), nil); err != nil {
			return fmt.Errorf("normal key was affected: %s, error: %v", key, err)
		}
		t.logger.Printf("✓ Normal key preserved: %s", key)
	}

	t.logger.Println("All verification checks passed!")
	return nil
}

// 清理测试环境
func (t *KeyFixerTester) cleanup() {
	if t.testDB != nil {
		t.testDB.Close()
	}
	if t.backupDB != nil {
		t.backupDB.Close()
	}

	// 删除测试数据库目录
	os.RemoveAll(t.config.TestDBPath)
	os.RemoveAll(t.config.BackupDBPath)
	
	t.logger.Println("Cleanup completed")
}

// 运行完整测试
func (t *KeyFixerTester) RunTest() error {
	defer t.cleanup()

	t.logger.Println("Starting LevelDB Key Fixer test...")

	// 1. 创建测试数据库
	if err := t.createTestDB(); err != nil {
		return err
	}

	// 2. 准备测试数据
	if err := t.prepareTestData(); err != nil {
		return err
	}

	// 3. 关闭数据库以便修复工具使用
	t.testDB.Close()
	t.testDB = nil

	// 4. 运行修复工具
	t.logger.Println("Running key fixer...")
	config := &FixerConfig{
		DBPath:       t.config.TestDBPath,
		BackupPath:   t.config.BackupDBPath,
		WorkerCount:  2,
		BatchSize:    100,
		DryRun:       false,
		EnableBackup: true,
	}

	fixer := NewKeyFixer(config)
	if err := fixer.Fix(); err != nil {
		return fmt.Errorf("key fixer failed: %v", err)
	}

	// 5. 重新打开数据库进行验证
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     8 * 1024 * 1024,
		WriteBuffer:            4 * 1024 * 1024,
	}

	var err error
	t.testDB, err = leveldb.OpenFile(t.config.TestDBPath, options)
	if err != nil {
		return fmt.Errorf("failed to reopen test database: %v", err)
	}

	// 6. 验证修复结果
	if err := t.verifyResults(); err != nil {
		return err
	}

	t.logger.Println("All tests passed successfully!")
	return nil
}

// 性能测试
func (t *KeyFixerTester) RunPerformanceTest(keyCount int) error {
	t.logger.Printf("Starting performance test with %d keys...", keyCount)

	defer t.cleanup()

	// 创建测试数据库
	if err := t.createTestDB(); err != nil {
		return err
	}

	// 生成大量测试数据
	batch := new(leveldb.Batch)
	for i := 0; i < keyCount; i++ {
		// 一半是需要修复的key，一半是正常的key
		var keyStr string
		if i%2 == 0 {
			keyStr = fmt.Sprintf("path/to/file/test_file_%d.dat", i)
		} else {
			keyStr = fmt.Sprintf("test_file_%d.dat", i)
		}
		
		key := append(PATH_PRE, []byte(keyStr)...)
		value := []byte(fmt.Sprintf("test_value_%d", i))
		batch.Put(key, value)

		// 每1000个key写入一次
		if i%1000 == 999 {
			if err := t.testDB.Write(batch, &opt.WriteOptions{Sync: false}); err != nil {
				return fmt.Errorf("failed to write batch: %v", err)
			}
			batch.Reset()
		}
	}

	// 写入剩余的key
	if err := t.testDB.Write(batch, &opt.WriteOptions{Sync: true}); err != nil {
		return fmt.Errorf("failed to write final batch: %v", err)
	}

	t.logger.Printf("Generated %d test keys", keyCount)

	// 关闭数据库
	t.testDB.Close()
	t.testDB = nil

	// 运行修复工具并测量时间
	startTime := time.Now()
	
	config := &FixerConfig{
		DBPath:       t.config.TestDBPath,
		BackupPath:   t.config.BackupDBPath,
		WorkerCount:  4,
		BatchSize:    1000,
		DryRun:       false,
		EnableBackup: false, // 性能测试时禁用备份
	}

	fixer := NewKeyFixer(config)
	if err := fixer.Fix(); err != nil {
		return fmt.Errorf("key fixer failed: %v", err)
	}

	elapsed := time.Since(startTime)
	t.logger.Printf("Performance test completed in %v", elapsed)
	t.logger.Printf("Processing rate: %.2f keys/second", float64(keyCount)/elapsed.Seconds())

	return nil
}
