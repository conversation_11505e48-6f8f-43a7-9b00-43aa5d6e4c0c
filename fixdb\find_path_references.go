// Find Path References in WFS Database
// 查找数据库中所有可能包含文件路径信息的地方

package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

func findPathReferences(dbPath string) error {
	log.Printf("Searching for path references in: %s", dbPath)

	// 打开数据库
	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     8 * 1024 * 1024,
		WriteBuffer:            4 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		ErrorIfExist:           false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(dbPath, options)
	if err != nil {
		log.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(dbPath, options)
		if err != nil {
			return fmt.Errorf("failed to open/recover database: %v", err)
		}
		log.Printf("Database recovered successfully")
	}
	defer db.Close()

	// 搜索关键词
	searchTerms := []string{
		"Pictures",
		"khms3google",
		"weigu",
		"Users",
		"C:",
		"\\",
		"/",
		".jpg",
		"1.jpg",
		"444.jpg",
	}

	log.Printf("Searching for the following terms: %v", searchTerms)

	// 遍历所有key-value对
	iter := db.NewIterator(nil, nil)
	defer iter.Release()

	foundCount := 0
	totalCount := 0

	for iter.Next() {
		key := iter.Key()
		value := iter.Value()
		totalCount++

		keyStr := string(key)
		valueStr := string(value)

		// 检查key中是否包含搜索词
		for _, term := range searchTerms {
			if strings.Contains(keyStr, term) {
				foundCount++
				log.Printf("FOUND in KEY: %q contains %q", keyStr, term)
				log.Printf("  Key hex: %x", key)
				log.Printf("  Value hex: %x", value)
				log.Printf("  Value string: %q", valueStr)
				break
			}
		}

		// 检查value中是否包含搜索词
		for _, term := range searchTerms {
			if strings.Contains(valueStr, term) {
				foundCount++
				log.Printf("FOUND in VALUE: %q contains %q", valueStr, term)
				log.Printf("  Key hex: %x", key)
				log.Printf("  Key string: %q", keyStr)
				log.Printf("  Value hex: %x", value)
				break
			}
		}

		// 特别检查可能的路径格式
		if strings.Contains(keyStr, "\\") || strings.Contains(keyStr, "/") {
			if len(keyStr) > 10 { // 避免短的分隔符
				log.Printf("POTENTIAL PATH in KEY: %q", keyStr)
				log.Printf("  Key hex: %x", key)
				log.Printf("  Value hex: %x", value)
			}
		}

		if strings.Contains(valueStr, "\\") || strings.Contains(valueStr, "/") {
			if len(valueStr) > 10 { // 避免短的分隔符
				log.Printf("POTENTIAL PATH in VALUE: %q", valueStr)
				log.Printf("  Key hex: %x", key)
				log.Printf("  Key string: %q", keyStr)
				log.Printf("  Value hex: %x", value)
			}
		}
	}

	if err := iter.Error(); err != nil {
		return fmt.Errorf("iterator error: %v", err)
	}

	log.Printf("Search completed: %d matches found out of %d total entries", foundCount, totalCount)

	// 额外检查：查找所有包含文件扩展名的条目
	log.Printf("\n=== Searching for file extensions ===")
	iter2 := db.NewIterator(nil, nil)
	defer iter2.Release()

	extensions := []string{".jpg", ".png", ".gif", ".pdf", ".txt", ".doc"}
	extCount := 0

	for iter2.Next() {
		key := iter2.Key()
		value := iter2.Value()

		keyStr := string(key)
		valueStr := string(value)

		for _, ext := range extensions {
			if strings.Contains(keyStr, ext) || strings.Contains(valueStr, ext) {
				extCount++
				log.Printf("FILE EXTENSION FOUND: %s", ext)
				log.Printf("  Key: %q (hex: %x)", keyStr, key)
				log.Printf("  Value: %q (hex: %x)", valueStr, value)
				break
			}
		}
	}

	log.Printf("File extension search completed: %d matches found", extCount)

	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: find_path_references <db_path>")
		fmt.Println("This tool searches for any references to file paths in the database")
		os.Exit(1)
	}

	dbPath := os.Args[1]
	if err := findPathReferences(dbPath); err != nil {
		log.Fatalf("Search failed: %v", err)
	}
}
