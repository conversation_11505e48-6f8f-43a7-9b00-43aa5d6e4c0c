@echo off
REM WFS Database Migration Tool Build Script
REM 构建WFS数据库迁移工具

echo ========================================
echo WFS Database Migration Tool Builder
echo ========================================

REM 检查Go环境
go version >nul 2>&1
if errorlevel 1 (
    echo Error: Go is not installed or not in PATH
    echo Please install Go 1.21 or later
    pause
    exit /b 1
)

echo Go environment detected
go version

REM 初始化Go模块
echo.
echo Initializing Go module...
if not exist go.mod (
    go mod init insertdbclient
)

REM 下载依赖
echo.
echo Downloading dependencies...
go mod tidy

REM 编译主程序
echo.
echo Building main program...
go build -trimpath -ldflags="-s -w" -o insertdbclient.exe main.go
if errorlevel 1 (
    echo Error: Failed to build main program
    pause
    exit /b 1
)
echo ✅ insertdbclient.exe built successfully

REM 编译验证工具
echo.
echo Building verification tool...
go build -trimpath -ldflags="-s -w" -o verify.exe verify.go
if errorlevel 1 (
    echo Error: Failed to build verification tool
    pause
    exit /b 1
)
echo ✅ verify.exe built successfully

REM 显示构建结果
echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Built files:
dir /b *.exe
echo.
echo Usage:
echo   insertdbclient.exe source_db target_db [options]
echo   verify.exe target_db
echo.
echo For detailed usage, see README.md
echo.
pause
