// 检查0x0800索引工具
package main

import (
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/filter"
	"github.com/syndtr/goleveldb/leveldb/opt"
)

type Check0x0800Config struct {
	DatabasePath string
}

type Check0x0800Tool struct {
	config *Check0x0800Config
	db     *leveldb.DB
	logger *log.Logger
}

func NewCheck0x0800Tool(config *Check0x0800Config) (*Check0x0800Tool, error) {
	logger := log.New(os.Stdout, "[Check0x0800] ", log.LstdFlags)

	options := &opt.Options{
		Filter:                 filter.NewBloomFilter(10),
		OpenFilesCacheCapacity: 1 << 8,
		BlockCacheCapacity:     32 * 1024 * 1024,
		WriteBuffer:            8 * 1024 * 1024,
		ReadOnly:               true,
		ErrorIfMissing:         false,
		Strict:                 opt.NoStrict,
	}

	db, err := leveldb.OpenFile(config.DatabasePath, options)
	if err != nil {
		logger.Printf("Failed to open normally, attempting recovery...")
		db, err = leveldb.RecoverFile(config.DatabasePath, options)
		if err != nil {
			return nil, fmt.Errorf("failed to open/recover database: %v", err)
		}
	}

	return &Check0x0800Tool{
		config: config,
		db:     db,
		logger: logger,
	}, nil
}

func (ct *Check0x0800Tool) Check() error {
	ct.logger.Println("=== Checking 0x0800 Index ===")

	prefix0x0800 := []byte{0x08, 0x00}
	iter := ct.db.NewIterator(nil, nil)
	defer iter.Release()

	count := 0
	for iter.Seek(prefix0x0800); iter.Valid(); iter.Next() {
		key := iter.Key()
		if len(key) < 2 || key[0] != 0x08 || key[1] != 0x00 {
			break
		}

		value := iter.Value()
		count++

		ct.logger.Printf("Entry %d:", count)
		ct.logger.Printf("  Key: %s", hex.EncodeToString(key))
		ct.logger.Printf("  Value: %s", hex.EncodeToString(value))

		// 解析WfsPathBean
		path, timestamp, err := ct.parseWfsPathBean(value)
		if err != nil {
			ct.logger.Printf("  Parse Error: %v", err)
		} else {
			ct.logger.Printf("  Path: %s", path)
			ct.logger.Printf("  Timestamp: %d", timestamp)
		}

		// 尝试提取seqID
		seqID := ct.extractSeqIDFromKey(key)
		ct.logger.Printf("  Extracted SeqID: %d", seqID)
		ct.logger.Println()
	}

	ct.logger.Printf("Total 0x0800 entries: %d", count)
	return nil
}

func (ct *Check0x0800Tool) parseWfsPathBean(data []byte) (string, int64, error) {
	var path string
	var timestamp int64

	i := 0
	for i < len(data) {
		tag, n := binary.Uvarint(data[i:])
		if n <= 0 {
			break
		}
		i += n

		fieldNum := tag >> 3
		wireType := tag & 0x7

		switch fieldNum {
		case 1: // Path字段
			if wireType == 2 {
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid path field")
				}
				i += n
				path = string(data[i : i+int(length)])
				i += int(length)
			}
		case 2: // Timestamp字段
			if wireType == 0 {
				ts, n := binary.Varint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid timestamp field")
				}
				timestamp = ts
				i += n
			}
		default:
			// 跳过未知字段
			switch wireType {
			case 0:
				_, n := binary.Uvarint(data[i:])
				if n <= 0 {
					return "", 0, fmt.Errorf("invalid varint field")
				}
				i += n
			case 2:
				length, n := binary.Uvarint(data[i:])
				if n <= 0 || i+n+int(length) > len(data) {
					return "", 0, fmt.Errorf("invalid length-delimited field")
				}
				i += n + int(length)
			default:
				return "", 0, fmt.Errorf("unsupported wire type: %d", wireType)
			}
		}
	}

	return path, timestamp, nil
}

func (ct *Check0x0800Tool) extractSeqIDFromKey(key []byte) int64 {
	if len(key) >= 10 {
		seqIDBytes := key[len(key)-8:]
		return int64(binary.BigEndian.Uint64(seqIDBytes))
	}
	return 0
}

func (ct *Check0x0800Tool) Close() {
	if ct.db != nil {
		ct.db.Close()
	}
}

func main() {
	config := &Check0x0800Config{}

	flag.StringVar(&config.DatabasePath, "db", "", "Database path (required)")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Check 0x0800 Index Tool\n\n")
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -db C:\\wfsdata\\wfsdb\n", os.Args[0])
	}

	flag.Parse()

	if config.DatabasePath == "" {
		fmt.Fprintf(os.Stderr, "Error: -db parameter is required\n")
		flag.Usage()
		os.Exit(1)
	}

	tool, err := NewCheck0x0800Tool(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	defer tool.Close()

	if err := tool.Check(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
