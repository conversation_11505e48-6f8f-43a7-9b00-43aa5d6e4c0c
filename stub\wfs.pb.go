// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0-devel
// 	protoc        v4.24.2
// source: wfs.proto

package stub

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WfsNodeBean struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rmsize *int64 `protobuf:"varint,1,opt,name=rmsize" json:"rmsize,omitempty"`
}

func (x *WfsNodeBean) Reset() {
	*x = WfsNodeBean{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wfs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WfsNodeBean) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WfsNodeBean) ProtoMessage() {}

func (x *WfsNodeBean) ProtoReflect() protoreflect.Message {
	mi := &file_wfs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WfsNodeBean.ProtoReflect.Descriptor instead.
func (*WfsNodeBean) Descriptor() ([]byte, []int) {
	return file_wfs_proto_rawDescGZIP(), []int{0}
}

func (x *WfsNodeBean) GetRmsize() int64 {
	if x != nil && x.Rmsize != nil {
		return *x.Rmsize
	}
	return 0
}

type WfsFileBean struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Storenode    *string `protobuf:"bytes,1,opt,name=storenode" json:"storenode,omitempty"`
	Offset       *int64  `protobuf:"varint,2,opt,name=offset" json:"offset,omitempty"`
	Size         *int64  `protobuf:"varint,3,opt,name=size" json:"size,omitempty"`
	CompressType *int32  `protobuf:"varint,4,opt,name=compressType" json:"compressType,omitempty"`
	Refercount   *int32  `protobuf:"varint,5,opt,name=refercount" json:"refercount,omitempty"`
}

func (x *WfsFileBean) Reset() {
	*x = WfsFileBean{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wfs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WfsFileBean) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WfsFileBean) ProtoMessage() {}

func (x *WfsFileBean) ProtoReflect() protoreflect.Message {
	mi := &file_wfs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WfsFileBean.ProtoReflect.Descriptor instead.
func (*WfsFileBean) Descriptor() ([]byte, []int) {
	return file_wfs_proto_rawDescGZIP(), []int{1}
}

func (x *WfsFileBean) GetStorenode() string {
	if x != nil && x.Storenode != nil {
		return *x.Storenode
	}
	return ""
}

func (x *WfsFileBean) GetOffset() int64 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

func (x *WfsFileBean) GetSize() int64 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *WfsFileBean) GetCompressType() int32 {
	if x != nil && x.CompressType != nil {
		return *x.CompressType
	}
	return 0
}

func (x *WfsFileBean) GetRefercount() int32 {
	if x != nil && x.Refercount != nil {
		return *x.Refercount
	}
	return 0
}

type WfsPathBean struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path       *string `protobuf:"bytes,1,opt,name=path" json:"path,omitempty"`
	Timestramp *int64  `protobuf:"varint,2,opt,name=timestramp" json:"timestramp,omitempty"`
}

func (x *WfsPathBean) Reset() {
	*x = WfsPathBean{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wfs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WfsPathBean) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WfsPathBean) ProtoMessage() {}

func (x *WfsPathBean) ProtoReflect() protoreflect.Message {
	mi := &file_wfs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WfsPathBean.ProtoReflect.Descriptor instead.
func (*WfsPathBean) Descriptor() ([]byte, []int) {
	return file_wfs_proto_rawDescGZIP(), []int{2}
}

func (x *WfsPathBean) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *WfsPathBean) GetTimestramp() int64 {
	if x != nil && x.Timestramp != nil {
		return *x.Timestramp
	}
	return 0
}

type SnapshotBean struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   []byte `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Value []byte `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
}

func (x *SnapshotBean) Reset() {
	*x = SnapshotBean{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wfs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotBean) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotBean) ProtoMessage() {}

func (x *SnapshotBean) ProtoReflect() protoreflect.Message {
	mi := &file_wfs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotBean.ProtoReflect.Descriptor instead.
func (*SnapshotBean) Descriptor() ([]byte, []int) {
	return file_wfs_proto_rawDescGZIP(), []int{3}
}

func (x *SnapshotBean) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *SnapshotBean) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

type SnapshotBeans struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    *int64          `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Beans []*SnapshotBean `protobuf:"bytes,2,rep,name=beans" json:"beans,omitempty"`
}

func (x *SnapshotBeans) Reset() {
	*x = SnapshotBeans{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wfs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotBeans) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotBeans) ProtoMessage() {}

func (x *SnapshotBeans) ProtoReflect() protoreflect.Message {
	mi := &file_wfs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotBeans.ProtoReflect.Descriptor instead.
func (*SnapshotBeans) Descriptor() ([]byte, []int) {
	return file_wfs_proto_rawDescGZIP(), []int{4}
}

func (x *SnapshotBeans) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *SnapshotBeans) GetBeans() []*SnapshotBean {
	if x != nil {
		return x.Beans
	}
	return nil
}

type SnapshotFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           *int64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Path         *string `protobuf:"bytes,2,opt,name=path" json:"path,omitempty"`
	Data         []byte  `protobuf:"bytes,3,opt,name=data" json:"data,omitempty"`
	CompressType *int32  `protobuf:"varint,4,opt,name=compressType" json:"compressType,omitempty"`
}

func (x *SnapshotFile) Reset() {
	*x = SnapshotFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wfs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotFile) ProtoMessage() {}

func (x *SnapshotFile) ProtoReflect() protoreflect.Message {
	mi := &file_wfs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotFile.ProtoReflect.Descriptor instead.
func (*SnapshotFile) Descriptor() ([]byte, []int) {
	return file_wfs_proto_rawDescGZIP(), []int{5}
}

func (x *SnapshotFile) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *SnapshotFile) GetPath() string {
	if x != nil && x.Path != nil {
		return *x.Path
	}
	return ""
}

func (x *SnapshotFile) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SnapshotFile) GetCompressType() int32 {
	if x != nil && x.CompressType != nil {
		return *x.CompressType
	}
	return 0
}

var File_wfs_proto protoreflect.FileDescriptor

var file_wfs_proto_rawDesc = []byte{
	0x0a, 0x09, 0x77, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x73, 0x74, 0x75,
	0x62, 0x22, 0x25, 0x0a, 0x0b, 0x57, 0x66, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x42, 0x65, 0x61, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x6d, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x72, 0x6d, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x0b, 0x57, 0x66, 0x73,
	0x46, 0x69, 0x6c, 0x65, 0x42, 0x65, 0x61, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x41, 0x0a, 0x0b, 0x57, 0x66, 0x73, 0x50, 0x61, 0x74,
	0x68, 0x42, 0x65, 0x61, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x72, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x72, 0x61, 0x6d, 0x70, 0x22, 0x36, 0x0a, 0x0c, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x42, 0x65, 0x61, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x49, 0x0a, 0x0d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x42, 0x65, 0x61,
	0x6e, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x28, 0x0a, 0x05, 0x62, 0x65, 0x61, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x73, 0x74, 0x75, 0x62, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x42, 0x65, 0x61, 0x6e, 0x52, 0x05, 0x62, 0x65, 0x61, 0x6e, 0x73, 0x22, 0x6a, 0x0a, 0x0c,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0x5a, 0x06, 0x2e, 0x2f, 0x73, 0x74,
	0x75, 0x62,
}

var (
	file_wfs_proto_rawDescOnce sync.Once
	file_wfs_proto_rawDescData = file_wfs_proto_rawDesc
)

func file_wfs_proto_rawDescGZIP() []byte {
	file_wfs_proto_rawDescOnce.Do(func() {
		file_wfs_proto_rawDescData = protoimpl.X.CompressGZIP(file_wfs_proto_rawDescData)
	})
	return file_wfs_proto_rawDescData
}

var file_wfs_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_wfs_proto_goTypes = []interface{}{
	(*WfsNodeBean)(nil),   // 0: stub.WfsNodeBean
	(*WfsFileBean)(nil),   // 1: stub.WfsFileBean
	(*WfsPathBean)(nil),   // 2: stub.WfsPathBean
	(*SnapshotBean)(nil),  // 3: stub.SnapshotBean
	(*SnapshotBeans)(nil), // 4: stub.SnapshotBeans
	(*SnapshotFile)(nil),  // 5: stub.SnapshotFile
}
var file_wfs_proto_depIdxs = []int32{
	3, // 0: stub.SnapshotBeans.beans:type_name -> stub.SnapshotBean
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_wfs_proto_init() }
func file_wfs_proto_init() {
	if File_wfs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_wfs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WfsNodeBean); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wfs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WfsFileBean); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wfs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WfsPathBean); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wfs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotBean); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wfs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotBeans); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_wfs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_wfs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wfs_proto_goTypes,
		DependencyIndexes: file_wfs_proto_depIdxs,
		MessageInfos:      file_wfs_proto_msgTypes,
	}.Build()
	File_wfs_proto = out.File
	file_wfs_proto_rawDesc = nil
	file_wfs_proto_goTypes = nil
	file_wfs_proto_depIdxs = nil
}
